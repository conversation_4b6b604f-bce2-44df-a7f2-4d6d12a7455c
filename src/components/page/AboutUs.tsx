import { useEffect, useRef, useState } from "react";
import Footer from "../Footer";
import { useTranslation } from "react-i18next";
import headerBgDark from "/src/assets/AboutUs/about-us-header-bg-dark.webp";
import headerBgLight from "/src/assets/AboutUs/about-us-header-bg-light.webp";
import { useAtom, useAtomValue } from "jotai";
import {
  featureSectionAtom,
  isAuthenticatedAtom,
  themeAtom,
} from "@/store/atoms";
import missionImageDark from "/src/assets/AboutUs/mission-image-dark.webp";
import missionImageLight from "/src/assets/AboutUs/mission-image-light.webp";
import payoutLogo from "/src/assets/Leaderboard/payout.png";
import conditionImage from "/src/assets/Leaderboard/condition-dark.png";
import conditionImageLight from "/src/assets/Leaderboard/condition-light.png";
import analyticsscreenlight from "/src/assets/Leaderboard/analyticsscreenlight.png";
import matchtraderLogoDark from "/src/assets/Leaderboard/matchtrader-logo-dark.png";
import metatraderLogoDark from "/src/assets/Leaderboard/metatrader-logo-dark.png";
import riskmanaIcon from "/src/assets/AboutUs/risk-mana-icon.svg";
import headphoneIcon from "/src/assets/AboutUs/head-icon.svg";
import ruleIcon from "/src/assets/AboutUs/rule-icon.svg";
import communityIcon from "/src/assets/AboutUs/community-icon.svg";
import promiseBglight from "/src/assets/AboutUs/promise-section-background-light.webp";
import LivePerformanceChart from "../charts/LivePerformanceChart";
import checkIconDark from "/src/assets/AboutUs/check-icon-dark.svg";
import checkIconLight from "/src/assets/AboutUs/check-icon-light.svg";
import { scrollToFeatureSectionFunction } from "@/utils/scrollToFeatureSectionFunction";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import GeneralButtonWithCss from "../landing/GeneralButtonWithCss";
// import FAQ from "./FAQ";
import FaqSection from "../landing/FaqSection";
import Panel from "../leaderboard/Panel";
// import ThemeToggleButton from "../themeToggle/ThemeToggleButton";

export default function AboutUs() {
  const navigate = useNavigate();
  const metaAccounts = useSelector(
    (state: { metaAccount: { accounts: any } }) => state.metaAccount.accounts
  );
  const [isAuthenticated] = useAtom(isAuthenticatedAtom);
  const [featureSectionRef] = useAtom(featureSectionAtom);
  const { t } = useTranslation();
  const sectionsRef = useRef<(HTMLElement | null)[]>([]);

  useEffect(() => {
    // const aboutUsSectionComponent = document.getElementById("aboutUsSection");
    // if (aboutUsSectionComponent) {
    //   aboutUsSectionComponent.scrollIntoView({ behavior: "smooth" });
    // }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("visible");
          }
        });
      },
      { threshold: 0.1, rootMargin: "0px" }
    );

    sectionsRef.current.forEach((section) => {
      if (section) observer.observe(section);
    });

    return () => observer.disconnect();
  }, []);

  const [headerBg, setHeaderBg] = useState<string>();
  const themeAtomValue = useAtomValue(themeAtom);
  const [missionImage, setMissionImage] = useState<string>();
  const [checkIcon, setCheckIcon] = useState<string>();
  useEffect(() => {
    setHeaderBg(themeAtomValue === "dark" ? headerBgDark : headerBgLight);
    setMissionImage(
      themeAtomValue === "dark" ? missionImageDark : missionImageLight
    );
    setCheckIcon(themeAtomValue === "dark" ? checkIconDark : checkIconLight);
  }, [themeAtomValue]);

  return (
    <div
      className="relative h-full w-full dark:bg-black dark:text-white bg-white text-black overflow-hidden"
      id="aboutUsSection"
    >
      {/* Header Section */}
      <div className="relative w-full h-[669px] overflow-hidden">
        <img
          src={headerBg}
          alt="HeaderBackground"
          className="w-full h-full object-cover"
        />
        <div className="absolute flex justify-center items-center w-full h-full inset-0">
          <div className="relative flex w-full h-[60%] flex-col items-center ">
            <span className="text-[48px] font-bold top-[100px]">
              {t("About Us")}
            </span>
            <div className="lg:w-[40%] w-[90%] text-center">
              {t(
                "Choose from flexible plans designed for traders at every level."
              )}{" "}
              {t(
                "Transparent pricing with no hidden fees—start trading with confidence today."
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mission Section */}
      <div className="flex flex-col lg:flex-row gap-10 w-full h-auto items-center justify-center lg:p-12 pt-20 space-x-3 overflow-hidden">
        <div className="lg:w-[35%] w-[90%] flex flex-col space-y-1">
          <span className="font-bold text-3xl md:text-[48px]">
            {t("Our Mission")}
          </span>
          <span className="text-sm lg:text-[18px] dark:text-[#838383] text-[#7A7A7A]">
            {t(
              "At URFX, we are dedicated to revolutionizing the trading experience by providing innovative, secure, and user-centric solutions. Our mission is to empower traders of all levels by offering cutting-edge tools, comprehensive education, and unparalleled support. We strive to create a transparent and efficient trading environment that fosters growth, learning, and success. Through continuous innovation and a commitment to excellence, we aim to be the trusted partner for traders worldwide, helping them navigate the complexities of the financial markets with confidence and ease."
            )}
          </span>
        </div>

        <div className="w-fit pr-3">
          <img src={missionImage} alt="MissionImage" className="" />
        </div>
      </div>

      {/* Trade with the Most Reliable Prop Firm Section */}
      <div className="w-full z-0">
        <div className="text-center mt-8 mb-0 z-0">
          <h2 className="text-[32px] md:text-[48px] 2k:text-[72px] 2k:leading-[90px] leading-9 md:leading-normal font-bold mb-4">
            {t("Trade with the Most")} <br />
            {t("Reliable Prop Firm")}
          </h2>
          <p className="text-[14px] sm:text-[18px] 2k:text-[27px] 2k:leading-[36px] tracking-[-0.0001em] text-gray-400 mx-auto">
            {t("Reliable and swift, our service ensures precision and")} <br />
            {t("stability, building trust with every interaction.")}
          </p>
        </div>

        <div className="flex justify-start pb-[40px] pt-[40px] px-4 md:justify-center w-full space-x-4 sm:space-x-8 2k:space-x-11 overflow-x-scroll overflow-y-hidden scrollbar-hide">
          <Panel
            className="min-w-[298px] min-h-[279px] md:min-w-[397px] md:min-h-[392px] md:w-[397px] md:h-[392px] 2k:w-[520px] 2k:h-[520px] dark:bg-[#1C1E1C] dark:bg-none"
            classNameContent="flex items-start justify-end"
          >
            <img
              src={payoutLogo}
              alt="payout"
              className="size-[250px] md:size-[300px] 2k:size-[410px]"
            />

            <div className="absolute flex flex-col items-start justify-end inset-0 p-2 sm:p-5 2k:p-8 2k:font-normal tracking-[-0.0001em]">
              <p className="font-semibold text-start text-[18px] sm:text-[24px] 2k:text-[38px] leading-5 sm:leading-7 2k:leading-[45px] mb-2">
                {t("Guaranteed")} <br /> {t("Payouts")}
              </p>
              <span className="text-start text-[14px] sm:text-[18px] 2k:text-[22px] 2k:tracking-normal font-normal opacity-50">
                {t(
                  "Receive your payment within 24 hours, or we'll add an extra $1,000 to your earnings!"
                )}
              </span>
            </div>
          </Panel>

          <Panel
            className="min-w-[298px] min-h-[279px] md:min-w-[397px] md:min-h-[392px] md:w-[397px] md:h-[392px] 2k:w-[520px] 2k:h-[520px] dark:bg-[#1C1E1C]"
            classNameContent="flex items-start justify-end"
          >
            <img
              src={
                themeAtomValue === "dark" ? conditionImage : conditionImageLight
              }
              alt="ConditionImage"
              className="size-[250px] md:size-[300px] 2k:size-[340px]"
            />

            <div className="absolute flex flex-col items-start justify-end inset-0 p-2 sm:p-5 2k:p-8 2k:font-normal tracking-[-0.0001em]">
              <p className="font-semibold text-start text-[18px] sm:text-[24px] 2k:text-[38px] leading-5 sm:leading-7 2k:leading-[45px] mb-2">
                {t("Best Trading")} <br /> {t("Conditions")}
              </p>
              <span className="text-start text-[14px] sm:text-[18px] 2k:text-[22px] 2k:tracking-normal font-normal opacity-50">
                {t(
                  "Transforming trading journeys globally through industry-leading resources."
                )}
              </span>
            </div>
          </Panel>

          <Panel
            className="min-w-[298px] min-h-[279px] md:min-w-[397px] md:min-h-[392px] md:w-[397px] md:h-[392px] 2k:w-[520px] 2k:h-[520px] dark:bg-[#1C1E1C]"
            classNameContent="flex items-start justify-end"
          >
            <div className="flex flex-col mt-1 ml-3 sm:mt-3 sm:ml-3 items-start">
              <img
                src={metatraderLogoDark}
                alt="metatraderLogoDark"
                className="w-[97px] h-[77.1px] md:w-[117px] md:h-[93px] 2k:w-[162px] 2k:h-[135px]"
              />
              <img
                src={matchtraderLogoDark}
                alt="matchtraderLogoDark"
                className="size-[93px] md:size-[131px] 2k:size-[181px]"
              />
            </div>
            <img
              src={analyticsscreenlight}
              alt="analyticsscreen"
              className="sm:size-[280px] size-[200px] 2k:size-[400px]"
            />

            <div className="absolute flex flex-col items-start justify-end inset-0 p-2 sm:p-5 2k:p-8 2k:font-normal tracking-[-0.0001em]">
              <p className="font-semibold text-start text-[18px] sm:text-[24px] 2k:text-[38px] leading-5 sm:leading-7 2k:leading-[45px] mb-2">
                {t("Best Trading")} <br /> {t("Platforms")}
              </p>
              <span className="text-start text-[14px] sm:text-[18px] 2k:text-[22px] 2k:tracking-normal font-normal opacity-50">
                {t(
                  "Our MQ licenses and tech boost experience, security, and efficiency."
                )}
              </span>
            </div>
          </Panel>
        </div>
      </div>

      {/* What Makes Us different Section */}
      <div className="flex flex-col items-center justify-center w-full text-center mt-10">
        <span className="text-3xl lg:text-[48px] font-bold leading-[1]">
          {t("What Makes Us Different?")}
        </span>
        <span className="dark:text-[#FFFFFF] dark:opacity-50 text-dark w-[80%] xl:w-[40%] text-sm md:text-base mt-5">
          {t(
            "We offer a user-friendly platform with advanced tools, real-time data, and robust security,"
          )}{" "}
          {t(
            "empowering traders at all levels to navigate the markets confidently."
          )}
        </span>
      </div>

      <div className="w-full h-auto grid lg:grid-cols-2 gap-3 p-10 md:px-40">
        <div className="flex flex-col justify-evenly p-7  h-[318px] bg-gradient-to-r from-[#D2E9DC] via-[#DAECD8] to-[#E8ECDB] dark:bg-gradient-to-r dark:from-[#1C1D15] dark:via-[#1E2116] dark:to-[#232F1C] border border-[#C3BE25] gradient-border">
          <img
            src={riskmanaIcon}
            alt="RiskManagementIcon"
            className="w-[50px] h-[50px]"
          />
          <p className="font-bold text-xl md:text-[28px]">
            {t("Tech-Driven Risk Management")}
          </p>
          <span className="text-sm md:text-[20px]">
            {t(
              "Risk Management: We use proprietary systems to monitor and support funded traders — optimizing risk, tracking performance, and helping you scale faster."
            )}
          </span>
        </div>

        <div className="flex flex-col justify-evenly p-7  h-[318px] bg-gradient-to-r from-[#D2E9DC] via-[#DAECD8] to-[#E8ECDB] dark:bg-gradient-to-r dark:from-[#212D1B] dark:via-[#19211C] dark:to-[#232F1C] border border-[#C3BE25] gradient-border">
          <img
            src={headphoneIcon}
            alt="HeadPhoneIcon"
            className="w-[50px] h-[50px]"
          />
          <p className="font-bold text-xl md:text-[28px]">
            {t("Real Support")}
          </p>
          <span className="text-sm md:text-[20px]">
            {t(
              "Real Fast: Live chat, Telegram, Discord — our team is always around when you need us. No generic replies. No ticket black holes."
            )}
          </span>
        </div>

        <div className="flex flex-col justify-evenly p-7  h-[318px] bg-gradient-to-r from-[#D2E9DC] via-[#DAECD8] to-[#E8ECDB] dark:bg-gradient-to-r dark:from-[#212D1B] dark:via-[#19211C] dark:to-[#232F1C]  border border-[#C3BE25] gradient-border">
          <img src={ruleIcon} alt="RuleIcon" className="w-[50px] h-[50px]" />
          <p className="font-bold text-xl md:text-[28px]">
            {t("Fair & Transparent Rules")}
          </p>
          <span className="text-sm md:text-[20px]">
            {t(
              "We built our model to support traders, not trap them. Our challenges are designed to be achievable, with real profit potential and growth opportunities."
            )}
          </span>
        </div>

        <div className="flex flex-col justify-evenly p-7  h-[318px] bg-gradient-to-r from-[#D2E9DC] via-[#DAECD8] to-[#E8ECDB] dark:bg-gradient-to-r dark:from-[#212D1B] dark:via-[#19211C] dark:to-[#232F1C]  border border-[#C3BE25] gradient-border">
          <img
            src={communityIcon}
            alt="RuleIcon"
            className="w-[50px] h-[50px]"
          />
          <p className="font-bold text-xl md:text-[28px]">
            {t("Community of Elite Traders")}
          </p>
          <span className="text-sm md:text-[20px]">
            {t(
              "Join hundreds of traders leveling up together through live events, Discord sessions, exclusive coaching drops, real-time market analysis, trade alerts, strategy workshops, and more."
            )}
          </span>
        </div>
      </div>

      {/* Promist Section */}
      <div className="relative w-full lg:h-[610px] overflow-hidden h-[800px]">
        <img
          src={promiseBglight}
          alt="PromistBackgroundImage"
          className="w-full h-full dark:opacity-30"
        />

        <div className="absolute inset-0 flex flex-col lg:flex-row">
          <div className="lg:w-1/2 flex justify-end items-center p-10">
            <div className="flex flex-col space-y-5">
              <p className="font-bold text-3xl md:text-[48px] lg:w-[70%] leading-[1]">
                {t("Our Promise to Every Trader")}
              </p>
              <div className="flex gap-2">
                <img src={checkIcon} alt="CheckIcon" />
                {t("Real-Time Performance Tracking")}
              </div>
              <div className="flex gap-2">
                <img src={checkIcon} alt="CheckIcon" />
                {t("No hidden rules")}
              </div>
              <div className="flex gap-2">
                <img src={checkIcon} alt="CheckIcon" />
                {t("No delays on payouts")}
              </div>
              <div className="flex gap-2">
                <img src={checkIcon} alt="CheckIcon" />
                {t("No BS—just real support")}
              </div>
              <div className="w-full mt-10">
                <GeneralButtonWithCss
                  onClick={() =>
                    scrollToFeatureSectionFunction(
                      metaAccounts,
                      isAuthenticated,
                      navigate,
                      featureSectionRef
                    )
                  }
                  blur={true}
                  className="w-[150px] h-10"
                  bgClassName="dark:bg-dark dark:opacity-30 dark:bg-none bg-gradient-to-r from-[#7DDEE9] via-[#BBE0A5] to-[#E4E389]"
                >
                  {t("Start Challenge")}
                </GeneralButtonWithCss>
              </div>
            </div>
          </div>

          <div className="flex justify-center items-center lg:w-1/2 px-4">
            <LivePerformanceChart chartOnly={true} />
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="mt-10 lg:mt-20">
        <FaqSection />
      </div>

      <Footer />
    </div>
  );
}
