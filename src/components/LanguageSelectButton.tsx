import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import engFlag from "/src/assets/LanguageButton/uk.svg";
import frFlag from "/src/assets/LanguageButton/fr.svg";
import geFlag from "/src/assets/LanguageButton/ge.svg";
import spFlag from "/src/assets/LanguageButton/spanish.svg";
import globeIcon from "/src/assets/LanguageButton/global-icon-dark.svg";

const LanguageSelectButton = () => {
  const { i18n } = useTranslation();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const [, setDropdownDirection] = useState<"down" | "up">("down");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [dropdownStyle, setDropdownStyle] = useState<React.CSSProperties>({});

  const options = [
    { value: "en", label: "En", displayCode: "ENG", fullName: "English", flag: engFlag },
    { value: "fr", label: "Fr", displayCode: "FRA", fullName: "Français", flag: frFlag },
    { value: "ge", label: "Ge", displayCode: "DEU", fullName: "Deutsch", flag: geFlag },
    { value: "sp", label: "Sp", displayCode: "ESP", fullName: "Español", flag: spFlag },
  ];

  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleOptionClick = (value: string) => {
    setSelectedLanguage(value);
    i18n.changeLanguage(value);
    setIsDropdownOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isDropdownOpen) {
      setIsDropdownVisible(true);
    } else {
      // Wait for transition before unmounting
      const timeout = setTimeout(() => setIsDropdownVisible(false), 200);
      return () => clearTimeout(timeout);
    }
  }, [isDropdownOpen]);

  useEffect(() => {
    if (isDropdownOpen && dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      const dropdownHeight = 180; // Approximate, adjust as needed
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownDirection("up");
      } else {
        setDropdownDirection("down");
      }
    }
  }, [isDropdownOpen]);

  useEffect(() => {
    if (isDropdownOpen && dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      const dropdownHeight = 180;
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      let direction: "down" | "up" = "down";
      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        direction = "up";
        setDropdownDirection("up");
      } else {
        direction = "down";
        setDropdownDirection("down");
      }
      // Calculate left/top/bottom for fixed dropdown
      setDropdownStyle({
        position: "fixed",
        left: rect.left,
        width: rect.width,
        maxWidth: "240px",
        minWidth: "140px",
        zIndex: 50,
        ...(direction === "down"
          ? { top: rect.bottom + 4 }
          : { bottom: window.innerHeight - rect.top + 4 }),
        // Removed maxHeight and overflowY to ensure dropdown is always fully visible
      });
    }
  }, [isDropdownOpen]);

  return (
    <div
      ref={dropdownRef}
      className="relative flex justify-center items-center text-center h-full "
    >
      <button
        id="dropdownDefaultButton"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center justify-center gap-1 px-3 py-1.5  hover:bg-gray-700 rounded-full text-white text-sm font-medium transition-colors duration-200 min-w-[70px] h-[32px]"
        type="button"
      >
        <img src={globeIcon} alt="globe" className="w-4 h-4" />
        <span className="text-white">
          {options.find((option) => option.value === selectedLanguage)?.displayCode || "ENG"}
        </span>
        <svg
          className={`w-3 h-3 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isDropdownVisible && (
        <div
          id="dropdown"
          className={`bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden
            transition-all duration-200 ease-in-out
            ${
              isDropdownOpen
                ? "opacity-100 scale-y-100 pointer-events-auto"
                : "opacity-0 scale-y-95 pointer-events-none"
            }
            origin-top min-w-[120px]
          `}
          style={dropdownStyle}
        >
          <ul className="py-1">
            {options.map((option) => (
              <li key={option.value}>
                <button
                  onClick={() => handleOptionClick(option.value)}
                  className="flex items-center gap-3 w-full px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors duration-200"
                >
                  <img
                    src={option.flag}
                    alt={option.label}
                    className="w-5 h-5 rounded-sm"
                  />
                  <span>{option.fullName}</span>
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default LanguageSelectButton;
