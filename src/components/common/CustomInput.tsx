"use client";

import React, { useState } from "react";

export default function CustomInput(props: React.InputHTMLAttributes<HTMLInputElement>) {
  const [focused, setFocused] = useState(false);

  return (
    <div className="relative rounded-md">
      {/* Border effect, only visible when focused */}
      {focused && (
        <div className="absolute inset-0 pointer-events-none z-10">
          {/* Left border */}
          <div className="absolute left-0 top-0 h-full w-px bg-skyBright"></div>
          {/* Right border */}
          <div className="absolute right-0 top-0 h-full w-px bg-teal"></div>
          {/* Top partial borders */}
          <div className="absolute top-0 left-0 w-[12%] h-px bg-skyBright"></div>
          <div className="absolute top-0 right-0 w-[12%] h-px bg-teal"></div>
          {/* Bottom partial borders */}
          <div className="absolute bottom-0 left-0 w-[12%] h-px bg-skyBright"></div>
          <div className="absolute bottom-0 right-0 w-[12%] h-px bg-teal"></div>
        </div>
      )}
      <input
        type="text"
        placeholder="Enter your full name"
        className={`
          w-full h-14 px-4 bg-transparent text-white placeholder:text-[#01010199] dark:placeholder:text-[#ffffff99]
          outline-none border-none ${focused? "dark:!bg-primary-gradient !bg-black/20": "dark:!bg-dark-gradient !bg-black/10"}
          transition-all font-creato font-medium text-lg
        `}
        onFocus={() => setFocused(true)}
        onBlur={() => setFocused(false)}
        {...props}
      />
    </div>
  );
}