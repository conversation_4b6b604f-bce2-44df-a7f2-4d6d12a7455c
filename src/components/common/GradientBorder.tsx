export default function GradientBorder() {
    return (
      <div className="absolute inset-0 pointer-events-none z-10">
        {/* Left border */}
        <div className="absolute left-0 top-0 h-full w-px bg-skyBright"></div>
        {/* Right border */}
        <div className="absolute right-0 top-0 h-full w-px bg-teal"></div>
        {/* Top partial borders */}
        <div className="absolute top-0 left-0 w-[12%] h-px bg-skyBright"></div>
        <div className="absolute top-0 right-0 w-[12%] h-px bg-teal"></div>
        {/* Bottom partial borders */}
        <div className="absolute bottom-0 left-0 w-[12%] h-px bg-skyBright"></div>
        <div className="absolute bottom-0 right-0 w-[12%] h-px bg-teal"></div>
      </div>
    );
  }