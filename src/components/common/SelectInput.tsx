import { useState } from "react";

interface Option {
  value: string;
  label: string;
}

interface SelectInputProps {
  className?: string;
  variant?: "default" | "dark-gradient";
  defaultOption?: string;
  options: Option[];
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  name?: string;
  id?: string;
}

export default function SelectInput({
  className,
  variant = "default",
  defaultOption,
  options,
  value,
  onChange,
  name,
  id,
  ...props
}: SelectInputProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <select
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        onFocus={() => setIsOpen(true)}
        onBlur={() => setIsOpen(false)}
        className={`
          w-full h-14 px-4 bg-transparent text-white placeholder:text-[#01010199] dark:placeholder:text-[#ffffff99]
          outline-none border-none ${isOpen? "dark:!bg-primary-gradient !bg-black/20": "dark:!bg-dark-gradient !bg-black/10"}
          transition-all font-creato font-medium text-lg
         ${isOpen
            ? "dark:bg-primary-gradient bg-black/20"
            : "dark:bg-dark-gradient bg-black/10"
        } ${className || ""}`}
        {...props}
      >
        {defaultOption && (
          <option value="" className="text-black">
            {defaultOption}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value} className="text-black">
            {option.label}
          </option>
        ))}
      </select>

      {/* Custom borders - only show when selected */}
      {isOpen && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Left border */}
          <div className="absolute left-0 top-0 h-full w-px bg-skyBright"></div>
          {/* Right border */}
          <div className="absolute right-0 top-0 h-full w-px bg-teal"></div>
          {/* Top partial borders */}
          <div className="absolute top-0 left-0 w-[12%] h-px bg-skyBright"></div>
          <div className="absolute top-0 right-0 w-[12%] h-px bg-teal"></div>
          {/* Bottom partial borders */}
          <div className="absolute bottom-0 left-0 w-[12%] h-px bg-skyBright"></div>
          <div className="absolute bottom-0 right-0 w-[12%] h-px bg-teal"></div>
        </div>
      )}
    </div>
  );
}
