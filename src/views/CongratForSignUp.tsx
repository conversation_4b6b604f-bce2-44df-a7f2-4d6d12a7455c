import emailVerifySuc<PERSON>Logo from "../../src/assets/email-verify-success-imgae.png";
import { env } from "@/config/env";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import { useSet<PERSON>tom } from "jotai";
import { signupEmail<PERSON>tom } from "@/store/atoms";
import GeneralButtonWithCss from "@/components/landing/GeneralButtonWithCss";
import { useTranslation } from "react-i18next";
import Logo from "@/components/common/Logo";

const CongratForSignup: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const setSignupEmail = useSetAtom(signupEmailAtom);
  const params = new URLSearchParams(window.location.search);
  const token = params.get("token") || null; // Get the token from the URL
  console.log("token ===>", token);

  useEffect(() => {
    if (token === null) {
      navigate("/register");
    }
    try {
            axios.post(`${env.BASE_URL}/auth/register-verified-email`, {
              token: token,
            });
            console.log("Token sent to the backend successfully!");
    
            // navigate("/login"); // Redirect to the login page after successful registration
          } catch (error) {
            console.error("Error during email transfer: ", error);
            if (axios.isAxiosError(error) && error.response) {
              console.error(
                "Server responded with ===>: ",
                error.response.data.message
              );
            } else {
              console.error("An unexpected error occurred:", error);
            }
          }
  });

  const handleNavigate=()=>{
    // Clear the signup email state when user finishes verification flow
    setSignupEmail("");
    navigate("/login");
  }

  // const handleRegister = async () => {
  //   console.log("token ===>", token);

  //   if (token) {
  //     try {
  //       axios.post(`${env.BASE_URL}/auth/register-verified-email`, {
  //         token: token,
  //       });
  //       console.log("Token sent to the backend successfully!");

  //       navigate("/login"); // Redirect to the login page after successful registration
  //     } catch (error) {
  //       console.error("Error during email transfer: ", error);
  //       if (axios.isAxiosError(error) && error.response) {
  //         console.error(
  //           "Server responded with ===>: ",
  //           error.response.data.message
  //         );
  //       } else {
  //         console.error("An unexpected error occurred:", error);
  //       }
  //     }
  //   } else {
  //     console.error("Token is null or undefined");
  //   }
  // };

  useEffect(() => {
    // Ensure dark mode class is set on <html> if user previously selected dark mode
    const theme = localStorage.getItem("theme");
    const root = window.document.documentElement;
    if (theme === "dark") {
      root.classList.add("dark");
    } else {
      root.classList.remove("dark");
    }
  }, []);

  return (
    <div className="relative w-full min-h-screen dark:bg-[#070707] bg-white lg:gap-0 gap-4">

      {/* logo */}
      <div className="relative left-8 top-5 w-1/2">
        <Logo />
      </div>

      <div className="fixed inset-0 w-full h-screen flex items-center justify-center">
        <div className="relative flex flex-col space-y-5">
          <div className="w-full flex justify-center">
            <img
              src={emailVerifySuccessLogo}
              alt="emailVerifyLogo"
              className="w-[140px] h-[140px]"
            />
          </div>
          <div className="w-full text-center text-[35px] font-bold text-black dark:text-white">
            Congratulations! Your account is verified.
          </div>

          <div className="w-full flex justify-center">
          <GeneralButtonWithCss
                    onClick={handleNavigate}
                    className="w-[220px] 2k:h-[60px] 2k:text-3xl text-black dark:text-white font-medium cursor-pointer"
                    bgClassName="dark:bg-[#1A1A1A] dark:bg-none bg-gradient-to-r from-[#7DDEE9] via-[#BBE0A5] to-[#E4E389]"
                  >
                    {t("Continue to Login")}
                  </GeneralButtonWithCss>
           
          </div>
        </div>
      </div>
    </div>
  );
};

export default CongratForSignup;
