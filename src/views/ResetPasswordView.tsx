import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "react-toastify";
import { RotatingLines } from "react-loader-spinner";
import axios from "axios";
import { useAtomValue } from "jotai";
import { env } from "@/config/env";
import { themeAtom } from "@/store/atoms";
import { useTranslation } from "react-i18next";
import logoDark from "/src/assets/Navbar/dark-logo.png";
import logoLight from "/src/assets/Navbar/light-logo.png";
import ThemeToggleButton from "@/components/themeToggle/ThemeToggleButton";
import lockIconDark from "/src/assets/SigninView/lock-icon-dark.svg";
import lockIconLight from "/src/assets/SigninView/lock-icon-light.svg";
import { useRef } from "react";
import quoteIconDark from "/src/assets/SigninView/quote-icon-dark.svg";
import quoteIconLight from "/src/assets/SigninView/quote-icon-light.svg";
import avatar from "/src/assets/SigninView/avatar.svg";
import GeneralButtonWithCss from "@/components/landing/GeneralButtonWithCss";
import laptop from "/src/assets/SigninView/laptop.webp";
import VectorLogo from "/src/assets/SigninView/logo-vector.webp";
import LanguageSelectButton from "@/components/LanguageSelectButton";
import Label from "@/components/common/Label";

const ResetPasswordView: React.FC = () => {
  const navbarRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const query = new URLSearchParams(useLocation().search);
  const token = query.get('token');
  const navigate = useNavigate();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const handleLogin = () => {
    navigate("/login");
  };

  const handleResetPassword = async () => {
    if (loading) return;
    if (!token) {
      toast.error("Invalid token");
      return;
    }
    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }
    if (password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }
    try {
      setLoading(true);
      const response = await axios.post(
        `${env.BASE_URL}/auth/reset-password`,
        {
          password,
          token
        }
      );
      toast.success(response.data.message);
      navigate("/login");
    } catch (error) {
      console.error("Error during authentication: ", error);
      if (axios.isAxiosError(error) && error.response) {
        const { status, data } = error.response;
        switch (status) {
          case 400:
            toast.warn(data.message || "Invalid request. Please check your input");
            break;
          case 401:
            toast.warn(data.message || "Invalid or expired token. Please try again.");
            break;
          case 403:
            toast.warn(data.message || "You don't have permission to access this resource.");
            break;
          case 404:
            toast.warn(data.message || "The requested resource was not found.");
            break;
          case 500:
            toast.error(data.message || "Server error. Please try again later.");
            break;
          default:
            toast.warn(data.message || "An unexpected error occurred. Please try again.");
        }
      } else {
        toast.error("No response from the server. Please check your network connection.");
      }
    } finally {
      setLoading(false);
    }
  };

  const [logo, setLogo] = useState<string>();
  const themeAtomValue = useAtomValue(themeAtom);
  const [quoteIcon, setQuoteIcon] = useState<string>();
  
  useEffect(() => {
    if (navbarRef.current) {
      const height = navbarRef.current.offsetHeight;
      setNavbarHeight(height);
    }
  }, []);

  const [navbarHeight, setNavbarHeight] = useState<number>(0);
  const [lockIcon, setLockIcon] = useState<string>();
  
  useEffect(() => {
    setLogo(themeAtomValue === "dark" ? logoDark : logoLight);
    setLockIcon(themeAtomValue === "dark" ? lockIconDark : lockIconLight);
    setQuoteIcon(themeAtomValue === "dark" ? quoteIconDark : quoteIconLight);
  }, [themeAtomValue]);

  return (
    <div className="relative w-full h-screen flex justify-center items-center overflow-hidden dark:bg-dark dark:text-white bg-white text-dark lg:gap-0">
      {/* Mobile-specific background fix - ensures solid background on mobile */}
      <div className="absolute inset-0 z-[-1] dark:bg-black bg-white" />

      {/* Mobile Gradient Effects - only show on mobile */}
      <div className="absolute inset-0 z-0 pointer-events-none bg-soft-radial-green-mobile opacity-0 dark:opacity-70 md:hidden" />
      <div className="absolute inset-0 z-0 pointer-events-none bg-soft-radial-blue-mobile opacity-0 dark:opacity-60 md:hidden" />

      {/* Web Gradient Effects - only show on desktop (original gradients) */}
      <div className="absolute inset-0 z-0 pointer-events-none bg-soft-radial-green opacity-0 dark:opacity-70 hidden md:block" />
      <div className="absolute inset-0 z-0 pointer-events-none bg-soft-radial-blue opacity-0 dark:opacity-60 hidden md:block" />

      <div className="absolute w-full h-full inset-0 flex flex-row items-center justify-center">
        {/* left half section */}
        <div className="relative w-full lg:w-1/2 h-full">
          {/* navbar section */}
          <div
            className="flex justify-between w-full top-0 p-3 sm:p-9"
            ref={navbarRef}
          >
            <img src={logo} alt="Logo" className="w-[161px] h-[49px]" />

            <div className="flex items-center space-x-5">
              {/* Theme toggle, optional */}
              <span className="hidden sm:block"><ThemeToggleButton /></span>
              {/* Only show this text on xl and up */}
              <span className="hidden xl:flex whitespace-nowrap overflow-hidden text-ellipsis line-clamp-1">
                {t("Remember your password?")}
              </span>
              {/* Login button: always visible */}
              <GeneralButtonWithCss
                onClick={handleLogin}
                className="w-[73px] h-[32px] text-[16px]"
              >
                <span className="dark:opacity-50 opacity-100">{t("Login")}</span>
              </GeneralButtonWithCss>
            </div>
          </div>

          {/* Form Section */}
          <div
            className="w-full flex flex-col items-center justify-around top-0"
            style={{ height: `calc(100vh - ${navbarHeight}px)` }}
          >
            <form
              onSubmit={handleSubmit}
              className="p-6 rounded-2xl h-fit mb-[50px]"
            >
              <div className="mb-10 mt-6">
                <h1 className="text-[32px] sm:text-[40px] w-full text-center">
                  {t("Reset Your Password")}
                </h1>
                <p className="opacity-[60%] font-thin tracking-wider text-center">
                  {t("Enter your new password below")}
                </p>
              </div>

              <div className="mb-4">  
                <Label text={t("New Password")} compulsory />
                <div className="relative">
                  <img
                    src={lockIcon}
                    alt="LockIcon"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5"
                  />
                  <input
                    type="password"
                    id="password"
                    placeholder={t("• • • • • • • • • • ")}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="h-[52px] opacity-[60%] rounded-[10px] dark:bg-bgInput dark:placeholder-[#FFFFFF] placeholder-[#676767] text-sm mt-1 block w-full p-3 sm:p-2 pl-10 sm:pl-10 border border-gray-700 dark:bg-[#302F19] focus:outline focus:outline-gray-500 focus:border-none"
                    required
                  />
                </div>
              </div>

              <div className="mb-4">
                <Label text={t("Confirm Password")} compulsory />
                <div className="relative">
                  <img
                    src={lockIcon}
                    alt="LockIcon"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5"
                  />
                  <input
                    type="password"
                    id="confirmPassword"
                    placeholder={t("• • • • • • • • • • ")}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="h-[52px] opacity-[60%] rounded-[10px] dark:bg-bgInput dark:placeholder-[#FFFFFF] placeholder-[#676767] text-sm mt-1 block w-full p-3 sm:p-2 pl-10 sm:pl-10 border border-gray-700 dark:bg-[#302F19] focus:outline focus:outline-gray-500 focus:border-none"
                    required
                  />
                </div>
              </div>

              <div className="w-full flex justify-start h-auto mt-8">
                {themeAtomValue === "dark" ? (
                  <GeneralButtonWithCss
                    onClick={() => handleResetPassword()}
                    blur={true}
                    className="text-[14px] sm:text-[18px] h-[37px] w-full sm:h-[42px] dark:bg-gradient-to-r dark:from-[#12555A] dark:via-[#446035] dark:to-[#5C5F1B]"
                    enableBackgroundAtTheBeginning={false}
                    enableHoverEffect={true}
                  >
                    {loading ? (
                      <RotatingLines
                        width={"28"}
                        strokeWidth="5"
                        strokeColor="white"
                        animationDuration="0.75"
                        ariaLabel="rotating-lines-loading"
                      />
                    ) : (
                      <span className="tracking-wide">{t("Reset Password")}</span>
                    )}
                  </GeneralButtonWithCss>
                ) : (
                  <button
                    onClick={() => handleResetPassword()}
                    className="w-full py-3 text-center font-bold rounded bg-gradient-to-r from-cyan-400 to-yellow-300 text-black"
                  >
                    {loading ? (
                      <RotatingLines
                        width={"28"}
                        strokeWidth="5"
                        strokeColor="black"
                        animationDuration="0.75"
                        ariaLabel="rotating-lines-loading"
                      />
                    ) : (
                      <span className="tracking-wide">{t("Reset Password")}</span>
                    )}
                  </button>
                )}
              </div>
            </form>
            <div className="absolute bottom-2 flex justify-between w-full h-fit px-2">
              <div className="flex items-center"><span>© 2025 URFX</span></div>
              <LanguageSelectButton />
            </div>
          </div>
        </div>

        {/* Half Background Image Section */}
        <div className="hidden lg:block p-[2px] bg-gradient-to-r from-[#1CCDE5] via-[#9DD373] to-[#DBD633] w-[57%] h-[99%] rounded-xl mr-1">
          <div className="relative bg-gradient-to-tr dark:from-[#1d5e57] dark:via-[#425533] dark:to-[#5e5d1f] w-full h-full rounded-xl">
            <img
              src={laptop}
              alt="laptop"
              className="absolute h-[66%] right-0 bottom-0 rounded-xl"
            />

            <img
              src={VectorLogo}
              alt="VectorLogo"
              className="absolute right-0"
            />

            <img
              src={VectorLogo}
              alt="VectorLogo"
              className="absolute right-0"
            />
            <div className="absolute inset-0 p-10 pt-20">
              <div>
                <img src={quoteIcon} alt="QuoteIcon" />
              </div>
              <p className="text-[20px]">
                Secure your account with a strong new password. Your data security 
                is our priority. Create a password that's unique and memorable to 
                keep your trading account safe with URFX.
              </p>

              <div className="flex items-center justify-start mt-10 gap-5">
                <div>
                  <img src={avatar} alt="Avatar" />
                </div>
                <div>Security Team</div>
                <div>URFX</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPasswordView;
