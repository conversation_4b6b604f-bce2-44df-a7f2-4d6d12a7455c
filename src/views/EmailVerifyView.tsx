import emailVerifyLogo from "../../src/assets/email-verify-image.png";
import { useState } from "react";
import { toast } from "react-toastify";
import { userAtom, signupEmailAtom } from "@/store/atoms";
import { useAtom } from "jotai";
import axios from "axios";
import { env } from "@/config/env";
import GeneralButtonWithCss from "@/components/landing/GeneralButtonWithCss";
import Logo from "@/components/common/Logo";
import { useTranslation } from "react-i18next";

const EmailVerifyView: React.FC = () => {
  const { t } = useTranslation();
  const [userInfoGlobal] = useAtom(userAtom);
  const [signupEmail] = useAtom(signupEmailAtom);
  const [loading, setLoading] = useState(false);
  
  // Use signup email first, fallback to user email if available
  const email = signupEmail || userInfoGlobal?.email || null;

  const handleResendEmail = async () => {
    if (!email) {
      toast.error("No email address found");
      return;
    }

    setLoading(true);
    try {
      await axios.post(`${env.BASE_URL}/auth/resend-verification`, {
        email: email,
      });
      toast.success("Verification email has been resent successfully!");
    } catch (error) {
      console.error("Error resending verification email:", error);
      if (axios.isAxiosError(error) && error.response) {
        toast.error(error.response.data.message || "Failed to resend verification email. Please try again.");
      } else {
        toast.error("Failed to resend verification email. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   if (email) {
  //     axios
  //       .post(`${env.BASE_URL}/auth/email-verify`, {
  //         email: email,
  //       })
  //       .then((res) => {
  //         console.log(res.data);
  //         toast.success(res.data.message);
  //       })
  //       .catch((err) => {
  //         console.error("Error during email verification: ", err);
  //         if (axios.isAxiosError(err) && err.response) {
  //           toast.warning(err.response.data.message);
  //         } else {
  //           console.error("An unexpected error occurred:", err);
  //         }
  //       });
  //   } else {
  //     console.error("Email is null or undefined");
  //     navigate("/user");
  //   }

  //   console.log("email ===>", email);
  // });

  return (
    <div className="relative w-full min-h-screen dark:bg-[#070707] bg-white  lg:gap-0 gap-4">

      {/* logo */}
      <div className="relative left-8 top-5 w-1/2">
      <Logo />
      </div>

      <div className="fixed inset-0 w-full h-screen flex items-center justify-center">
        <div className="relative flex flex-col space-y-5">
          <div className="w-full flex justify-center">
            <img
              src={emailVerifyLogo}
              alt="emailVerifyLogo"
              className="w-[140px] h-[140px]"
            />
          </div>
          <div className="w-full text-center text-[35px] font-bold text-black dark:text-white">
            Verify your email to continue
          </div>
          <div className="w-full text-[20px] text-gray-400 text-center">
            We just sent an email to the address:{" "}
            {<span className="text-black dark:text-white"> {email} </span>} <br />
            Please check your email and select the link provided to verify your
            address.
          </div>
          
          <div className="w-full flex justify-center mt-8">
            <div className="w-[200px]">
            <div className="w-full h-full">
                  <GeneralButtonWithCss
                    onClick={loading ? undefined : handleResendEmail}
                    className="min-w-[106px] min-h-[41px] 2k:w-[180px] 2k:h-[60px] 2k:text-3xl text-black dark:text-white font-medium cursor-pointer"
                    bgClassName="dark:bg-[#1A1A1A] dark:bg-none bg-gradient-to-r from-[#7DDEE9] via-[#BBE0A5] to-[#E4E389]"
                  >
                    {t("Resend Email")}
                  </GeneralButtonWithCss>
                </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailVerifyView;
