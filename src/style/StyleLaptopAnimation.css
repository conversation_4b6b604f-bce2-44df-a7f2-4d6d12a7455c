*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: #000;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  width: 100%;
  height: 100vh;
}

.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loading-container {
  text-align: center;
  color: #fff;
  max-width: 80%;
}

.progress-bar-container {
  width: 300px;
  height: 10px;
  background-color: #333;
  border-radius: 5px;
  margin: 20px auto;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #fff;
  transition: width 0.3s ease;
}
