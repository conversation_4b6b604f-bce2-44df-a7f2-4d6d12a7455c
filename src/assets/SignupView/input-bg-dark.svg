<svg width="549" height="73" viewBox="0 0 549 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="549" height="73" fill="#1E1E1E"/>
<g clip-path="url(#clip0_0_1)">
<path d="M-67 -461C-67 -474.255 -56.2548 -485 -43 -485H1349C1362.25 -485 1373 -474.255 1373 -461V728C1373 741.255 1362.25 752 1349 752H-43C-56.2549 752 -67 741.255 -67 728V-461Z" fill="#0B0B0B"/>
<g filter="url(#filter0_f_0_1)">
<rect x="660.688" y="295.844" width="704.312" height="704.312" rx="352.156" fill="url(#paint0_linear_0_1)" fill-opacity="0.6"/>
</g>
<rect x="1" y="1" width="547" height="71" fill="url(#paint1_linear_0_1)" fill-opacity="0.4"/>
<rect x="1" y="1" width="547" height="71" fill="black" fill-opacity="0.2"/>
<rect x="1" y="1" width="547" height="71" stroke="url(#paint2_linear_0_1)" stroke-width="1.5"/>
</g>
<defs>
<filter id="filter0_f_0_1" x="323.985" y="-40.859" width="1377.72" height="1377.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="168.352" result="effect1_foregroundBlur_0_1"/>
</filter>
<linearGradient id="paint0_linear_0_1" x1="1365" y1="648" x2="660.688" y2="648" gradientUnits="userSpaceOnUse">
<stop stop-color="#C3BE25"/>
<stop offset="0.5" stop-color="#72C430"/>
<stop offset="1" stop-color="#2AB5C9"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="548" y1="36.5" x2="1" y2="36.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.5" stop-color="#9ED473"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1" x1="548" y1="36.5" x2="1" y2="36.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<clipPath id="clip0_0_1">
<path d="M-67 -461C-67 -474.255 -56.2548 -485 -43 -485H1349C1362.25 -485 1373 -474.255 1373 -461V728C1373 741.255 1362.25 752 1349 752H-43C-56.2549 752 -67 741.255 -67 728V-461Z" fill="white"/>
</clipPath>
</defs>
</svg>
