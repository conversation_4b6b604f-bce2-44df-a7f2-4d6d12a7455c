<svg width="163" height="44" viewBox="0 0 163 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="163" height="44" fill="#303030"/>
<g clip-path="url(#clip0_0_1)">
<rect width="1440" height="8817.74" transform="translate(-100 -452)" fill="#070707"/>
<g clip-path="url(#clip1_0_1)">
<g filter="url(#filter0_f_0_1)">
<rect x="187" y="-118" width="866" height="866" rx="433" fill="url(#paint0_linear_0_1)"/>
</g>
<g clip-path="url(#clip2_0_1)">
<rect x="1" y="1.5" width="161" height="41" fill="black" fill-opacity="0.2"/>
<rect x="1" y="1.5" width="161" height="41" stroke="url(#paint1_linear_0_1)" stroke-width="1.5"/>
<g opacity="0.5" filter="url(#filter1_f_0_1)">
<rect width="161" height="41" transform="translate(1 22.5)" fill="url(#paint2_linear_0_1)"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_f_0_1" x="-227" y="-532" width="1694" height="1694" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="207" result="effect1_foregroundBlur_0_1"/>
</filter>
<filter id="filter1_f_0_1" x="-63" y="-41.5" width="289" height="169" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_0_1"/>
</filter>
<linearGradient id="paint0_linear_0_1" x1="1053" y1="315" x2="187" y2="315" gradientUnits="userSpaceOnUse">
<stop stop-color="#C3BE25"/>
<stop offset="0.5" stop-color="#72C430"/>
<stop offset="1" stop-color="#2AB5C9"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="162" y1="22" x2="1" y2="22" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1" x1="161" y1="20.5" x2="0" y2="20.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<clipPath id="clip0_0_1">
<rect width="1440" height="8817.74" fill="white" transform="translate(-100 -452)"/>
</clipPath>
<clipPath id="clip1_0_1">
<rect width="1440" height="810" fill="white" transform="translate(-100 -452)"/>
</clipPath>
<clipPath id="clip2_0_1">
<rect width="163" height="43" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
