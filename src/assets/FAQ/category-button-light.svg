<svg width="420" height="73" viewBox="0 0 420 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="420" height="73" fill="#1E1E1E"/>
<g clip-path="url(#clip0_0_1)">
<rect width="1440" height="2234" transform="translate(-98.7617 -559)" fill="#F5F5F5"/>
<rect x="1.23828" y="1" width="418" height="71" fill="url(#paint0_linear_0_1)"/>
<rect x="1.23828" y="1" width="418" height="71" stroke="url(#paint1_linear_0_1)" stroke-width="1.5"/>
<g opacity="0.5" filter="url(#filter1_f_0_1)">
<rect width="840" height="139" transform="translate(469.238 1.24219)" fill="url(#paint2_linear_0_1)"/>
</g>
</g>
<defs>
<filter id="filter1_f_0_1" x="335.238" y="-132.758" width="1108" height="407" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="67" result="effect1_foregroundBlur_0_1"/>
</filter>
<linearGradient id="paint0_linear_0_1" x1="419.238" y1="36.5" x2="1.23828" y2="36.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.5" stop-color="#9ED473"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="419.238" y1="36.5" x2="1.23828" y2="36.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1" x1="840" y1="69.5" x2="0" y2="69.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.5" stop-color="#9ED473"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<clipPath id="clip0_0_1">
<rect width="1440" height="2234" fill="white" transform="translate(-98.7617 -559)"/>
</clipPath>
</defs>
</svg>
