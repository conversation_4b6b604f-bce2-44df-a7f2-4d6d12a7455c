<svg width="549" height="58" viewBox="0 0 549 58" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="549" height="58" fill="#303030"/>
<g clip-path="url(#clip0_0_1)">
<path d="M-67 -952C-67 -965.255 -56.2548 -976 -43 -976H1349C1362.25 -976 1373 -965.255 1373 -952V237C1373 250.255 1362.25 261 1349 261H-43C-56.2549 261 -67 250.255 -67 237V-952Z" fill="white"/>
<g filter="url(#filter0_f_0_1)">
<rect x="660.688" y="-195.156" width="704.312" height="704.312" rx="352.156" fill="url(#paint0_linear_0_1)" fill-opacity="0.6"/>
</g>
<g opacity="0.5" filter="url(#filter1_f_0_1)">
<rect width="547" height="56" transform="translate(0.821777 1)" fill="url(#paint1_linear_0_1)"/>
</g>
<rect x="0.821777" y="1" width="547" height="56" fill="url(#paint2_linear_0_1)"/>
<rect x="0.821777" y="1" width="547" height="56" stroke="url(#paint3_linear_0_1)" stroke-width="1.5"/>
</g>
<defs>
<filter id="filter0_f_0_1" x="323.985" y="-531.86" width="1377.72" height="1377.72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="168.352" result="effect1_foregroundBlur_0_1"/>
</filter>
<filter id="filter1_f_0_1" x="-63.1782" y="-63" width="675" height="184" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="32" result="effect1_foregroundBlur_0_1"/>
</filter>
<linearGradient id="paint0_linear_0_1" x1="1365" y1="157" x2="660.688" y2="157" gradientUnits="userSpaceOnUse">
<stop stop-color="#C3BE25"/>
<stop offset="0.5" stop-color="#72C430"/>
<stop offset="1" stop-color="#2AB5C9"/>
</linearGradient>
<linearGradient id="paint1_linear_0_1" x1="547" y1="28" x2="0" y2="28" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<linearGradient id="paint2_linear_0_1" x1="547.822" y1="29" x2="0.821777" y2="29" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.5" stop-color="#9ED473"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<linearGradient id="paint3_linear_0_1" x1="547.822" y1="29" x2="0.821777" y2="29" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633"/>
<stop offset="0.15" stop-color="#DBD633" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6" stop-opacity="0"/>
<stop offset="0.85" stop-color="#1CCDE6"/>
<stop offset="1" stop-color="#1CCDE6"/>
</linearGradient>
<clipPath id="clip0_0_1">
<path d="M-67 -952C-67 -965.255 -56.2548 -976 -43 -976H1349C1362.25 -976 1373 -965.255 1373 -952V237C1373 250.255 1362.25 261 1349 261H-43C-56.2549 261 -67 250.255 -67 237V-952Z" fill="white"/>
</clipPath>
</defs>
</svg>
