import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Provider as <PERSON><PERSON><PERSON>rovider } from "jotai";
import ErrorBoundary from "../components/common/ErrorBoundary";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 30000,
    },
  },
});

export default function AppProviders({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <JotaiProvider>
          {children}
        </JotaiProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}