// third-party
import { combineReducers } from "redux";

// project import
import metaAccount from "./metaAccount";
import metaAccountInfo from "./metaAccountInfo";
import webhook from "./webhook";
import metaStats from "./metaStats";
import metaTotalStats from "./metaTotalStats";
import metaVisualTrades from "./metaVisualTrades";
import closeOrder from "./closeOrder";
import tradelocker from "./tradelocker";
import tradelockerInfo from "./tradelockerInfo";
import trade from "./trade";

// ==============================|| COMBINE REDUCERS ||============================== //

const reducers = combineReducers({
  metaAccount,
  metaAccountInfo,
  webhook,
  metaStats,
  metaTotalStats,
  metaVisualTrades,
  closeOrder,
  tradelocker,
  tradelockerInfo,
  trade,
});

export default reducers;
