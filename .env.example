# === URFX Trading Platform Frontend Example Environment Variables ===
# Copy this file to .env and fill in the actual values for your environment

# --- API URLs ---
VITE_API_URL=http://localhost:5000/api           # Main backend API base URL
VITE_BASE_URL=http://localhost:5000/api          # Used in some API clients
VITE_IMAGE_URL=http://localhost:5000/api         # Avatar/image base URL
VITE_WEBHOOK_RECEIVER_URL=http://localhost:4000  # Webhook receiver endpoint
VITE_TRADE_MANAGEMENT_URL=http://localhost:4001  # Trade management endpoint

# --- Authentication & Security ---
VITE_JWT_SECRET=your_jwt_secret_here

# --- Meta API ---
VITE_META_API_TOKEN=your_meta_api_token_here

# --- Telegram ---
VITE_TELEGRAM_API_TOKEN=your_telegram_api_token_here
VITE_TELEGRAM_COMMUNITY=https://t.me/your_community
VITE_TELEGRAM_SUPPORT=https://t.me/your_support

# --- Discord & Instagram ---
VITE_DISCORD_COMMUNITY=https://discord.gg/your_community
VITE_INSTAGRAM_COMMUNITY=https://instagram.com/your_community

# --- Sellix (Payments) ---
VITE_SELLIX_WEBHOOK_SECRET=your_sellix_webhook_secret
VITE_SELLIX_API_KEY=your_sellix_api_key
VITE_SELLIX_MERCHANT_ID=your_sellix_merchant_id

# --- Google OAuth ---
VITE_GOOGLE_CLIENT_ID=your_google_client_id

# --- PayPal ---
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id

# --- Analytics & Marketing ---
VITE_GA_MEASUREMENT_ID=your_ga_measurement_id
VITE_KLAVIO_PRIVATE_KEY=your_klavio_private_key

# --- AWS S3 ---
VITE_S3_BUCKET_NAME=urfx.io
VITE_AWS_REGION=us-east-2

# --- App URLs ---
VITE_APP_URL=http://localhost:3000
VITE_LANDING_URL=/

# --- Miscellaneous ---
VITE_PHONE_NUMBER=+1234567890
VITE_ADMIN_EMAIL=<EMAIL>

# --- Feature Flags (set by Vite automatically, do not edit) ---
# VITE_PROD, VITE_DEV, VITE_MODE