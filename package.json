{"name": "urfx", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "typecheck": "tsc --noEmit", "prepare": "husky"}, "dependencies": {"@eslint/js": "^9.24.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@paypal/react-paypal-js": "^8.8.3", "@react-oauth/google": "^0.12.1", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-query": "^5.72.0", "@types/three": "^0.175.0", "axios": "^1.6.7", "date-fns": "^3.3.1", "framer-motion": "^12.6.3", "globals": "^15.15.0", "i18next": "^25.0.1", "jotai": "^2.6.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.344.0", "p5": "^1.11.3", "primeicons": "^7.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.1", "react-icons": "^5.4.0", "react-loader-spinner": "^6.1.6", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.3", "react-spinners": "^0.15.0", "react-toastify": "^11.0.3", "react-ts-tradingview-widgets": "^1.2.8", "recharts": "^2.15.1", "redux-persist": "^6.0.0", "simplex-noise": "^4.0.3", "swiper": "^11.2.5", "tailwind-scrollbar-hide": "^2.0.0", "three": "^0.175.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/node": "^20.11.24", "@types/p5": "^1.7.6", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.18", "eslint": "^9.24.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "vite": "^6.2.6", "husky": "^9.1.7"}}