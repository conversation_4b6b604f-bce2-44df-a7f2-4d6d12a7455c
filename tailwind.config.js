/** @type {import('tailwindcss').Config} */
import tailwindScrollbarHide from "tailwind-scrollbar-hide";
const { fontFamily } = require('tailwindcss/defaultTheme');

export default {
  darkMode: 'class',
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily:{
        creato: ['"Creato Display"', 'sans-serif'],
        outfit: ['"Outfit"', 'sans-serif'],
        messina:['Messina', ...fontFamily.sans],
      },
      screens:{
        "3xl":"1920px",
        "2k": "2560px",
      },
      colors: {
        dark: {
          DEFAULT: "#000000",
          50: "#161618",
          100: "#1C1C1F",
          200: "#222225",
          300: "#2A2A2E",
          400: "#323236",
          500: "#3A3A3F",
          600: "#434348",
          700: "#4C4C52",
        },
        accent: {
          DEFAULT: "#007AFF",
          dark: "#0056B3",
        },
        primary: "#0A0D34",
        secondary: "#00ffcc",
        darkContent: "#9CA3AF",
        skyBright: "#1CCDE6",
        teal: "#DBD633",
        darkOlive: "#302F19",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        glass:
          "linear-gradient(rgba(255, 255, 255, 0.03), rgba(255, 255, 255, 0.01))",
        "glass-hover":
          "linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02))",
        "primary-gradient": "linear-gradient(270deg, rgba(219, 214, 51, 0.4) 0%, rgba(158, 212, 115, 0.4) 50%, rgba(28, 205, 230, 0.4) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2))",
        "dark-gradient": "linear-gradient(180deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)",
        "soft-radial-green-mobile": "radial-gradient(circle 1600px at 80% 100%, #d4cf25 0%, #000000 25%, #000000 100%)",
        "soft-radial-blue-mobile": "radial-gradient(circle 1600px at 70% 100%, #2b6647 0%, #000000 25%, #000000 100%)",
      },
      boxShadow: {
        glass: "0 4px 24px -1px rgba(0, 0, 0, 0.2)",
        "glass-hover": "0 8px 32px -1px rgba(0, 0, 0, 0.3)",
        "inner-light": "inset 0 1px 0 0 rgba(255, 255, 255, 0.05)",
        "active": "inset 0 0 12px rgb(255, 255, 255)",
      },
      animation: {
        "float-slow": "float-slow 6s ease-in-out infinite",
        "float-medium": "float-medium 5s ease-in-out infinite",
        "float-fast": "float-fast 4s ease-in-out infinite",
        "gradient-x": "gradient-x 15s ease infinite",
        "fade-in-up": "fade-in-up 0.5s ease-out forwards",
        "spin-slow": "spin 3s linear infinite",
        "infinite-scroll": "infinite-scroll 20s linear infinite",
        "infinite-scroll-reverse":"infinite-scroll 20s linear infinite reverse",
        "infinite-scroll-vertical": "infinite-scroll-vertical 20s linear infinite",
      },
      keyframes: {
        "float-slow": {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-20px)" },
        },
        "float-medium": {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-15px)" },
        },
        "float-fast": {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-10px)" },
        },
        "gradient-x": {
          "0%, 100%": {
            "background-size": "200% 100%",
            "background-position": "left center",
          },
          "50%": {
            "background-size": "200% 100%",
            "background-position": "right center",
          },
        },
        "fade-in-up": {
          "0%": {
            opacity: "0",
            transform: "translateY(20px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "infinite-scroll": {
          from: { transform: "translateX(0)" },
          to: { transform: "translateX(-50%)" },
        },
        "infinite-scroll-vertical": {
          from: { transform: "translateY(0)" },
          to: { transform: "translateY(-100%)" },
        },
      },
    },
  },
  plugins: [tailwindScrollbarHide],
};
