{"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2016.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@types/react/global.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@types/react/index.d.ts", "./node_modules/@types/react/jsx-runtime.d.ts", "./node_modules/react-router/dist/development/route-data-D7Xbr_Ww.d.mts", "./node_modules/react-router/dist/development/lib-B33EY9A0.d.mts", "./node_modules/react-router/dist/development/dom-export.d.mts", "./node_modules/cookie/dist/index.d.ts", "./node_modules/react-router/dist/development/register-DeIo2iHO.d.mts", "./node_modules/react-router/dist/development/index.d.mts", "./node_modules/react-router-dom/dist/index.d.mts", "./node_modules/jotai/esm/vanilla/internals.d.mts", "./node_modules/jotai/esm/vanilla/store.d.mts", "./node_modules/jotai/esm/vanilla/atom.d.mts", "./node_modules/jotai/esm/vanilla/typeUtils.d.mts", "./node_modules/jotai/esm/vanilla.d.mts", "./node_modules/jotai/esm/react/Provider.d.mts", "./node_modules/jotai/esm/react/useAtomValue.d.mts", "./node_modules/jotai/esm/react/useSetAtom.d.mts", "./node_modules/jotai/esm/react/useAtom.d.mts", "./node_modules/jotai/esm/react.d.mts", "./node_modules/jotai/esm/index.d.mts", "./node_modules/react-toastify/dist/index.d.ts", "./node_modules/jotai/esm/vanilla/utils/constants.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomWithReset.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomWithReducer.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomFamily.d.mts", "./node_modules/jotai/esm/vanilla/utils/selectAtom.d.mts", "./node_modules/jotai/esm/vanilla/utils/freezeAtom.d.mts", "./node_modules/jotai/esm/vanilla/utils/splitAtom.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomWithDefault.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomWithStorage.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomWithObservable.d.mts", "./node_modules/jotai/esm/vanilla/utils/loadable.d.mts", "./node_modules/jotai/esm/vanilla/utils/unwrap.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomWithRefresh.d.mts", "./node_modules/jotai/esm/vanilla/utils/atomWithLazy.d.mts", "./node_modules/jotai/esm/vanilla/utils.d.mts", "./node_modules/jotai/esm/react/utils/useResetAtom.d.mts", "./node_modules/jotai/esm/react/utils/useReducerAtom.d.mts", "./node_modules/jotai/esm/react/utils/useAtomCallback.d.mts", "./node_modules/jotai/esm/react/utils/useHydrateAtoms.d.mts", "./node_modules/jotai/esm/react/utils.d.mts", "./node_modules/jotai/esm/utils.d.mts", "./src/types/index.tsx", "./src/utils/format.tsx", "./node_modules/react-i18next/helpers.d.ts", "./node_modules/i18next/typescript/helpers.d.ts", "./node_modules/i18next/typescript/options.d.ts", "./node_modules/i18next/typescript/t.d.ts", "./node_modules/i18next/index.d.ts", "./node_modules/i18next/index.d.mts", "./node_modules/react-i18next/TransWithoutContext.d.ts", "./node_modules/react-i18next/initReactI18next.d.ts", "./node_modules/react-i18next/index.d.ts", "./node_modules/react-i18next/index.d.mts", "./src/components/featureSection/AccountSizeButton.tsx", "./node_modules/lucide-react/dist/lucide-react.d.ts", "./src/components/model/RadioButton.tsx", "./src/components/landing/GeneralButtonWithCss.tsx", "./src/components/featureSection/FundingTypeButton.tsx", "./src/components/landing/FeaturesSection.tsx", "./node_modules/@tanstack/query-core/build/modern/removable.d.ts", "./node_modules/@tanstack/query-core/build/modern/subscribable.d.ts", "./node_modules/@tanstack/query-core/build/modern/hydration-Cr-4Kky1.d.ts", "./node_modules/@tanstack/query-core/build/modern/queriesObserver.d.ts", "./node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.d.ts", "./node_modules/@tanstack/query-core/build/modern/notifyManager.d.ts", "./node_modules/@tanstack/query-core/build/modern/focusManager.d.ts", "./node_modules/@tanstack/query-core/build/modern/onlineManager.d.ts", "./node_modules/@tanstack/query-core/build/modern/streamedQuery.d.ts", "./node_modules/@tanstack/query-core/build/modern/index.d.ts", "./node_modules/@tanstack/react-query/build/modern/types.d.ts", "./node_modules/@tanstack/react-query/build/modern/useQueries.d.ts", "./node_modules/@tanstack/react-query/build/modern/queryOptions.d.ts", "./node_modules/@tanstack/react-query/build/modern/useQuery.d.ts", "./node_modules/@tanstack/react-query/build/modern/useSuspenseQuery.d.ts", "./node_modules/@tanstack/react-query/build/modern/useSuspenseInfiniteQuery.d.ts", "./node_modules/@tanstack/react-query/build/modern/useSuspenseQueries.d.ts", "./node_modules/@tanstack/react-query/build/modern/usePrefetchQuery.d.ts", "./node_modules/@tanstack/react-query/build/modern/usePrefetchInfiniteQuery.d.ts", "./node_modules/@tanstack/react-query/build/modern/infiniteQueryOptions.d.ts", "./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.d.ts", "./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.d.ts", "./node_modules/@tanstack/react-query/build/modern/HydrationBoundary.d.ts", "./node_modules/@tanstack/react-query/build/modern/useIsFetching.d.ts", "./node_modules/@tanstack/react-query/build/modern/useMutationState.d.ts", "./node_modules/@tanstack/react-query/build/modern/useMutation.d.ts", "./node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.d.ts", "./node_modules/@tanstack/react-query/build/modern/IsRestoringProvider.d.ts", "./node_modules/@tanstack/react-query/build/modern/index.d.ts", "./src/config/env.ts", "./node_modules/axios/index.d.ts", "./src/utils/api.tsx", "./src/components/Footer.tsx", "./src/components/landing/GeneralButton.tsx", "./src/components/common/CustomInput.tsx", "./src/components/common/GradientBorder.tsx", "./src/components/common/SelectInput.tsx", "./src/components/getfunded/GetFunded.tsx", "./src/store/atoms.ts", "./node_modules/redux/dist/redux.d.ts", "./node_modules/immer/dist/immer.d.ts", "./node_modules/reselect/dist/reselect.d.ts", "./node_modules/redux-thunk/dist/redux-thunk.d.ts", "./node_modules/@reduxjs/toolkit/dist/uncheckedindexed.ts", "./node_modules/@reduxjs/toolkit/dist/index.d.mts", "./node_modules/react-redux/dist/react-redux.d.ts", "./node_modules/redux-persist/types/constants.d.ts", "./node_modules/redux-persist/types/createMigrate.d.ts", "./node_modules/redux-persist/types/createPersistoid.d.ts", "./node_modules/redux-persist/types/createTransform.d.ts", "./node_modules/redux-persist/types/getStoredState.d.ts", "./node_modules/redux-persist/types/integration/getStoredStateMigrateV4.d.ts", "./node_modules/redux-persist/types/integration/react.d.ts", "./node_modules/redux-persist/types/persistCombineReducers.d.ts", "./node_modules/redux-persist/types/persistReducer.d.ts", "./node_modules/redux-persist/types/persistStore.d.ts", "./node_modules/redux-persist/types/purgeStoredState.d.ts", "./node_modules/redux-persist/types/stateReconciler/autoMergeLevel1.d.ts", "./node_modules/redux-persist/types/stateReconciler/autoMergeLevel2.d.ts", "./node_modules/redux-persist/types/stateReconciler/hardSet.d.ts", "./node_modules/redux-persist/types/storage/createWebStorage.d.ts", "./node_modules/redux-persist/types/storage/getStorage.d.ts", "./node_modules/redux-persist/types/storage/index.d.ts", "./node_modules/redux-persist/types/storage/session.d.ts", "./node_modules/redux-persist/types/types.d.ts", "./node_modules/redux-persist/types/index.d.ts", "./src/types/metaAccount.tsx", "./src/app/reducers/metaAccount.tsx", "./src/app/reducers/metaAccountInfo.tsx", "./src/types/webhook.tsx", "./src/app/reducers/webhook.tsx", "./src/types/metaStats.tsx", "./src/app/reducers/metaStats.tsx", "./node_modules/date-fns/locale/types.d.ts", "./node_modules/date-fns/fp/types.d.ts", "./node_modules/date-fns/types.d.ts", "./node_modules/date-fns/add.d.ts", "./node_modules/date-fns/addBusinessDays.d.ts", "./node_modules/date-fns/addDays.d.ts", "./node_modules/date-fns/addHours.d.ts", "./node_modules/date-fns/addISOWeekYears.d.ts", "./node_modules/date-fns/addMilliseconds.d.ts", "./node_modules/date-fns/addMinutes.d.ts", "./node_modules/date-fns/addMonths.d.ts", "./node_modules/date-fns/addQuarters.d.ts", "./node_modules/date-fns/addSeconds.d.ts", "./node_modules/date-fns/addWeeks.d.ts", "./node_modules/date-fns/addYears.d.ts", "./node_modules/date-fns/areIntervalsOverlapping.d.ts", "./node_modules/date-fns/clamp.d.ts", "./node_modules/date-fns/closestIndexTo.d.ts", "./node_modules/date-fns/closestTo.d.ts", "./node_modules/date-fns/compareAsc.d.ts", "./node_modules/date-fns/compareDesc.d.ts", "./node_modules/date-fns/constructFrom.d.ts", "./node_modules/date-fns/constructNow.d.ts", "./node_modules/date-fns/daysToWeeks.d.ts", "./node_modules/date-fns/differenceInBusinessDays.d.ts", "./node_modules/date-fns/differenceInCalendarDays.d.ts", "./node_modules/date-fns/differenceInCalendarISOWeekYears.d.ts", "./node_modules/date-fns/differenceInCalendarISOWeeks.d.ts", "./node_modules/date-fns/differenceInCalendarMonths.d.ts", "./node_modules/date-fns/differenceInCalendarQuarters.d.ts", "./node_modules/date-fns/differenceInCalendarWeeks.d.ts", "./node_modules/date-fns/differenceInCalendarYears.d.ts", "./node_modules/date-fns/differenceInDays.d.ts", "./node_modules/date-fns/differenceInHours.d.ts", "./node_modules/date-fns/differenceInISOWeekYears.d.ts", "./node_modules/date-fns/differenceInMilliseconds.d.ts", "./node_modules/date-fns/differenceInMinutes.d.ts", "./node_modules/date-fns/differenceInMonths.d.ts", "./node_modules/date-fns/differenceInQuarters.d.ts", "./node_modules/date-fns/differenceInSeconds.d.ts", "./node_modules/date-fns/differenceInWeeks.d.ts", "./node_modules/date-fns/differenceInYears.d.ts", "./node_modules/date-fns/eachDayOfInterval.d.ts", "./node_modules/date-fns/eachHourOfInterval.d.ts", "./node_modules/date-fns/eachMinuteOfInterval.d.ts", "./node_modules/date-fns/eachMonthOfInterval.d.ts", "./node_modules/date-fns/eachQuarterOfInterval.d.ts", "./node_modules/date-fns/eachWeekOfInterval.d.ts", "./node_modules/date-fns/eachWeekendOfInterval.d.ts", "./node_modules/date-fns/eachWeekendOfMonth.d.ts", "./node_modules/date-fns/eachWeekendOfYear.d.ts", "./node_modules/date-fns/eachYearOfInterval.d.ts", "./node_modules/date-fns/endOfDay.d.ts", "./node_modules/date-fns/endOfDecade.d.ts", "./node_modules/date-fns/endOfHour.d.ts", "./node_modules/date-fns/endOfISOWeek.d.ts", "./node_modules/date-fns/endOfISOWeekYear.d.ts", "./node_modules/date-fns/endOfMinute.d.ts", "./node_modules/date-fns/endOfMonth.d.ts", "./node_modules/date-fns/endOfQuarter.d.ts", "./node_modules/date-fns/endOfSecond.d.ts", "./node_modules/date-fns/endOfToday.d.ts", "./node_modules/date-fns/endOfTomorrow.d.ts", "./node_modules/date-fns/endOfWeek.d.ts", "./node_modules/date-fns/endOfYear.d.ts", "./node_modules/date-fns/endOfYesterday.d.ts", "./node_modules/date-fns/_lib/format/formatters.d.ts", "./node_modules/date-fns/_lib/format/longFormatters.d.ts", "./node_modules/date-fns/format.d.ts", "./node_modules/date-fns/formatDistance.d.ts", "./node_modules/date-fns/formatDistanceStrict.d.ts", "./node_modules/date-fns/formatDistanceToNow.d.ts", "./node_modules/date-fns/formatDistanceToNowStrict.d.ts", "./node_modules/date-fns/formatDuration.d.ts", "./node_modules/date-fns/formatISO.d.ts", "./node_modules/date-fns/formatISO9075.d.ts", "./node_modules/date-fns/formatISODuration.d.ts", "./node_modules/date-fns/formatRFC3339.d.ts", "./node_modules/date-fns/formatRFC7231.d.ts", "./node_modules/date-fns/formatRelative.d.ts", "./node_modules/date-fns/fromUnixTime.d.ts", "./node_modules/date-fns/getDate.d.ts", "./node_modules/date-fns/getDay.d.ts", "./node_modules/date-fns/getDayOfYear.d.ts", "./node_modules/date-fns/getDaysInMonth.d.ts", "./node_modules/date-fns/getDaysInYear.d.ts", "./node_modules/date-fns/getDecade.d.ts", "./node_modules/date-fns/_lib/defaultOptions.d.ts", "./node_modules/date-fns/getDefaultOptions.d.ts", "./node_modules/date-fns/getHours.d.ts", "./node_modules/date-fns/getISODay.d.ts", "./node_modules/date-fns/getISOWeek.d.ts", "./node_modules/date-fns/getISOWeekYear.d.ts", "./node_modules/date-fns/getISOWeeksInYear.d.ts", "./node_modules/date-fns/getMilliseconds.d.ts", "./node_modules/date-fns/getMinutes.d.ts", "./node_modules/date-fns/getMonth.d.ts", "./node_modules/date-fns/getOverlappingDaysInIntervals.d.ts", "./node_modules/date-fns/getQuarter.d.ts", "./node_modules/date-fns/getSeconds.d.ts", "./node_modules/date-fns/getTime.d.ts", "./node_modules/date-fns/getUnixTime.d.ts", "./node_modules/date-fns/getWeek.d.ts", "./node_modules/date-fns/getWeekOfMonth.d.ts", "./node_modules/date-fns/getWeekYear.d.ts", "./node_modules/date-fns/getWeeksInMonth.d.ts", "./node_modules/date-fns/getYear.d.ts", "./node_modules/date-fns/hoursToMilliseconds.d.ts", "./node_modules/date-fns/hoursToMinutes.d.ts", "./node_modules/date-fns/hoursToSeconds.d.ts", "./node_modules/date-fns/interval.d.ts", "./node_modules/date-fns/intervalToDuration.d.ts", "./node_modules/date-fns/intlFormat.d.ts", "./node_modules/date-fns/intlFormatDistance.d.ts", "./node_modules/date-fns/isAfter.d.ts", "./node_modules/date-fns/isBefore.d.ts", "./node_modules/date-fns/isDate.d.ts", "./node_modules/date-fns/isEqual.d.ts", "./node_modules/date-fns/isExists.d.ts", "./node_modules/date-fns/isFirstDayOfMonth.d.ts", "./node_modules/date-fns/isFriday.d.ts", "./node_modules/date-fns/isFuture.d.ts", "./node_modules/date-fns/isLastDayOfMonth.d.ts", "./node_modules/date-fns/isLeapYear.d.ts", "./node_modules/date-fns/isMatch.d.ts", "./node_modules/date-fns/isMonday.d.ts", "./node_modules/date-fns/isPast.d.ts", "./node_modules/date-fns/isSameDay.d.ts", "./node_modules/date-fns/isSameHour.d.ts", "./node_modules/date-fns/isSameISOWeek.d.ts", "./node_modules/date-fns/isSameISOWeekYear.d.ts", "./node_modules/date-fns/isSameMinute.d.ts", "./node_modules/date-fns/isSameMonth.d.ts", "./node_modules/date-fns/isSameQuarter.d.ts", "./node_modules/date-fns/isSameSecond.d.ts", "./node_modules/date-fns/isSameWeek.d.ts", "./node_modules/date-fns/isSameYear.d.ts", "./node_modules/date-fns/isSaturday.d.ts", "./node_modules/date-fns/isSunday.d.ts", "./node_modules/date-fns/isThisHour.d.ts", "./node_modules/date-fns/isThisISOWeek.d.ts", "./node_modules/date-fns/isThisMinute.d.ts", "./node_modules/date-fns/isThisMonth.d.ts", "./node_modules/date-fns/isThisQuarter.d.ts", "./node_modules/date-fns/isThisSecond.d.ts", "./node_modules/date-fns/isThisWeek.d.ts", "./node_modules/date-fns/isThisYear.d.ts", "./node_modules/date-fns/isThursday.d.ts", "./node_modules/date-fns/isToday.d.ts", "./node_modules/date-fns/isTomorrow.d.ts", "./node_modules/date-fns/isTuesday.d.ts", "./node_modules/date-fns/isValid.d.ts", "./node_modules/date-fns/isWednesday.d.ts", "./node_modules/date-fns/isWeekend.d.ts", "./node_modules/date-fns/isWithinInterval.d.ts", "./node_modules/date-fns/isYesterday.d.ts", "./node_modules/date-fns/lastDayOfDecade.d.ts", "./node_modules/date-fns/lastDayOfISOWeek.d.ts", "./node_modules/date-fns/lastDayOfISOWeekYear.d.ts", "./node_modules/date-fns/lastDayOfMonth.d.ts", "./node_modules/date-fns/lastDayOfQuarter.d.ts", "./node_modules/date-fns/lastDayOfWeek.d.ts", "./node_modules/date-fns/lastDayOfYear.d.ts", "./node_modules/date-fns/_lib/format/lightFormatters.d.ts", "./node_modules/date-fns/lightFormat.d.ts", "./node_modules/date-fns/max.d.ts", "./node_modules/date-fns/milliseconds.d.ts", "./node_modules/date-fns/millisecondsToHours.d.ts", "./node_modules/date-fns/millisecondsToMinutes.d.ts", "./node_modules/date-fns/millisecondsToSeconds.d.ts", "./node_modules/date-fns/min.d.ts", "./node_modules/date-fns/minutesToHours.d.ts", "./node_modules/date-fns/minutesToMilliseconds.d.ts", "./node_modules/date-fns/minutesToSeconds.d.ts", "./node_modules/date-fns/monthsToQuarters.d.ts", "./node_modules/date-fns/monthsToYears.d.ts", "./node_modules/date-fns/nextDay.d.ts", "./node_modules/date-fns/nextFriday.d.ts", "./node_modules/date-fns/nextMonday.d.ts", "./node_modules/date-fns/nextSaturday.d.ts", "./node_modules/date-fns/nextSunday.d.ts", "./node_modules/date-fns/nextThursday.d.ts", "./node_modules/date-fns/nextTuesday.d.ts", "./node_modules/date-fns/nextWednesday.d.ts", "./node_modules/date-fns/parse/_lib/types.d.ts", "./node_modules/date-fns/parse/_lib/Setter.d.ts", "./node_modules/date-fns/parse/_lib/Parser.d.ts", "./node_modules/date-fns/parse/_lib/parsers.d.ts", "./node_modules/date-fns/parse.d.ts", "./node_modules/date-fns/parseISO.d.ts", "./node_modules/date-fns/parseJSON.d.ts", "./node_modules/date-fns/previousDay.d.ts", "./node_modules/date-fns/previousFriday.d.ts", "./node_modules/date-fns/previousMonday.d.ts", "./node_modules/date-fns/previousSaturday.d.ts", "./node_modules/date-fns/previousSunday.d.ts", "./node_modules/date-fns/previousThursday.d.ts", "./node_modules/date-fns/previousTuesday.d.ts", "./node_modules/date-fns/previousWednesday.d.ts", "./node_modules/date-fns/quartersToMonths.d.ts", "./node_modules/date-fns/quartersToYears.d.ts", "./node_modules/date-fns/roundToNearestHours.d.ts", "./node_modules/date-fns/roundToNearestMinutes.d.ts", "./node_modules/date-fns/secondsToHours.d.ts", "./node_modules/date-fns/secondsToMilliseconds.d.ts", "./node_modules/date-fns/secondsToMinutes.d.ts", "./node_modules/date-fns/set.d.ts", "./node_modules/date-fns/setDate.d.ts", "./node_modules/date-fns/setDay.d.ts", "./node_modules/date-fns/setDayOfYear.d.ts", "./node_modules/date-fns/setDefaultOptions.d.ts", "./node_modules/date-fns/setHours.d.ts", "./node_modules/date-fns/setISODay.d.ts", "./node_modules/date-fns/setISOWeek.d.ts", "./node_modules/date-fns/setISOWeekYear.d.ts", "./node_modules/date-fns/setMilliseconds.d.ts", "./node_modules/date-fns/setMinutes.d.ts", "./node_modules/date-fns/setMonth.d.ts", "./node_modules/date-fns/setQuarter.d.ts", "./node_modules/date-fns/setSeconds.d.ts", "./node_modules/date-fns/setWeek.d.ts", "./node_modules/date-fns/setWeekYear.d.ts", "./node_modules/date-fns/setYear.d.ts", "./node_modules/date-fns/startOfDay.d.ts", "./node_modules/date-fns/startOfDecade.d.ts", "./node_modules/date-fns/startOfHour.d.ts", "./node_modules/date-fns/startOfISOWeek.d.ts", "./node_modules/date-fns/startOfISOWeekYear.d.ts", "./node_modules/date-fns/startOfMinute.d.ts", "./node_modules/date-fns/startOfMonth.d.ts", "./node_modules/date-fns/startOfQuarter.d.ts", "./node_modules/date-fns/startOfSecond.d.ts", "./node_modules/date-fns/startOfToday.d.ts", "./node_modules/date-fns/startOfTomorrow.d.ts", "./node_modules/date-fns/startOfWeek.d.ts", "./node_modules/date-fns/startOfWeekYear.d.ts", "./node_modules/date-fns/startOfYear.d.ts", "./node_modules/date-fns/startOfYesterday.d.ts", "./node_modules/date-fns/sub.d.ts", "./node_modules/date-fns/subBusinessDays.d.ts", "./node_modules/date-fns/subDays.d.ts", "./node_modules/date-fns/subHours.d.ts", "./node_modules/date-fns/subISOWeekYears.d.ts", "./node_modules/date-fns/subMilliseconds.d.ts", "./node_modules/date-fns/subMinutes.d.ts", "./node_modules/date-fns/subMonths.d.ts", "./node_modules/date-fns/subQuarters.d.ts", "./node_modules/date-fns/subSeconds.d.ts", "./node_modules/date-fns/subWeeks.d.ts", "./node_modules/date-fns/subYears.d.ts", "./node_modules/date-fns/toDate.d.ts", "./node_modules/date-fns/transpose.d.ts", "./node_modules/date-fns/weeksToDays.d.ts", "./node_modules/date-fns/yearsToDays.d.ts", "./node_modules/date-fns/yearsToMonths.d.ts", "./node_modules/date-fns/yearsToQuarters.d.ts", "./node_modules/date-fns/index.d.mts", "./src/app/reducers/metaTotalStats.tsx", "./src/app/reducers/metaVisualTrades.tsx", "./src/app/reducers/closeOrder.tsx", "./src/types/tradeLocker.tsx", "./src/app/reducers/tradelocker.tsx", "./src/app/reducers/tradelockerInfo.tsx", "./src/types/trade.tsx", "./src/app/reducers/trade.tsx", "./src/app/reducers/index.tsx", "./src/app/store.tsx", "./src/utils/scrollToFeatureSectionFunction.tsx", "./src/components/heroSection/CarouselButton.tsx", "./src/components/landing/HeroSection.tsx", "./src/components/charts/LivePerformanceChart.tsx", "./src/components/landing/PerformanceChart.tsx", "./src/components/leaderboard/Panel.tsx", "./src/components/landing/LeaderboardSection.tsx", "./src/components/landing/FaqSection.tsx", "./src/components/landing/TextScrollerSection.tsx", "./src/components/landing/DashboardAdSection.tsx", "./src/types/certificateData.tsx", "./src/components/CertificateCardBackground.tsx", "./src/components/landing/CertificateCard.tsx", "./src/components/landing/CertificateScrollSection.tsx", "./src/components/landing/JoinOurTeamSection.tsx", "./src/components/TestimonialCard/TestimonialCard.tsx", "./src/components/landing/TestimonialFlowSection.tsx", "./src/components/landing/CommunitySupportSection.tsx", "./src/views/LandingView.tsx", "./node_modules/recharts/types/container/Surface.d.ts", "./node_modules/recharts/types/container/Layer.d.ts", "./node_modules/@types/d3-time/index.d.ts", "./node_modules/@types/d3-scale/index.d.ts", "./node_modules/victory-vendor/d3-scale.d.ts", "./node_modules/recharts/types/cartesian/XAxis.d.ts", "./node_modules/recharts/types/cartesian/YAxis.d.ts", "./node_modules/recharts/types/util/types.d.ts", "./node_modules/recharts/types/component/DefaultLegendContent.d.ts", "./node_modules/recharts/types/util/payload/getUniqPayload.d.ts", "./node_modules/recharts/types/component/Legend.d.ts", "./node_modules/recharts/types/component/DefaultTooltipContent.d.ts", "./node_modules/recharts/types/component/Tooltip.d.ts", "./node_modules/recharts/types/component/ResponsiveContainer.d.ts", "./node_modules/recharts/types/component/Cell.d.ts", "./node_modules/recharts/types/component/Text.d.ts", "./node_modules/recharts/types/component/Label.d.ts", "./node_modules/recharts/types/component/LabelList.d.ts", "./node_modules/recharts/types/component/Customized.d.ts", "./node_modules/recharts/types/shape/Sector.d.ts", "./node_modules/@types/d3-path/index.d.ts", "./node_modules/@types/d3-shape/index.d.ts", "./node_modules/victory-vendor/d3-shape.d.ts", "./node_modules/recharts/types/shape/Curve.d.ts", "./node_modules/recharts/types/shape/Rectangle.d.ts", "./node_modules/recharts/types/shape/Polygon.d.ts", "./node_modules/recharts/types/shape/Dot.d.ts", "./node_modules/recharts/types/shape/Cross.d.ts", "./node_modules/recharts/types/shape/Symbols.d.ts", "./node_modules/recharts/types/polar/PolarGrid.d.ts", "./node_modules/recharts/types/polar/PolarRadiusAxis.d.ts", "./node_modules/recharts/types/polar/PolarAngleAxis.d.ts", "./node_modules/recharts/types/polar/Pie.d.ts", "./node_modules/recharts/types/polar/Radar.d.ts", "./node_modules/recharts/types/polar/RadialBar.d.ts", "./node_modules/recharts/types/cartesian/Brush.d.ts", "./node_modules/recharts/types/util/IfOverflowMatches.d.ts", "./node_modules/recharts/types/cartesian/ReferenceLine.d.ts", "./node_modules/recharts/types/cartesian/ReferenceDot.d.ts", "./node_modules/recharts/types/cartesian/ReferenceArea.d.ts", "./node_modules/recharts/types/cartesian/CartesianAxis.d.ts", "./node_modules/recharts/types/cartesian/CartesianGrid.d.ts", "./node_modules/recharts/types/cartesian/Line.d.ts", "./node_modules/recharts/types/cartesian/Area.d.ts", "./node_modules/recharts/types/util/BarUtils.d.ts", "./node_modules/recharts/types/cartesian/Bar.d.ts", "./node_modules/recharts/types/cartesian/ZAxis.d.ts", "./node_modules/recharts/types/cartesian/ErrorBar.d.ts", "./node_modules/recharts/types/cartesian/Scatter.d.ts", "./node_modules/@types/lodash/common/common.d.ts", "./node_modules/@types/lodash/common/array.d.ts", "./node_modules/@types/lodash/common/collection.d.ts", "./node_modules/@types/lodash/common/date.d.ts", "./node_modules/@types/lodash/common/function.d.ts", "./node_modules/@types/lodash/common/lang.d.ts", "./node_modules/@types/lodash/common/math.d.ts", "./node_modules/@types/lodash/common/number.d.ts", "./node_modules/@types/lodash/common/object.d.ts", "./node_modules/@types/lodash/common/seq.d.ts", "./node_modules/@types/lodash/common/string.d.ts", "./node_modules/@types/lodash/common/util.d.ts", "./node_modules/@types/lodash/index.d.ts", "./node_modules/recharts/types/util/getLegendProps.d.ts", "./node_modules/recharts/types/util/ChartUtils.d.ts", "./node_modules/recharts/types/chart/AccessibilityManager.d.ts", "./node_modules/recharts/types/chart/types.d.ts", "./node_modules/recharts/types/chart/generateCategoricalChart.d.ts", "./node_modules/recharts/types/chart/LineChart.d.ts", "./node_modules/recharts/types/chart/BarChart.d.ts", "./node_modules/recharts/types/chart/PieChart.d.ts", "./node_modules/recharts/types/chart/Treemap.d.ts", "./node_modules/recharts/types/chart/Sankey.d.ts", "./node_modules/recharts/types/chart/RadarChart.d.ts", "./node_modules/recharts/types/chart/ScatterChart.d.ts", "./node_modules/recharts/types/chart/AreaChart.d.ts", "./node_modules/recharts/types/chart/RadialBarChart.d.ts", "./node_modules/recharts/types/chart/ComposedChart.d.ts", "./node_modules/recharts/types/chart/SunburstChart.d.ts", "./node_modules/recharts/types/shape/Trapezoid.d.ts", "./node_modules/recharts/types/numberAxis/Funnel.d.ts", "./node_modules/recharts/types/chart/FunnelChart.d.ts", "./node_modules/recharts/types/util/Global.d.ts", "./node_modules/recharts/types/index.d.ts", "./src/views/DashboardView.tsx", "./src/components/UserMenu.tsx", "./node_modules/jwt-decode/build/esm/index.d.ts", "./src/components/LanguageSelectButton.tsx", "./node_modules/react-icons/lib/iconsManifest.d.ts", "./node_modules/react-icons/lib/iconBase.d.ts", "./node_modules/react-icons/lib/iconContext.d.ts", "./node_modules/react-icons/lib/index.d.ts", "./node_modules/react-icons/fa6/index.d.ts", "./src/components/user/SideMenuMini.tsx", "./node_modules/react-icons/fa/index.d.ts", "./node_modules/styled-components/dist/sheet/types.d.ts", "./node_modules/styled-components/dist/sheet/Sheet.d.ts", "./node_modules/styled-components/dist/sheet/index.d.ts", "./node_modules/styled-components/dist/models/ComponentStyle.d.ts", "./node_modules/styled-components/dist/models/ThemeProvider.d.ts", "./node_modules/styled-components/dist/utils/createWarnTooManyClasses.d.ts", "./node_modules/styled-components/dist/utils/domElements.d.ts", "./node_modules/styled-components/dist/types.d.ts", "./node_modules/styled-components/dist/constructors/constructWithOptions.d.ts", "./node_modules/styled-components/dist/constructors/styled.d.ts", "./node_modules/styled-components/dist/constants.d.ts", "./node_modules/styled-components/dist/constructors/createGlobalStyle.d.ts", "./node_modules/styled-components/dist/constructors/css.d.ts", "./node_modules/styled-components/dist/models/Keyframes.d.ts", "./node_modules/styled-components/dist/constructors/keyframes.d.ts", "./node_modules/styled-components/dist/utils/hoist.d.ts", "./node_modules/styled-components/dist/hoc/withTheme.d.ts", "./node_modules/@types/node/compatibility/disposable.d.ts", "./node_modules/@types/node/compatibility/indexable.d.ts", "./node_modules/@types/node/compatibility/iterators.d.ts", "./node_modules/@types/node/compatibility/index.d.ts", "./node_modules/@types/node/globals.typedarray.d.ts", "./node_modules/@types/node/buffer.buffer.d.ts", "./node_modules/buffer/index.d.ts", "./node_modules/undici-types/header.d.ts", "./node_modules/undici-types/readable.d.ts", "./node_modules/undici-types/file.d.ts", "./node_modules/undici-types/fetch.d.ts", "./node_modules/undici-types/formdata.d.ts", "./node_modules/undici-types/connector.d.ts", "./node_modules/undici-types/client.d.ts", "./node_modules/undici-types/errors.d.ts", "./node_modules/undici-types/dispatcher.d.ts", "./node_modules/undici-types/global-dispatcher.d.ts", "./node_modules/undici-types/global-origin.d.ts", "./node_modules/undici-types/pool-stats.d.ts", "./node_modules/undici-types/pool.d.ts", "./node_modules/undici-types/handlers.d.ts", "./node_modules/undici-types/balanced-pool.d.ts", "./node_modules/undici-types/agent.d.ts", "./node_modules/undici-types/mock-interceptor.d.ts", "./node_modules/undici-types/mock-agent.d.ts", "./node_modules/undici-types/mock-client.d.ts", "./node_modules/undici-types/mock-pool.d.ts", "./node_modules/undici-types/mock-errors.d.ts", "./node_modules/undici-types/proxy-agent.d.ts", "./node_modules/undici-types/env-http-proxy-agent.d.ts", "./node_modules/undici-types/retry-handler.d.ts", "./node_modules/undici-types/retry-agent.d.ts", "./node_modules/undici-types/api.d.ts", "./node_modules/undici-types/interceptors.d.ts", "./node_modules/undici-types/util.d.ts", "./node_modules/undici-types/cookies.d.ts", "./node_modules/undici-types/patch.d.ts", "./node_modules/undici-types/websocket.d.ts", "./node_modules/undici-types/eventsource.d.ts", "./node_modules/undici-types/filereader.d.ts", "./node_modules/undici-types/diagnostics-channel.d.ts", "./node_modules/undici-types/content-type.d.ts", "./node_modules/undici-types/cache.d.ts", "./node_modules/undici-types/index.d.ts", "./node_modules/@types/node/globals.d.ts", "./node_modules/@types/node/assert.d.ts", "./node_modules/@types/node/assert/strict.d.ts", "./node_modules/@types/node/async_hooks.d.ts", "./node_modules/@types/node/buffer.d.ts", "./node_modules/@types/node/child_process.d.ts", "./node_modules/@types/node/cluster.d.ts", "./node_modules/@types/node/console.d.ts", "./node_modules/@types/node/constants.d.ts", "./node_modules/@types/node/crypto.d.ts", "./node_modules/@types/node/dgram.d.ts", "./node_modules/@types/node/diagnostics_channel.d.ts", "./node_modules/@types/node/dns.d.ts", "./node_modules/@types/node/dns/promises.d.ts", "./node_modules/@types/node/domain.d.ts", "./node_modules/@types/node/dom-events.d.ts", "./node_modules/@types/node/events.d.ts", "./node_modules/@types/node/fs.d.ts", "./node_modules/@types/node/fs/promises.d.ts", "./node_modules/@types/node/http.d.ts", "./node_modules/@types/node/http2.d.ts", "./node_modules/@types/node/https.d.ts", "./node_modules/@types/node/inspector.d.ts", "./node_modules/@types/node/module.d.ts", "./node_modules/@types/node/net.d.ts", "./node_modules/@types/node/os.d.ts", "./node_modules/@types/node/path.d.ts", "./node_modules/@types/node/perf_hooks.d.ts", "./node_modules/@types/node/process.d.ts", "./node_modules/@types/node/punycode.d.ts", "./node_modules/@types/node/querystring.d.ts", "./node_modules/@types/node/readline.d.ts", "./node_modules/@types/node/readline/promises.d.ts", "./node_modules/@types/node/repl.d.ts", "./node_modules/@types/node/sea.d.ts", "./node_modules/@types/node/stream.d.ts", "./node_modules/@types/node/stream/promises.d.ts", "./node_modules/@types/node/stream/consumers.d.ts", "./node_modules/@types/node/stream/web.d.ts", "./node_modules/@types/node/string_decoder.d.ts", "./node_modules/@types/node/test.d.ts", "./node_modules/@types/node/timers.d.ts", "./node_modules/@types/node/timers/promises.d.ts", "./node_modules/@types/node/tls.d.ts", "./node_modules/@types/node/trace_events.d.ts", "./node_modules/@types/node/tty.d.ts", "./node_modules/@types/node/url.d.ts", "./node_modules/@types/node/util.d.ts", "./node_modules/@types/node/v8.d.ts", "./node_modules/@types/node/vm.d.ts", "./node_modules/@types/node/wasi.d.ts", "./node_modules/@types/node/worker_threads.d.ts", "./node_modules/@types/node/zlib.d.ts", "./node_modules/@types/node/index.d.ts", "./node_modules/styled-components/dist/models/ServerStyleSheet.d.ts", "./node_modules/@types/stylis/index.d.ts", "./node_modules/styled-components/dist/models/StyleSheetManager.d.ts", "./node_modules/styled-components/dist/utils/isStyledComponent.d.ts", "./node_modules/styled-components/dist/secretInternals.d.ts", "./node_modules/styled-components/dist/base.d.ts", "./node_modules/styled-components/dist/index.d.ts", "./src/components/themeToggle/ThemeToggleButton.tsx", "./src/components/common/Logo.tsx", "./src/components/Navbar.tsx", "./src/types/sidemenu.tsx", "./src/components/SideMenuAdmin.tsx", "./src/views/AdminViewNew.tsx", "./src/layout/index.tsx", "./node_modules/@react-oauth/google/dist/index.d.ts", "./src/views/SignupView.tsx", "./node_modules/react-loader-spinner/dist/types.d.ts", "./src/components/common/Label.tsx", "./src/views/SigninView.tsx", "./src/components/dashboard/PerformanceChart.tsx", "./src/components/admin/AdminDashboard.tsx", "./src/components/admin/UserManagementButton.tsx", "./src/components/admin/UserAccountTable.tsx", "./src/components/user/SideMenuUser.tsx", "./src/layout/UserLayout.tsx", "./src/components/user/ComingSoonLetter.tsx", "./src/components/user/TruckLoader.tsx", "./src/views/ComingSoonView.tsx", "./src/views/ProfileSettingView.tsx", "./src/components/user/UserTradingAccounts.tsx", "./src/components/user/BillingTable.tsx", "./src/components/PaymentSuccess.tsx", "./src/components/page/ContactUs.tsx", "./src/components/page/AboutUs.tsx", "./src/components/page/FAQ.tsx", "./node_modules/motion-utils/dist/index.d.ts", "./node_modules/motion-dom/dist/index.d.ts", "./node_modules/framer-motion/dist/types.d-D0HXPxHm.d.ts", "./node_modules/framer-motion/dist/types/index.d.ts", "./src/utils/empty.tsx", "./src/components/oxapay/Oxapay.tsx", "./src/utils/timeFormatter.tsx", "./src/components/oxapay/Oxapay2.tsx", "./src/views/OxapayView.tsx", "./src/components/page/Disclaimer.tsx", "./src/components/page/TermsAndConditions.tsx", "./src/components/page/PrivacyPolicy.tsx", "./src/components/page/BecomeaPartner.tsx", "./src/components/page/TradingRules.tsx", "./src/components/user/Leaderboard.tsx", "./src/views/ForgotPasswordView.tsx", "./src/views/ResetPasswordView.tsx", "./src/views/Pricing.tsx", "./src/views/CertificatesView.tsx", "./src/components/certificateQRpage/CertificateQRPage.tsx", "./node_modules/@paypal/paypal-js/types/script-options.d.ts", "./node_modules/@paypal/paypal-js/types/apis/openapi/checkout_orders_v2.d.ts", "./node_modules/@paypal/paypal-js/types/apis/orders.d.ts", "./node_modules/@paypal/paypal-js/types/apis/openapi/billing_subscriptions_v1.d.ts", "./node_modules/@paypal/paypal-js/types/apis/subscriptions.d.ts", "./node_modules/@paypal/paypal-js/types/components/funding-eligibility.d.ts", "./node_modules/@paypal/paypal-js/types/components/buttons.d.ts", "./node_modules/@paypal/paypal-js/types/components/marks.d.ts", "./node_modules/@paypal/paypal-js/types/components/messages.d.ts", "./node_modules/@paypal/paypal-js/types/components/hosted-fields.d.ts", "./node_modules/@paypal/paypal-js/types/components/card-fields.d.ts", "./node_modules/@paypal/paypal-js/types/index.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/paypalButtonTypes.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/braintree/commonsTypes.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/braintree/clientTypes.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/enums.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/scriptProviderTypes.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/braintree/paypalCheckout.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/braintreePayPalButtonTypes.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/payPalHostedFieldTypes.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/payPalCardFieldsTypes.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/types/index.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/context/scriptProviderContext.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/hooks/scriptProviderHooks.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/hooks/payPalHostedFieldsHooks.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/PayPalButtons.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/braintree/BraintreePayPalButtons.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/PayPalMarks.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/PayPalMessages.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/PayPalScriptProvider.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/hostedFields/PayPalHostedFieldsProvider.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/hostedFields/PayPalHostedField.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/PayPalCardFieldsProvider.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/PayPalNameField.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/PayPalNumberField.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/PayPalExpiryField.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/PayPalCVVField.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/PayPalCardFieldsForm.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/hooks.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/components/cardFields/context.d.ts", "./node_modules/@paypal/react-paypal-js/dist/types/index.d.ts", "./src/components/paypal/Paypal.tsx", "./src/views/PaypalView.tsx", "./src/views/EmailVerifyView.tsx", "./src/views/CongratForSignUp.tsx", "./node_modules/react-ts-tradingview-widgets/dist/components/AdvancedRealTimeChart.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/CompanyProfile.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/CryptoCurrencyMarket.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/EconomicCalendar.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/ForexCrossRates.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/ForexHeatMap.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/FundamentalData.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/MarketData.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/MarketOverview.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/MiniChart.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/Screener.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/SingleTicker.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/StockMarket.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/SymbolInfo.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/SymbolOverview.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/TechnicalAnalysis.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/Ticker.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/TickerTape.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/Timeline.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/StockHeatmap.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/CryptoCoinsHeatmap.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/Copyright.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/components/index.d.ts", "./node_modules/react-ts-tradingview-widgets/dist/index.d.ts", "./src/components/user/CalendarNews.tsx", "./src/views/AdminDashboardView.tsx", "./src/App.tsx", "./src/locales/en.json", "./src/locales/fr.json", "./src/locales/ge.json", "./src/locales/sp.json", "./src/i18n.ts", "./node_modules/@types/react-dom/client.d.ts", "./src/main.tsx", "./node_modules/vite/types/hmrPayload.d.ts", "./node_modules/vite/types/customEvent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importGlob.d.ts", "./node_modules/vite/types/importMeta.d.ts", "./node_modules/vite/client.d.ts", "./src/vite-env.d.ts", "./src/api/client.ts", "./src/api/meta-api.ts", "./src/api/sellix.ts", "./src/api/telegram.ts", "./node_modules/zod/dist/types/v3/helpers/typeAliases.d.ts", "./node_modules/zod/dist/types/v3/helpers/util.d.ts", "./node_modules/zod/dist/types/v3/ZodError.d.ts", "./node_modules/zod/dist/types/v3/locales/en.d.ts", "./node_modules/zod/dist/types/v3/errors.d.ts", "./node_modules/zod/dist/types/v3/helpers/parseUtil.d.ts", "./node_modules/zod/dist/types/v3/helpers/enumUtil.d.ts", "./node_modules/zod/dist/types/v3/helpers/errorUtil.d.ts", "./node_modules/zod/dist/types/v3/helpers/partialUtil.d.ts", "./node_modules/zod/dist/types/v3/standard-schema.d.ts", "./node_modules/zod/dist/types/v3/types.d.ts", "./node_modules/zod/dist/types/v3/external.d.ts", "./node_modules/zod/dist/types/v3/index.d.ts", "./node_modules/zod/dist/types/index.d.ts", "./src/api/endpoints/auth.ts", "./src/api/endpoints/payments.ts", "./src/api/endpoints/trading.ts", "./src/api/endpoints/webhooks.ts", "./src/components/AlertsList.tsx", "./src/components/AssetBadge.tsx", "./src/components/ChatWindow.tsx", "./src/components/CheckoutModal.tsx", "./src/components/CopyTraderCard.tsx", "./src/components/DynamicSvg.tsx", "./src/components/LanguageSwitcherButton.tsx", "./src/components/Leaderboard.tsx", "./src/components/NewWebhookModal.tsx", "./src/components/PremiumFeatures.tsx", "./src/components/QuickActions.tsx", "./src/components/SideMenu.tsx", "./src/components/SideMenu_M.tsx", "./src/components/TotalOverviewCard.tsx", "./src/components/TradeProgressionCard.tsx", "./src/components/TradeDetailsModal.tsx", "./src/components/TradingStats.tsx", "./src/components/WebhookCard.tsx", "./node_modules/@types/three/src/constants.d.ts", "./node_modules/@types/three/src/core/Layers.d.ts", "./node_modules/@types/three/src/math/Vector2.d.ts", "./node_modules/@types/three/src/math/Matrix3.d.ts", "./node_modules/@types/three/src/core/BufferAttribute.d.ts", "./node_modules/@types/three/src/core/InterleavedBuffer.d.ts", "./node_modules/@types/three/src/core/InterleavedBufferAttribute.d.ts", "./node_modules/@types/three/src/math/Quaternion.d.ts", "./node_modules/@types/three/src/math/Euler.d.ts", "./node_modules/@types/three/src/math/Matrix4.d.ts", "./node_modules/@types/three/src/math/Vector4.d.ts", "./node_modules/@types/three/src/cameras/Camera.d.ts", "./node_modules/@types/three/src/math/ColorManagement.d.ts", "./node_modules/@types/three/src/math/Color.d.ts", "./node_modules/@types/three/src/math/Cylindrical.d.ts", "./node_modules/@types/three/src/math/Spherical.d.ts", "./node_modules/@types/three/src/math/Vector3.d.ts", "./node_modules/@types/three/src/objects/Bone.d.ts", "./node_modules/@types/three/src/math/Interpolant.d.ts", "./node_modules/@types/three/src/math/interpolants/CubicInterpolant.d.ts", "./node_modules/@types/three/src/math/interpolants/DiscreteInterpolant.d.ts", "./node_modules/@types/three/src/math/interpolants/LinearInterpolant.d.ts", "./node_modules/@types/three/src/animation/KeyframeTrack.d.ts", "./node_modules/@types/three/src/animation/AnimationClip.d.ts", "./node_modules/@types/three/src/extras/core/Curve.d.ts", "./node_modules/@types/three/src/extras/core/CurvePath.d.ts", "./node_modules/@types/three/src/extras/core/Path.d.ts", "./node_modules/@types/three/src/extras/core/Shape.d.ts", "./node_modules/@types/three/src/math/Line3.d.ts", "./node_modules/@types/three/src/math/Sphere.d.ts", "./node_modules/@types/three/src/math/Plane.d.ts", "./node_modules/@types/three/src/math/Triangle.d.ts", "./node_modules/@types/three/src/math/Box3.d.ts", "./node_modules/@types/three/src/renderers/common/StorageBufferAttribute.d.ts", "./node_modules/@types/three/src/renderers/common/IndirectStorageBufferAttribute.d.ts", "./node_modules/@types/three/src/core/EventDispatcher.d.ts", "./node_modules/@types/three/src/core/GLBufferAttribute.d.ts", "./node_modules/@types/three/src/core/BufferGeometry.d.ts", "./node_modules/@types/three/src/objects/Group.d.ts", "./node_modules/@types/three/src/textures/DepthTexture.d.ts", "./node_modules/@types/three/src/core/RenderTarget.d.ts", "./node_modules/@types/three/src/textures/CompressedTexture.d.ts", "./node_modules/@types/three/src/textures/CubeTexture.d.ts", "./node_modules/@types/three/src/textures/Source.d.ts", "./node_modules/@types/three/src/textures/Texture.d.ts", "./node_modules/@types/three/src/materials/LineBasicMaterial.d.ts", "./node_modules/@types/three/src/materials/LineDashedMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshBasicMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshDepthMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshDistanceMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshLambertMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshMatcapMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshNormalMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshPhongMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshStandardMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshPhysicalMaterial.d.ts", "./node_modules/@types/three/src/materials/MeshToonMaterial.d.ts", "./node_modules/@types/three/src/materials/PointsMaterial.d.ts", "./node_modules/@types/three/src/core/Uniform.d.ts", "./node_modules/@types/three/src/core/UniformsGroup.d.ts", "./node_modules/@types/three/src/renderers/shaders/UniformsLib.d.ts", "./node_modules/@types/three/src/materials/ShaderMaterial.d.ts", "./node_modules/@types/three/src/materials/RawShaderMaterial.d.ts", "./node_modules/@types/three/src/materials/ShadowMaterial.d.ts", "./node_modules/@types/three/src/materials/SpriteMaterial.d.ts", "./node_modules/@types/three/src/materials/Materials.d.ts", "./node_modules/@types/three/src/objects/Sprite.d.ts", "./node_modules/@types/three/src/math/Frustum.d.ts", "./node_modules/@types/three/src/renderers/WebGLRenderTarget.d.ts", "./node_modules/@types/three/src/lights/LightShadow.d.ts", "./node_modules/@types/three/src/lights/Light.d.ts", "./node_modules/@types/three/src/scenes/Fog.d.ts", "./node_modules/@types/three/src/scenes/FogExp2.d.ts", "./node_modules/@types/three/src/scenes/Scene.d.ts", "./node_modules/@types/three/src/math/Box2.d.ts", "./node_modules/@types/three/src/textures/DataTexture.d.ts", "./node_modules/@types/three/src/textures/Data3DTexture.d.ts", "./node_modules/@types/three/src/textures/DataArrayTexture.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLCapabilities.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLExtensions.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLProperties.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLState.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLUtils.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLTextures.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLUniforms.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLProgram.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLInfo.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLRenderLists.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLObjects.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLShadowMap.d.ts", "./node_modules/@types/webxr/index.d.ts", "./node_modules/@types/three/src/cameras/PerspectiveCamera.d.ts", "./node_modules/@types/three/src/cameras/ArrayCamera.d.ts", "./node_modules/@types/three/src/objects/Mesh.d.ts", "./node_modules/@types/three/src/renderers/webxr/WebXRController.d.ts", "./node_modules/@types/three/src/renderers/webxr/WebXRManager.d.ts", "./node_modules/@types/three/src/renderers/WebGLRenderer.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLAttributes.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLBindingStates.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLClipping.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLCubeMaps.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLLights.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLPrograms.d.ts", "./node_modules/@types/three/src/materials/Material.d.ts", "./node_modules/@types/three/src/objects/Skeleton.d.ts", "./node_modules/@types/three/src/math/Ray.d.ts", "./node_modules/@types/three/src/core/Raycaster.d.ts", "./node_modules/@types/three/src/core/Object3D.d.ts", "./node_modules/@types/three/src/animation/AnimationObjectGroup.d.ts", "./node_modules/@types/three/src/animation/AnimationMixer.d.ts", "./node_modules/@types/three/src/animation/AnimationAction.d.ts", "./node_modules/@types/three/src/animation/AnimationUtils.d.ts", "./node_modules/@types/three/src/animation/PropertyBinding.d.ts", "./node_modules/@types/three/src/animation/PropertyMixer.d.ts", "./node_modules/@types/three/src/animation/tracks/BooleanKeyframeTrack.d.ts", "./node_modules/@types/three/src/animation/tracks/ColorKeyframeTrack.d.ts", "./node_modules/@types/three/src/animation/tracks/NumberKeyframeTrack.d.ts", "./node_modules/@types/three/src/animation/tracks/QuaternionKeyframeTrack.d.ts", "./node_modules/@types/three/src/animation/tracks/StringKeyframeTrack.d.ts", "./node_modules/@types/three/src/animation/tracks/VectorKeyframeTrack.d.ts", "./node_modules/@types/three/src/audio/AudioContext.d.ts", "./node_modules/@types/three/src/audio/AudioListener.d.ts", "./node_modules/@types/three/src/audio/Audio.d.ts", "./node_modules/@types/three/src/audio/AudioAnalyser.d.ts", "./node_modules/@types/three/src/audio/PositionalAudio.d.ts", "./node_modules/@types/three/src/renderers/WebGLCubeRenderTarget.d.ts", "./node_modules/@types/three/src/cameras/CubeCamera.d.ts", "./node_modules/@types/three/src/cameras/OrthographicCamera.d.ts", "./node_modules/@types/three/src/cameras/StereoCamera.d.ts", "./node_modules/@types/three/src/core/Clock.d.ts", "./node_modules/@types/three/src/core/InstancedBufferAttribute.d.ts", "./node_modules/@types/three/src/core/InstancedBufferGeometry.d.ts", "./node_modules/@types/three/src/core/InstancedInterleavedBuffer.d.ts", "./node_modules/@types/three/src/core/RenderTarget3D.d.ts", "./node_modules/@types/three/src/core/RenderTargetArray.d.ts", "./node_modules/@types/three/src/extras/Controls.d.ts", "./node_modules/@types/three/src/extras/core/ShapePath.d.ts", "./node_modules/@types/three/src/extras/curves/EllipseCurve.d.ts", "./node_modules/@types/three/src/extras/curves/ArcCurve.d.ts", "./node_modules/@types/three/src/extras/curves/CatmullRomCurve3.d.ts", "./node_modules/@types/three/src/extras/curves/CubicBezierCurve.d.ts", "./node_modules/@types/three/src/extras/curves/CubicBezierCurve3.d.ts", "./node_modules/@types/three/src/extras/curves/LineCurve.d.ts", "./node_modules/@types/three/src/extras/curves/LineCurve3.d.ts", "./node_modules/@types/three/src/extras/curves/QuadraticBezierCurve.d.ts", "./node_modules/@types/three/src/extras/curves/QuadraticBezierCurve3.d.ts", "./node_modules/@types/three/src/extras/curves/SplineCurve.d.ts", "./node_modules/@types/three/src/extras/curves/Curves.d.ts", "./node_modules/@types/three/src/extras/DataUtils.d.ts", "./node_modules/@types/three/src/extras/ImageUtils.d.ts", "./node_modules/@types/three/src/extras/ShapeUtils.d.ts", "./node_modules/@types/three/src/extras/TextureUtils.d.ts", "./node_modules/@types/three/src/geometries/BoxGeometry.d.ts", "./node_modules/@types/three/src/geometries/CapsuleGeometry.d.ts", "./node_modules/@types/three/src/geometries/CircleGeometry.d.ts", "./node_modules/@types/three/src/geometries/CylinderGeometry.d.ts", "./node_modules/@types/three/src/geometries/ConeGeometry.d.ts", "./node_modules/@types/three/src/geometries/PolyhedronGeometry.d.ts", "./node_modules/@types/three/src/geometries/DodecahedronGeometry.d.ts", "./node_modules/@types/three/src/geometries/EdgesGeometry.d.ts", "./node_modules/@types/three/src/geometries/ExtrudeGeometry.d.ts", "./node_modules/@types/three/src/geometries/IcosahedronGeometry.d.ts", "./node_modules/@types/three/src/geometries/LatheGeometry.d.ts", "./node_modules/@types/three/src/geometries/OctahedronGeometry.d.ts", "./node_modules/@types/three/src/geometries/PlaneGeometry.d.ts", "./node_modules/@types/three/src/geometries/RingGeometry.d.ts", "./node_modules/@types/three/src/geometries/ShapeGeometry.d.ts", "./node_modules/@types/three/src/geometries/SphereGeometry.d.ts", "./node_modules/@types/three/src/geometries/TetrahedronGeometry.d.ts", "./node_modules/@types/three/src/geometries/TorusGeometry.d.ts", "./node_modules/@types/three/src/geometries/TorusKnotGeometry.d.ts", "./node_modules/@types/three/src/geometries/TubeGeometry.d.ts", "./node_modules/@types/three/src/geometries/WireframeGeometry.d.ts", "./node_modules/@types/three/src/geometries/Geometries.d.ts", "./node_modules/@types/three/src/objects/Line.d.ts", "./node_modules/@types/three/src/helpers/ArrowHelper.d.ts", "./node_modules/@types/three/src/objects/LineSegments.d.ts", "./node_modules/@types/three/src/helpers/AxesHelper.d.ts", "./node_modules/@types/three/src/helpers/Box3Helper.d.ts", "./node_modules/@types/three/src/helpers/BoxHelper.d.ts", "./node_modules/@types/three/src/helpers/CameraHelper.d.ts", "./node_modules/@types/three/src/lights/DirectionalLightShadow.d.ts", "./node_modules/@types/three/src/lights/DirectionalLight.d.ts", "./node_modules/@types/three/src/helpers/DirectionalLightHelper.d.ts", "./node_modules/@types/three/src/helpers/GridHelper.d.ts", "./node_modules/@types/three/src/lights/HemisphereLight.d.ts", "./node_modules/@types/three/src/helpers/HemisphereLightHelper.d.ts", "./node_modules/@types/three/src/helpers/PlaneHelper.d.ts", "./node_modules/@types/three/src/lights/PointLightShadow.d.ts", "./node_modules/@types/three/src/lights/PointLight.d.ts", "./node_modules/@types/three/src/helpers/PointLightHelper.d.ts", "./node_modules/@types/three/src/helpers/PolarGridHelper.d.ts", "./node_modules/@types/three/src/objects/SkinnedMesh.d.ts", "./node_modules/@types/three/src/helpers/SkeletonHelper.d.ts", "./node_modules/@types/three/src/helpers/SpotLightHelper.d.ts", "./node_modules/@types/three/src/lights/AmbientLight.d.ts", "./node_modules/@types/three/src/math/SphericalHarmonics3.d.ts", "./node_modules/@types/three/src/lights/LightProbe.d.ts", "./node_modules/@types/three/src/lights/RectAreaLight.d.ts", "./node_modules/@types/three/src/lights/SpotLightShadow.d.ts", "./node_modules/@types/three/src/lights/SpotLight.d.ts", "./node_modules/@types/three/src/loaders/LoadingManager.d.ts", "./node_modules/@types/three/src/loaders/Loader.d.ts", "./node_modules/@types/three/src/loaders/AnimationLoader.d.ts", "./node_modules/@types/three/src/loaders/AudioLoader.d.ts", "./node_modules/@types/three/src/loaders/BufferGeometryLoader.d.ts", "./node_modules/@types/three/src/loaders/Cache.d.ts", "./node_modules/@types/three/src/loaders/CompressedTextureLoader.d.ts", "./node_modules/@types/three/src/loaders/CubeTextureLoader.d.ts", "./node_modules/@types/three/src/loaders/DataTextureLoader.d.ts", "./node_modules/@types/three/src/loaders/FileLoader.d.ts", "./node_modules/@types/three/src/loaders/ImageBitmapLoader.d.ts", "./node_modules/@types/three/src/loaders/ImageLoader.d.ts", "./node_modules/@types/three/src/loaders/LoaderUtils.d.ts", "./node_modules/@types/three/src/loaders/MaterialLoader.d.ts", "./node_modules/@types/three/src/loaders/ObjectLoader.d.ts", "./node_modules/@types/three/src/loaders/TextureLoader.d.ts", "./node_modules/@types/three/src/math/interpolants/QuaternionLinearInterpolant.d.ts", "./node_modules/@types/three/src/math/MathUtils.d.ts", "./node_modules/@types/three/src/math/Matrix2.d.ts", "./node_modules/@types/three/src/objects/BatchedMesh.d.ts", "./node_modules/@types/three/src/objects/InstancedMesh.d.ts", "./node_modules/@types/three/src/objects/LineLoop.d.ts", "./node_modules/@types/three/src/objects/LOD.d.ts", "./node_modules/@types/three/src/objects/Points.d.ts", "./node_modules/@types/three/src/renderers/WebGL3DRenderTarget.d.ts", "./node_modules/@types/three/src/renderers/WebGLArrayRenderTarget.d.ts", "./node_modules/@types/three/src/textures/CanvasTexture.d.ts", "./node_modules/@types/three/src/textures/CompressedArrayTexture.d.ts", "./node_modules/@types/three/src/textures/CompressedCubeTexture.d.ts", "./node_modules/@types/three/src/textures/FramebufferTexture.d.ts", "./node_modules/@types/three/src/textures/VideoTexture.d.ts", "./node_modules/@types/three/src/textures/VideoFrameTexture.d.ts", "./node_modules/@types/three/src/utils.d.ts", "./node_modules/@types/three/src/Three.Core.d.ts", "./node_modules/@types/three/src/extras/PMREMGenerator.d.ts", "./node_modules/@types/three/src/renderers/shaders/ShaderChunk.d.ts", "./node_modules/@types/three/src/renderers/shaders/ShaderLib.d.ts", "./node_modules/@types/three/src/renderers/shaders/UniformsUtils.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLBufferRenderer.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLCubeUVMaps.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLGeometries.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLIndexedBufferRenderer.d.ts", "./node_modules/@types/three/src/renderers/webgl/WebGLShader.d.ts", "./node_modules/@types/three/src/renderers/webxr/WebXRDepthSensing.d.ts", "./node_modules/@types/three/src/Three.d.ts", "./node_modules/@types/three/build/three.module.d.ts", "./node_modules/utility-types/dist/aliases-and-guards.d.ts", "./node_modules/utility-types/dist/mapped-types.d.ts", "./node_modules/utility-types/dist/utility-types.d.ts", "./node_modules/utility-types/dist/functional-helpers.d.ts", "./node_modules/utility-types/dist/index.d.ts", "./node_modules/@types/react-reconciler/index.d.ts", "./node_modules/zustand/esm/vanilla.d.mts", "./node_modules/zustand/esm/react.d.mts", "./node_modules/zustand/esm/index.d.mts", "./node_modules/zustand/esm/traditional.d.mts", "./node_modules/@react-three/fiber/dist/declarations/src/core/store.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/core/reconciler.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/core/utils.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/core/events.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/core/hooks.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/core/loop.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/core/renderer.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/core/index.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/three-types.d.ts", "./node_modules/react-use-measure/dist/index.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/web/Canvas.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/web/events.d.ts", "./node_modules/@react-three/fiber/dist/declarations/src/index.d.ts", "./node_modules/@react-three/fiber/dist/react-three-fiber.cjs.d.ts", "./node_modules/@react-three/drei/helpers/ts-utils.d.ts", "./node_modules/@react-three/drei/web/Html.d.ts", "./node_modules/@react-three/drei/web/CycleRaycast.d.ts", "./node_modules/@react-three/drei/web/useCursor.d.ts", "./node_modules/@react-three/drei/web/Loader.d.ts", "./node_modules/@react-three/drei/web/ScrollControls.d.ts", "./node_modules/@react-three/drei/web/PresentationControls.d.ts", "./node_modules/@react-three/drei/web/KeyboardControls.d.ts", "./node_modules/@react-three/drei/web/Select.d.ts", "./node_modules/@react-three/drei/core/Billboard.d.ts", "./node_modules/@react-three/drei/core/ScreenSpace.d.ts", "./node_modules/@react-three/drei/core/ScreenSizer.d.ts", "./node_modules/three-stdlib/misc/MD2CharacterComplex.d.ts", "./node_modules/three-stdlib/misc/ConvexObjectBreaker.d.ts", "./node_modules/three-stdlib/misc/MorphBlendMesh.d.ts", "./node_modules/three-stdlib/misc/GPUComputationRenderer.d.ts", "./node_modules/three-stdlib/misc/Gyroscope.d.ts", "./node_modules/three-stdlib/misc/MorphAnimMesh.d.ts", "./node_modules/three-stdlib/misc/RollerCoaster.d.ts", "./node_modules/three-stdlib/misc/Timer.d.ts", "./node_modules/three-stdlib/misc/WebGL.d.ts", "./node_modules/three-stdlib/misc/MD2Character.d.ts", "./node_modules/three-stdlib/misc/Volume.d.ts", "./node_modules/three-stdlib/misc/VolumeSlice.d.ts", "./node_modules/three-stdlib/misc/TubePainter.d.ts", "./node_modules/three-stdlib/misc/ProgressiveLightmap.d.ts", "./node_modules/three-stdlib/renderers/CSS2DRenderer.d.ts", "./node_modules/three-stdlib/renderers/CSS3DRenderer.d.ts", "./node_modules/three-stdlib/renderers/Projector.d.ts", "./node_modules/three-stdlib/renderers/SVGRenderer.d.ts", "./node_modules/three-stdlib/textures/FlakesTexture.d.ts", "./node_modules/three-stdlib/modifiers/CurveModifier.d.ts", "./node_modules/three-stdlib/modifiers/SimplifyModifier.d.ts", "./node_modules/three-stdlib/modifiers/EdgeSplitModifier.d.ts", "./node_modules/three-stdlib/modifiers/TessellateModifier.d.ts", "./node_modules/three-stdlib/exporters/GLTFExporter.d.ts", "./node_modules/three-stdlib/exporters/USDZExporter.d.ts", "./node_modules/three-stdlib/exporters/PLYExporter.d.ts", "./node_modules/three-stdlib/exporters/DRACOExporter.d.ts", "./node_modules/three-stdlib/exporters/ColladaExporter.d.ts", "./node_modules/three-stdlib/exporters/MMDExporter.d.ts", "./node_modules/three-stdlib/exporters/STLExporter.d.ts", "./node_modules/three-stdlib/exporters/OBJExporter.d.ts", "./node_modules/three-stdlib/environments/RoomEnvironment.d.ts", "./node_modules/three-stdlib/animation/AnimationClipCreator.d.ts", "./node_modules/three-stdlib/animation/CCDIKSolver.d.ts", "./node_modules/three-stdlib/animation/MMDPhysics.d.ts", "./node_modules/three-stdlib/animation/MMDAnimationHelper.d.ts", "./node_modules/three-stdlib/objects/BatchedMesh.d.ts", "./node_modules/three-stdlib/types/shared.d.ts", "./node_modules/three-stdlib/objects/Reflector.d.ts", "./node_modules/three-stdlib/objects/Refractor.d.ts", "./node_modules/three-stdlib/objects/ShadowMesh.d.ts", "./node_modules/three-stdlib/objects/Lensflare.d.ts", "./node_modules/three-stdlib/objects/Water.d.ts", "./node_modules/three-stdlib/objects/MarchingCubes.d.ts", "./node_modules/three-stdlib/geometries/LightningStrike.d.ts", "./node_modules/three-stdlib/objects/LightningStorm.d.ts", "./node_modules/three-stdlib/objects/ReflectorRTT.d.ts", "./node_modules/three-stdlib/objects/ReflectorForSSRPass.d.ts", "./node_modules/three-stdlib/objects/Sky.d.ts", "./node_modules/three-stdlib/objects/Water2.d.ts", "./node_modules/three-stdlib/objects/GroundProjectedEnv.d.ts", "./node_modules/three-stdlib/utils/SceneUtils.d.ts", "./node_modules/three-stdlib/utils/UVsDebug.d.ts", "./node_modules/three-stdlib/utils/GeometryUtils.d.ts", "./node_modules/three-stdlib/utils/RoughnessMipmapper.d.ts", "./node_modules/three-stdlib/utils/SkeletonUtils.d.ts", "./node_modules/three-stdlib/utils/ShadowMapViewer.d.ts", "./node_modules/three-stdlib/utils/BufferGeometryUtils.d.ts", "./node_modules/three-stdlib/utils/GeometryCompressionUtils.d.ts", "./node_modules/three-stdlib/shaders/BokehShader2.d.ts", "./node_modules/three-stdlib/cameras/CinematicCamera.d.ts", "./node_modules/three-stdlib/math/ConvexHull.d.ts", "./node_modules/three-stdlib/math/MeshSurfaceSampler.d.ts", "./node_modules/three-stdlib/math/SimplexNoise.d.ts", "./node_modules/three-stdlib/math/OBB.d.ts", "./node_modules/three-stdlib/math/Capsule.d.ts", "./node_modules/three-stdlib/math/ColorConverter.d.ts", "./node_modules/three-stdlib/math/ImprovedNoise.d.ts", "./node_modules/three-stdlib/math/Octree.d.ts", "./node_modules/three-stdlib/math/Lut.d.ts", "./node_modules/three-stdlib/controls/EventDispatcher.d.ts", "./node_modules/three-stdlib/controls/experimental/CameraControls.d.ts", "./node_modules/three-stdlib/controls/FirstPersonControls.d.ts", "./node_modules/three-stdlib/controls/TransformControls.d.ts", "./node_modules/three-stdlib/controls/DragControls.d.ts", "./node_modules/three-stdlib/controls/PointerLockControls.d.ts", "./node_modules/three-stdlib/controls/StandardControlsEventMap.d.ts", "./node_modules/three-stdlib/controls/DeviceOrientationControls.d.ts", "./node_modules/three-stdlib/controls/TrackballControls.d.ts", "./node_modules/three-stdlib/controls/OrbitControls.d.ts", "./node_modules/three-stdlib/controls/ArcballControls.d.ts", "./node_modules/three-stdlib/controls/FlyControls.d.ts", "./node_modules/three-stdlib/postprocessing/Pass.d.ts", "./node_modules/three-stdlib/shaders/types.d.ts", "./node_modules/three-stdlib/postprocessing/ShaderPass.d.ts", "./node_modules/three-stdlib/postprocessing/LUTPass.d.ts", "./node_modules/three-stdlib/postprocessing/ClearPass.d.ts", "./node_modules/three-stdlib/shaders/DigitalGlitch.d.ts", "./node_modules/three-stdlib/postprocessing/GlitchPass.d.ts", "./node_modules/three-stdlib/postprocessing/HalftonePass.d.ts", "./node_modules/three-stdlib/postprocessing/SMAAPass.d.ts", "./node_modules/three-stdlib/shaders/FilmShader.d.ts", "./node_modules/three-stdlib/postprocessing/FilmPass.d.ts", "./node_modules/three-stdlib/postprocessing/OutlinePass.d.ts", "./node_modules/three-stdlib/postprocessing/SSAOPass.d.ts", "./node_modules/three-stdlib/postprocessing/SavePass.d.ts", "./node_modules/three-stdlib/postprocessing/BokehPass.d.ts", "./node_modules/three-stdlib/postprocessing/TexturePass.d.ts", "./node_modules/three-stdlib/postprocessing/AdaptiveToneMappingPass.d.ts", "./node_modules/three-stdlib/postprocessing/UnrealBloomPass.d.ts", "./node_modules/three-stdlib/postprocessing/CubeTexturePass.d.ts", "./node_modules/three-stdlib/postprocessing/SAOPass.d.ts", "./node_modules/three-stdlib/shaders/AfterimageShader.d.ts", "./node_modules/three-stdlib/postprocessing/AfterimagePass.d.ts", "./node_modules/three-stdlib/postprocessing/MaskPass.d.ts", "./node_modules/three-stdlib/postprocessing/EffectComposer.d.ts", "./node_modules/three-stdlib/shaders/DotScreenShader.d.ts", "./node_modules/three-stdlib/postprocessing/DotScreenPass.d.ts", "./node_modules/three-stdlib/postprocessing/SSRPass.d.ts", "./node_modules/three-stdlib/postprocessing/SSAARenderPass.d.ts", "./node_modules/three-stdlib/postprocessing/TAARenderPass.d.ts", "./node_modules/three-stdlib/postprocessing/RenderPass.d.ts", "./node_modules/three-stdlib/postprocessing/RenderPixelatedPass.d.ts", "./node_modules/three-stdlib/shaders/ConvolutionShader.d.ts", "./node_modules/three-stdlib/postprocessing/BloomPass.d.ts", "./node_modules/three-stdlib/postprocessing/WaterPass.d.ts", "./node_modules/three-stdlib/webxr/ARButton.d.ts", "./node_modules/three-stdlib/webxr/XRHandMeshModel.d.ts", "./node_modules/three-stdlib/webxr/OculusHandModel.d.ts", "./node_modules/three-stdlib/webxr/OculusHandPointerModel.d.ts", "./node_modules/three-stdlib/webxr/Text2D.d.ts", "./node_modules/three-stdlib/webxr/VRButton.d.ts", "./node_modules/three-stdlib/loaders/DRACOLoader.d.ts", "./node_modules/three-stdlib/loaders/KTX2Loader.d.ts", "./node_modules/three-stdlib/loaders/GLTFLoader.d.ts", "./node_modules/three-stdlib/libs/MotionControllers.d.ts", "./node_modules/three-stdlib/webxr/XRControllerModelFactory.d.ts", "./node_modules/three-stdlib/webxr/XREstimatedLight.d.ts", "./node_modules/three-stdlib/webxr/XRHandPrimitiveModel.d.ts", "./node_modules/three-stdlib/webxr/XRHandModelFactory.d.ts", "./node_modules/three-stdlib/geometries/ParametricGeometry.d.ts", "./node_modules/three-stdlib/geometries/ParametricGeometries.d.ts", "./node_modules/three-stdlib/geometries/ConvexGeometry.d.ts", "./node_modules/three-stdlib/geometries/RoundedBoxGeometry.d.ts", "./node_modules/three-stdlib/geometries/BoxLineGeometry.d.ts", "./node_modules/three-stdlib/geometries/DecalGeometry.d.ts", "./node_modules/three-stdlib/geometries/TeapotGeometry.d.ts", "./node_modules/three-stdlib/loaders/FontLoader.d.ts", "./node_modules/three-stdlib/geometries/TextGeometry.d.ts", "./node_modules/three-stdlib/csm/CSMFrustum.d.ts", "./node_modules/three-stdlib/csm/CSM.d.ts", "./node_modules/three-stdlib/csm/CSMHelper.d.ts", "./node_modules/three-stdlib/csm/CSMShader.d.ts", "./node_modules/three-stdlib/shaders/ACESFilmicToneMappingShader.d.ts", "./node_modules/three-stdlib/shaders/BasicShader.d.ts", "./node_modules/three-stdlib/shaders/BleachBypassShader.d.ts", "./node_modules/three-stdlib/shaders/BlendShader.d.ts", "./node_modules/three-stdlib/shaders/BokehShader.d.ts", "./node_modules/three-stdlib/shaders/BrightnessContrastShader.d.ts", "./node_modules/three-stdlib/shaders/ColorCorrectionShader.d.ts", "./node_modules/three-stdlib/shaders/ColorifyShader.d.ts", "./node_modules/three-stdlib/shaders/CopyShader.d.ts", "./node_modules/three-stdlib/shaders/DOFMipMapShader.d.ts", "./node_modules/three-stdlib/shaders/DepthLimitedBlurShader.d.ts", "./node_modules/three-stdlib/shaders/FXAAShader.d.ts", "./node_modules/three-stdlib/shaders/FocusShader.d.ts", "./node_modules/three-stdlib/shaders/FreiChenShader.d.ts", "./node_modules/three-stdlib/shaders/FresnelShader.d.ts", "./node_modules/three-stdlib/shaders/GammaCorrectionShader.d.ts", "./node_modules/three-stdlib/shaders/GodRaysShader.d.ts", "./node_modules/three-stdlib/shaders/HalftoneShader.d.ts", "./node_modules/three-stdlib/shaders/HorizontalBlurShader.d.ts", "./node_modules/three-stdlib/shaders/HorizontalTiltShiftShader.d.ts", "./node_modules/three-stdlib/shaders/HueSaturationShader.d.ts", "./node_modules/three-stdlib/shaders/KaleidoShader.d.ts", "./node_modules/three-stdlib/shaders/LuminosityHighPassShader.d.ts", "./node_modules/three-stdlib/shaders/LuminosityShader.d.ts", "./node_modules/three-stdlib/shaders/MirrorShader.d.ts", "./node_modules/three-stdlib/shaders/NormalMapShader.d.ts", "./node_modules/three-stdlib/shaders/ParallaxShader.d.ts", "./node_modules/three-stdlib/shaders/PixelShader.d.ts", "./node_modules/three-stdlib/shaders/RGBShiftShader.d.ts", "./node_modules/three-stdlib/shaders/SAOShader.d.ts", "./node_modules/three-stdlib/shaders/SMAAShader.d.ts", "./node_modules/three-stdlib/shaders/SSAOShader.d.ts", "./node_modules/three-stdlib/shaders/SSRShader.d.ts", "./node_modules/three-stdlib/shaders/SepiaShader.d.ts", "./node_modules/three-stdlib/shaders/SobelOperatorShader.d.ts", "./node_modules/three-stdlib/shaders/SubsurfaceScatteringShader.d.ts", "./node_modules/three-stdlib/shaders/TechnicolorShader.d.ts", "./node_modules/three-stdlib/shaders/ToneMapShader.d.ts", "./node_modules/three-stdlib/shaders/ToonShader.d.ts", "./node_modules/three-stdlib/shaders/TriangleBlurShader.d.ts", "./node_modules/three-stdlib/shaders/UnpackDepthRGBAShader.d.ts", "./node_modules/three-stdlib/shaders/VerticalBlurShader.d.ts", "./node_modules/three-stdlib/shaders/VerticalTiltShiftShader.d.ts", "./node_modules/three-stdlib/shaders/VignetteShader.d.ts", "./node_modules/three-stdlib/shaders/VolumeShader.d.ts", "./node_modules/three-stdlib/shaders/WaterRefractionShader.d.ts", "./node_modules/three-stdlib/interactive/HTMLMesh.d.ts", "./node_modules/three-stdlib/interactive/InteractiveGroup.d.ts", "./node_modules/three-stdlib/interactive/SelectionBox.d.ts", "./node_modules/three-stdlib/interactive/SelectionHelper.d.ts", "./node_modules/three-stdlib/physics/AmmoPhysics.d.ts", "./node_modules/three-stdlib/effects/ParallaxBarrierEffect.d.ts", "./node_modules/three-stdlib/effects/PeppersGhostEffect.d.ts", "./node_modules/three-stdlib/effects/OutlineEffect.d.ts", "./node_modules/three-stdlib/effects/AnaglyphEffect.d.ts", "./node_modules/three-stdlib/effects/AsciiEffect.d.ts", "./node_modules/three-stdlib/effects/StereoEffect.d.ts", "./node_modules/three-stdlib/loaders/FBXLoader.d.ts", "./node_modules/three-stdlib/loaders/TGALoader.d.ts", "./node_modules/three-stdlib/loaders/LUTCubeLoader.d.ts", "./node_modules/three-stdlib/loaders/NRRDLoader.d.ts", "./node_modules/three-stdlib/loaders/STLLoader.d.ts", "./node_modules/three-stdlib/loaders/MTLLoader.d.ts", "./node_modules/three-stdlib/loaders/XLoader.d.ts", "./node_modules/three-stdlib/loaders/BVHLoader.d.ts", "./node_modules/three-stdlib/loaders/ColladaLoader.d.ts", "./node_modules/three-stdlib/loaders/KMZLoader.d.ts", "./node_modules/three-stdlib/loaders/VRMLoader.d.ts", "./node_modules/three-stdlib/loaders/VRMLLoader.d.ts", "./node_modules/three-stdlib/loaders/LottieLoader.d.ts", "./node_modules/three-stdlib/loaders/TTFLoader.d.ts", "./node_modules/three-stdlib/loaders/RGBELoader.d.ts", "./node_modules/three-stdlib/loaders/AssimpLoader.d.ts", "./node_modules/three-stdlib/loaders/MDDLoader.d.ts", "./node_modules/three-stdlib/loaders/EXRLoader.d.ts", "./node_modules/three-stdlib/loaders/3MFLoader.d.ts", "./node_modules/three-stdlib/loaders/XYZLoader.d.ts", "./node_modules/three-stdlib/loaders/VTKLoader.d.ts", "./node_modules/three-stdlib/loaders/LUT3dlLoader.d.ts", "./node_modules/three-stdlib/loaders/DDSLoader.d.ts", "./node_modules/three-stdlib/loaders/PVRLoader.d.ts", "./node_modules/three-stdlib/loaders/GCodeLoader.d.ts", "./node_modules/three-stdlib/loaders/BasisTextureLoader.d.ts", "./node_modules/three-stdlib/loaders/TDSLoader.d.ts", "./node_modules/three-stdlib/loaders/LDrawLoader.d.ts", "./node_modules/three-stdlib/loaders/SVGLoader.d.ts", "./node_modules/three-stdlib/loaders/3DMLoader.d.ts", "./node_modules/three-stdlib/loaders/OBJLoader.d.ts", "./node_modules/three-stdlib/loaders/AMFLoader.d.ts", "./node_modules/three-stdlib/loaders/MMDLoader.d.ts", "./node_modules/three-stdlib/loaders/MD2Loader.d.ts", "./node_modules/three-stdlib/loaders/KTXLoader.d.ts", "./node_modules/three-stdlib/loaders/TiltLoader.d.ts", "./node_modules/three-stdlib/loaders/HDRCubeTextureLoader.d.ts", "./node_modules/three-stdlib/loaders/PDBLoader.d.ts", "./node_modules/three-stdlib/loaders/PRWMLoader.d.ts", "./node_modules/three-stdlib/loaders/RGBMLoader.d.ts", "./node_modules/three-stdlib/loaders/VOXLoader.d.ts", "./node_modules/three-stdlib/loaders/PCDLoader.d.ts", "./node_modules/three-stdlib/loaders/LWOLoader.d.ts", "./node_modules/three-stdlib/loaders/PLYLoader.d.ts", "./node_modules/three-stdlib/lines/LineSegmentsGeometry.d.ts", "./node_modules/three-stdlib/lines/LineGeometry.d.ts", "./node_modules/three-stdlib/lines/LineMaterial.d.ts", "./node_modules/three-stdlib/lines/Wireframe.d.ts", "./node_modules/three-stdlib/lines/WireframeGeometry2.d.ts", "./node_modules/three-stdlib/lines/LineSegments2.d.ts", "./node_modules/three-stdlib/lines/Line2.d.ts", "./node_modules/three-stdlib/helpers/LightProbeHelper.d.ts", "./node_modules/three-stdlib/helpers/RaycasterHelper.d.ts", "./node_modules/three-stdlib/helpers/VertexTangentsHelper.d.ts", "./node_modules/three-stdlib/helpers/PositionalAudioHelper.d.ts", "./node_modules/three-stdlib/helpers/VertexNormalsHelper.d.ts", "./node_modules/three-stdlib/helpers/RectAreaLightHelper.d.ts", "./node_modules/three-stdlib/lights/RectAreaLightUniformsLib.d.ts", "./node_modules/three-stdlib/lights/LightProbeGenerator.d.ts", "./node_modules/three-stdlib/curves/NURBSUtils.d.ts", "./node_modules/three-stdlib/curves/NURBSCurve.d.ts", "./node_modules/three-stdlib/curves/NURBSSurface.d.ts", "./node_modules/three-stdlib/curves/CurveExtras.d.ts", "./node_modules/three-stdlib/deprecated/Geometry.d.ts", "./node_modules/three-stdlib/libs/MeshoptDecoder.d.ts", "./node_modules/three-stdlib/index.d.ts", "./node_modules/@react-three/drei/core/Line.d.ts", "./node_modules/@react-three/drei/core/QuadraticBezierLine.d.ts", "./node_modules/@react-three/drei/core/CubicBezierLine.d.ts", "./node_modules/@react-three/drei/core/CatmullRomLine.d.ts", "./node_modules/@react-three/drei/core/PositionalAudio.d.ts", "./node_modules/@react-three/drei/core/Text.d.ts", "./node_modules/@react-three/drei/core/useFont.d.ts", "./node_modules/@react-three/drei/core/Text3D.d.ts", "./node_modules/@react-three/drei/core/Effects.d.ts", "./node_modules/@react-three/drei/core/GradientTexture.d.ts", "./node_modules/@react-three/drei/core/Image.d.ts", "./node_modules/@react-three/drei/core/Edges.d.ts", "./node_modules/@react-three/drei/core/Outlines.d.ts", "./node_modules/meshline/dist/MeshLineGeometry.d.ts", "./node_modules/meshline/dist/MeshLineMaterial.d.ts", "./node_modules/meshline/dist/raycast.d.ts", "./node_modules/meshline/dist/index.d.ts", "./node_modules/@react-three/drei/core/Trail.d.ts", "./node_modules/@react-three/drei/core/Sampler.d.ts", "./node_modules/@react-three/drei/core/ComputedAttribute.d.ts", "./node_modules/@react-three/drei/core/Clone.d.ts", "./node_modules/@react-three/drei/core/MarchingCubes.d.ts", "./node_modules/@react-three/drei/core/Decal.d.ts", "./node_modules/@react-three/drei/core/Svg.d.ts", "./node_modules/@react-three/drei/core/Gltf.d.ts", "./node_modules/@react-three/drei/core/AsciiRenderer.d.ts", "./node_modules/@react-three/drei/core/Splat.d.ts", "./node_modules/@react-three/drei/core/OrthographicCamera.d.ts", "./node_modules/@react-three/drei/core/PerspectiveCamera.d.ts", "./node_modules/@react-three/drei/core/CubeCamera.d.ts", "./node_modules/@react-three/drei/core/DeviceOrientationControls.d.ts", "./node_modules/@react-three/drei/core/FlyControls.d.ts", "./node_modules/@react-three/drei/core/MapControls.d.ts", "./node_modules/@react-three/drei/core/OrbitControls.d.ts", "./node_modules/@react-three/drei/core/TrackballControls.d.ts", "./node_modules/@react-three/drei/core/ArcballControls.d.ts", "./node_modules/@react-three/drei/core/TransformControls.d.ts", "./node_modules/@react-three/drei/core/PointerLockControls.d.ts", "./node_modules/@react-three/drei/core/FirstPersonControls.d.ts", "./node_modules/camera-controls/dist/types.d.ts", "./node_modules/camera-controls/dist/EventDispatcher.d.ts", "./node_modules/camera-controls/dist/CameraControls.d.ts", "./node_modules/camera-controls/dist/index.d.ts", "./node_modules/@react-three/drei/core/CameraControls.d.ts", "./node_modules/@react-three/drei/core/MotionPathControls.d.ts", "./node_modules/@react-three/drei/core/GizmoHelper.d.ts", "./node_modules/@react-three/drei/core/GizmoViewcube.d.ts", "./node_modules/@react-three/drei/core/GizmoViewport.d.ts", "./node_modules/@react-three/drei/core/Grid.d.ts", "./node_modules/@react-three/drei/core/CubeTexture.d.ts", "./node_modules/@react-three/drei/core/Fbx.d.ts", "./node_modules/@react-three/drei/core/Ktx2.d.ts", "./node_modules/@react-three/drei/core/Progress.d.ts", "./node_modules/@react-three/drei/core/Texture.d.ts", "./node_modules/hls.js/dist/hls.d.mts", "./node_modules/@react-three/drei/core/VideoTexture.d.ts", "./node_modules/@react-three/drei/core/useSpriteLoader.d.ts", "./node_modules/@react-three/drei/core/Helper.d.ts", "./node_modules/@react-three/drei/core/Stats.d.ts", "./node_modules/stats-gl/dist/stats-gl.d.ts", "./node_modules/@react-three/drei/core/StatsGl.d.ts", "./node_modules/@react-three/drei/core/useDepthBuffer.d.ts", "./node_modules/@react-three/drei/core/useAspect.d.ts", "./node_modules/@react-three/drei/core/useCamera.d.ts", "./node_modules/detect-gpu/dist/src/index.d.ts", "./node_modules/@react-three/drei/core/DetectGPU.d.ts", "./node_modules/three-mesh-bvh/src/index.d.ts", "./node_modules/@react-three/drei/core/Bvh.d.ts", "./node_modules/@react-three/drei/core/useContextBridge.d.ts", "./node_modules/@react-three/drei/core/useAnimations.d.ts", "./node_modules/@react-three/drei/core/Fbo.d.ts", "./node_modules/@react-three/drei/core/useIntersect.d.ts", "./node_modules/@react-three/drei/core/useBoxProjectedEnv.d.ts", "./node_modules/@react-three/drei/core/BBAnchor.d.ts", "./node_modules/@react-three/drei/core/TrailTexture.d.ts", "./node_modules/@react-three/drei/core/Example.d.ts", "./node_modules/@react-three/drei/core/Instances.d.ts", "./node_modules/@react-three/drei/core/SpriteAnimator.d.ts", "./node_modules/@react-three/drei/core/CurveModifier.d.ts", "./node_modules/@react-three/drei/core/MeshDistortMaterial.d.ts", "./node_modules/@react-three/drei/core/MeshWobbleMaterial.d.ts", "./node_modules/@react-three/drei/materials/MeshReflectorMaterial.d.ts", "./node_modules/@react-three/drei/core/MeshReflectorMaterial.d.ts", "./node_modules/@react-three/drei/materials/MeshRefractionMaterial.d.ts", "./node_modules/@react-three/drei/core/MeshRefractionMaterial.d.ts", "./node_modules/@react-three/drei/core/MeshTransmissionMaterial.d.ts", "./node_modules/@react-three/drei/core/MeshDiscardMaterial.d.ts", "./node_modules/@react-three/drei/core/MultiMaterial.d.ts", "./node_modules/@react-three/drei/core/PointMaterial.d.ts", "./node_modules/@react-three/drei/core/shaderMaterial.d.ts", "./node_modules/@react-three/drei/core/softShadows.d.ts", "./node_modules/@react-three/drei/core/shapes.d.ts", "./node_modules/@react-three/drei/core/RoundedBox.d.ts", "./node_modules/@react-three/drei/core/ScreenQuad.d.ts", "./node_modules/@react-three/drei/core/Center.d.ts", "./node_modules/@react-three/drei/core/Resize.d.ts", "./node_modules/@react-three/drei/core/Bounds.d.ts", "./node_modules/@react-three/drei/core/CameraShake.d.ts", "./node_modules/@react-three/drei/core/Float.d.ts", "./node_modules/@react-three/drei/helpers/environment-assets.d.ts", "./node_modules/@react-three/drei/core/useEnvironment.d.ts", "./node_modules/@react-three/drei/core/Environment.d.ts", "./node_modules/@react-three/drei/core/ContactShadows.d.ts", "./node_modules/@react-three/drei/core/AccumulativeShadows.d.ts", "./node_modules/@react-three/drei/core/Stage.d.ts", "./node_modules/@react-three/drei/core/Backdrop.d.ts", "./node_modules/@react-three/drei/core/Shadow.d.ts", "./node_modules/@react-three/drei/core/Caustics.d.ts", "./node_modules/@react-three/drei/core/SpotLight.d.ts", "./node_modules/@react-three/drei/core/Lightformer.d.ts", "./node_modules/@react-three/drei/core/Sky.d.ts", "./node_modules/@react-three/drei/core/Stars.d.ts", "./node_modules/@react-three/drei/core/Cloud.d.ts", "./node_modules/@react-three/drei/core/Sparkles.d.ts", "./node_modules/@react-three/drei/core/MatcapTexture.d.ts", "./node_modules/@react-three/drei/core/NormalTexture.d.ts", "./node_modules/@react-three/drei/materials/WireframeMaterial.d.ts", "./node_modules/@react-three/drei/core/Wireframe.d.ts", "./node_modules/@react-three/drei/core/ShadowAlpha.d.ts", "./node_modules/@react-three/drei/core/Points.d.ts", "./node_modules/@react-three/drei/core/Segments.d.ts", "./node_modules/@react-three/drei/core/Detailed.d.ts", "./node_modules/@react-three/drei/core/Preload.d.ts", "./node_modules/@react-three/drei/core/BakeShadows.d.ts", "./node_modules/@react-three/drei/core/meshBounds.d.ts", "./node_modules/@react-three/drei/core/AdaptiveDpr.d.ts", "./node_modules/@react-three/drei/core/AdaptiveEvents.d.ts", "./node_modules/@react-three/drei/core/PerformanceMonitor.d.ts", "./node_modules/@react-three/drei/core/RenderTexture.d.ts", "./node_modules/@react-three/drei/core/RenderCubeTexture.d.ts", "./node_modules/@react-three/drei/core/Mask.d.ts", "./node_modules/@react-three/drei/core/Hud.d.ts", "./node_modules/@react-three/drei/core/Fisheye.d.ts", "./node_modules/@react-three/drei/core/MeshPortalMaterial.d.ts", "./node_modules/@react-three/drei/core/calculateScaleFactor.d.ts", "./node_modules/@react-three/drei/core/index.d.ts", "./node_modules/@react-three/drei/web/View.d.ts", "./node_modules/@react-three/drei/web/pivotControls/context.d.ts", "./node_modules/@react-three/drei/web/pivotControls/index.d.ts", "./node_modules/@react-three/drei/web/ScreenVideoTexture.d.ts", "./node_modules/@react-three/drei/web/WebcamVideoTexture.d.ts", "./node_modules/@mediapipe/tasks-vision/vision.d.ts", "./node_modules/@react-three/drei/web/Facemesh.d.ts", "./node_modules/@react-three/drei/web/FaceControls.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types/utils.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types/state.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types/config.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types/internalConfig.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types/handlers.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/config/resolver.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/EventStore.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/TimeoutStore.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/Controller.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/engines/Engine.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types/action.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types/index.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/types.d.ts", "./node_modules/@use-gesture/core/types/dist/use-gesture-core-types.cjs.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/types.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/useDrag.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/usePinch.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/useWheel.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/useScroll.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/useMove.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/useHover.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/useGesture.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/createUseGesture.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/utils/maths.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/utils.d.ts", "./node_modules/@use-gesture/core/utils/dist/use-gesture-core-utils.cjs.d.ts", "./node_modules/@use-gesture/core/dist/declarations/src/actions.d.ts", "./node_modules/@use-gesture/core/actions/dist/use-gesture-core-actions.cjs.d.ts", "./node_modules/@use-gesture/react/dist/declarations/src/index.d.ts", "./node_modules/@use-gesture/react/dist/use-gesture-react.cjs.d.ts", "./node_modules/@react-three/drei/web/DragControls.d.ts", "./node_modules/@react-three/drei/web/FaceLandmarker.d.ts", "./node_modules/@react-three/drei/web/index.d.ts", "./node_modules/@react-three/drei/index.d.ts", "./src/components/3DAnimation/Laptop.tsx", "./src/components/3DAnimation/Scene.tsx", "./src/components/Pricing/ChallengeTypeCheckButton.tsx", "./src/components/Pricing/ChallengeTypeTitleButton.tsx", "./src/components/admin/NewAnnouncementModal.tsx", "./src/components/admin/NewUpsellModal.tsx", "./src/components/admin/UpsellOpportunitiesModal.tsx", "./src/services/sellix.ts", "./src/services/telegram.ts", "./src/hooks/useCheckout.ts", "./src/components/checkout/CheckoutButton.tsx", "./src/components/common/ErrorFallback.tsx", "./src/components/common/ErrorBoundary.tsx", "./src/hooks/useIntersectionObserver.ts", "./src/components/common/LazyImage.tsx", "./src/components/common/LoadingFallback.tsx", "./src/components/common/LoadingSpinner.tsx", "./src/components/dashboard/AccountsOverview.tsx", "./src/components/dashboard/ActiveTrades.tsx", "./src/components/dashboard/DashboardSkeleton.tsx", "./src/components/dashboard/RecentAlerts.tsx", "./src/components/dashboard/SignalProviders.tsx", "./src/components/getfunded/getfundedtest.tsx", "./src/components/heroSection/CheckboxItems.tsx", "./src/components/landing/AddonsModal.tsx", "./src/components/landing/BrokerSection.tsx", "./src/components/landing/CountdownTimer.tsx", "./src/components/landing/DashboardButton.tsx", "./src/components/landing/DemoModal.tsx", "./src/components/landing/DownsellModal.tsx", "./src/components/landing/FeatureGrid.tsx", "./src/components/model/JoinButton.tsx", "./src/components/model/DiscordCard.tsx", "./src/components/model/Subscribe.tsx", "./src/components/landing/FinalSubscribe.tsx", "./src/components/landing/FloatingStats.tsx", "./src/components/landing/InstantAndTwoPhaseButton.tsx", "./src/components/landing/LimitedTimeOffer.tsx", "./src/components/landing/PhaseNumberButton.tsx", "./src/components/landing/UpsellModal.tsx", "./src/components/landing/PricingAddons.tsx", "./src/components/landing/PricingSection.tsx", "./src/components/landing/StatsGrid.tsx", "./src/components/landing/StatsSection.tsx", "./src/components/landing/TestimonialsSection.tsx", "./src/components/landing/TradingChart.tsx", "./src/components/landing/UpsellSection.tsx", "./node_modules/@types/react-dom/index.d.ts", "./src/components/leaderboard/BlurPortal.tsx", "./src/components/leaderboard/CategoryTabs.tsx", "./src/components/leaderboard/GlobalStats.tsx", "./src/components/leaderboard/TraderCard.tsx", "./node_modules/@headlessui/react/dist/types.d.ts", "./node_modules/@headlessui/react/dist/utils/render.d.ts", "./node_modules/@headlessui/react/dist/components/button/button.d.ts", "./node_modules/@headlessui/react/dist/components/checkbox/checkbox.d.ts", "./node_modules/@headlessui/react/dist/components/close-button/close-button.d.ts", "./node_modules/@headlessui/react/dist/hooks/use-by-comparator.d.ts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "./node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "./node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.d.mts", "./node_modules/@floating-ui/react/dist/floating-ui.react.d.mts", "./node_modules/@headlessui/react/dist/internal/floating.d.ts", "./node_modules/@headlessui/react/dist/components/label/label.d.ts", "./node_modules/@headlessui/react/dist/components/combobox/combobox.d.ts", "./node_modules/@headlessui/react/dist/components/data-interactive/data-interactive.d.ts", "./node_modules/@headlessui/react/dist/components/description/description.d.ts", "./node_modules/@headlessui/react/dist/components/dialog/dialog.d.ts", "./node_modules/@headlessui/react/dist/components/disclosure/disclosure.d.ts", "./node_modules/@headlessui/react/dist/components/field/field.d.ts", "./node_modules/@headlessui/react/dist/components/fieldset/fieldset.d.ts", "./node_modules/@headlessui/react/dist/components/focus-trap/focus-trap.d.ts", "./node_modules/@headlessui/react/dist/components/input/input.d.ts", "./node_modules/@headlessui/react/dist/components/legend/legend.d.ts", "./node_modules/@headlessui/react/dist/components/listbox/listbox.d.ts", "./node_modules/@headlessui/react/dist/components/menu/menu.d.ts", "./node_modules/@headlessui/react/dist/components/popover/popover.d.ts", "./node_modules/@headlessui/react/dist/components/portal/portal.d.ts", "./node_modules/@headlessui/react/dist/components/radio-group/radio-group.d.ts", "./node_modules/@headlessui/react/dist/components/select/select.d.ts", "./node_modules/@headlessui/react/dist/components/switch/switch.d.ts", "./node_modules/@headlessui/react/dist/components/tabs/tabs.d.ts", "./node_modules/@headlessui/react/dist/components/textarea/textarea.d.ts", "./node_modules/@headlessui/react/dist/internal/close-provider.d.ts", "./node_modules/@headlessui/react/dist/components/transition/transition.d.ts", "./node_modules/@headlessui/react/dist/index.d.ts", "./node_modules/@heroicons/react/20/solid/AcademicCapIcon.d.ts", "./node_modules/@heroicons/react/20/solid/AdjustmentsHorizontalIcon.d.ts", "./node_modules/@heroicons/react/20/solid/AdjustmentsVerticalIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArchiveBoxArrowDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArchiveBoxXMarkIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArchiveBoxIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowDownCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowDownLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowDownOnSquareStackIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowDownOnSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowDownRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowDownTrayIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLeftCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLeftEndOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLeftOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLeftStartOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLongDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLongLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLongRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowLongUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowPathRoundedSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowPathIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowRightCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowRightEndOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowRightOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowRightStartOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowSmallDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowSmallLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowSmallRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowSmallUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTopRightOnSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTrendingDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTrendingUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnDownLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnDownRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnLeftDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnLeftUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnRightDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnRightUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnUpLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowTurnUpRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUpCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUpLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUpOnSquareStackIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUpOnSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUpRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUpTrayIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUturnDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUturnLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUturnRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowUturnUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowsPointingInIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowsPointingOutIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowsRightLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ArrowsUpDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/AtSymbolIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BackspaceIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BackwardIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BanknotesIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Bars2Icon.d.ts", "./node_modules/@heroicons/react/20/solid/Bars3BottomLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Bars3BottomRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Bars3CenterLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Bars3Icon.d.ts", "./node_modules/@heroicons/react/20/solid/Bars4Icon.d.ts", "./node_modules/@heroicons/react/20/solid/BarsArrowDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BarsArrowUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Battery0Icon.d.ts", "./node_modules/@heroicons/react/20/solid/Battery100Icon.d.ts", "./node_modules/@heroicons/react/20/solid/Battery50Icon.d.ts", "./node_modules/@heroicons/react/20/solid/BeakerIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BellAlertIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BellSlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BellSnoozeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BellIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BoldIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BoltSlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BoltIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BookOpenIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BookmarkSlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BookmarkSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BookmarkIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BriefcaseIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BugAntIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BuildingLibraryIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BuildingOffice2Icon.d.ts", "./node_modules/@heroicons/react/20/solid/BuildingOfficeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/BuildingStorefrontIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CakeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CalculatorIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CalendarDateRangeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CalendarDaysIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CalendarIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CameraIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChartBarSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChartBarIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChartPieIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChatBubbleBottomCenterTextIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChatBubbleBottomCenterIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChatBubbleLeftEllipsisIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChatBubbleLeftRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChatBubbleLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChatBubbleOvalLeftEllipsisIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChatBubbleOvalLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CheckBadgeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CheckCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CheckIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronDoubleDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronDoubleLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronDoubleRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronDoubleUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronUpDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ChevronUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CircleStackIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ClipboardDocumentCheckIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ClipboardDocumentListIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ClipboardDocumentIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ClipboardIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ClockIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CloudArrowDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CloudArrowUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CloudIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CodeBracketSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CodeBracketIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Cog6ToothIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Cog8ToothIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CogIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CommandLineIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ComputerDesktopIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CpuChipIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CreditCardIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CubeTransparentIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CubeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CurrencyBangladeshiIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CurrencyDollarIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CurrencyEuroIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CurrencyPoundIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CurrencyRupeeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CurrencyYenIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CursorArrowRaysIcon.d.ts", "./node_modules/@heroicons/react/20/solid/CursorArrowRippleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DevicePhoneMobileIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DeviceTabletIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DivideIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentArrowDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentArrowUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentChartBarIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentCheckIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentCurrencyBangladeshiIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentCurrencyDollarIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentCurrencyEuroIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentCurrencyPoundIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentCurrencyRupeeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentCurrencyYenIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentDuplicateIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentMagnifyingGlassIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentMinusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentPlusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentTextIcon.d.ts", "./node_modules/@heroicons/react/20/solid/DocumentIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EllipsisHorizontalCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EllipsisHorizontalIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EllipsisVerticalIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EnvelopeOpenIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EnvelopeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EqualsIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ExclamationCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ExclamationTriangleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EyeDropperIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EyeSlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/EyeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FaceFrownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FaceSmileIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FilmIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FingerPrintIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FireIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FlagIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FolderArrowDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FolderMinusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FolderOpenIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FolderPlusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FolderIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ForwardIcon.d.ts", "./node_modules/@heroicons/react/20/solid/FunnelIcon.d.ts", "./node_modules/@heroicons/react/20/solid/GifIcon.d.ts", "./node_modules/@heroicons/react/20/solid/GiftTopIcon.d.ts", "./node_modules/@heroicons/react/20/solid/GiftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/GlobeAltIcon.d.ts", "./node_modules/@heroicons/react/20/solid/GlobeAmericasIcon.d.ts", "./node_modules/@heroicons/react/20/solid/GlobeAsiaAustraliaIcon.d.ts", "./node_modules/@heroicons/react/20/solid/GlobeEuropeAfricaIcon.d.ts", "./node_modules/@heroicons/react/20/solid/H1Icon.d.ts", "./node_modules/@heroicons/react/20/solid/H2Icon.d.ts", "./node_modules/@heroicons/react/20/solid/H3Icon.d.ts", "./node_modules/@heroicons/react/20/solid/HandRaisedIcon.d.ts", "./node_modules/@heroicons/react/20/solid/HandThumbDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/HandThumbUpIcon.d.ts", "./node_modules/@heroicons/react/20/solid/HashtagIcon.d.ts", "./node_modules/@heroicons/react/20/solid/HeartIcon.d.ts", "./node_modules/@heroicons/react/20/solid/HomeModernIcon.d.ts", "./node_modules/@heroicons/react/20/solid/HomeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/IdentificationIcon.d.ts", "./node_modules/@heroicons/react/20/solid/InboxArrowDownIcon.d.ts", "./node_modules/@heroicons/react/20/solid/InboxStackIcon.d.ts", "./node_modules/@heroicons/react/20/solid/InboxIcon.d.ts", "./node_modules/@heroicons/react/20/solid/InformationCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ItalicIcon.d.ts", "./node_modules/@heroicons/react/20/solid/KeyIcon.d.ts", "./node_modules/@heroicons/react/20/solid/LanguageIcon.d.ts", "./node_modules/@heroicons/react/20/solid/LifebuoyIcon.d.ts", "./node_modules/@heroicons/react/20/solid/LightBulbIcon.d.ts", "./node_modules/@heroicons/react/20/solid/LinkSlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/LinkIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ListBulletIcon.d.ts", "./node_modules/@heroicons/react/20/solid/LockClosedIcon.d.ts", "./node_modules/@heroicons/react/20/solid/LockOpenIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MagnifyingGlassCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MagnifyingGlassMinusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MagnifyingGlassPlusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MagnifyingGlassIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MapPinIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MapIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MegaphoneIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MicrophoneIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MinusCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MinusSmallIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MinusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MoonIcon.d.ts", "./node_modules/@heroicons/react/20/solid/MusicalNoteIcon.d.ts", "./node_modules/@heroicons/react/20/solid/NewspaperIcon.d.ts", "./node_modules/@heroicons/react/20/solid/NoSymbolIcon.d.ts", "./node_modules/@heroicons/react/20/solid/NumberedListIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PaintBrushIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PaperAirplaneIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PaperClipIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PauseCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PauseIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PencilSquareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PencilIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PercentBadgeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PhoneArrowDownLeftIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PhoneArrowUpRightIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PhoneXMarkIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PhoneIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PhotoIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PlayCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PlayPauseIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PlayIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PlusCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PlusSmallIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PlusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PowerIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PresentationChartBarIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PresentationChartLineIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PrinterIcon.d.ts", "./node_modules/@heroicons/react/20/solid/PuzzlePieceIcon.d.ts", "./node_modules/@heroicons/react/20/solid/QrCodeIcon.d.ts", "./node_modules/@heroicons/react/20/solid/QuestionMarkCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/QueueListIcon.d.ts", "./node_modules/@heroicons/react/20/solid/RadioIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ReceiptPercentIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ReceiptRefundIcon.d.ts", "./node_modules/@heroicons/react/20/solid/RectangleGroupIcon.d.ts", "./node_modules/@heroicons/react/20/solid/RectangleStackIcon.d.ts", "./node_modules/@heroicons/react/20/solid/RocketLaunchIcon.d.ts", "./node_modules/@heroicons/react/20/solid/RssIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ScaleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ScissorsIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ServerStackIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ServerIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ShareIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ShieldCheckIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ShieldExclamationIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ShoppingBagIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ShoppingCartIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SignalSlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SignalIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SparklesIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SpeakerWaveIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SpeakerXMarkIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Square2StackIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Square3Stack3DIcon.d.ts", "./node_modules/@heroicons/react/20/solid/Squares2X2Icon.d.ts", "./node_modules/@heroicons/react/20/solid/SquaresPlusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/StarIcon.d.ts", "./node_modules/@heroicons/react/20/solid/StopCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/StopIcon.d.ts", "./node_modules/@heroicons/react/20/solid/StrikethroughIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SunIcon.d.ts", "./node_modules/@heroicons/react/20/solid/SwatchIcon.d.ts", "./node_modules/@heroicons/react/20/solid/TableCellsIcon.d.ts", "./node_modules/@heroicons/react/20/solid/TagIcon.d.ts", "./node_modules/@heroicons/react/20/solid/TicketIcon.d.ts", "./node_modules/@heroicons/react/20/solid/TrashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/TrophyIcon.d.ts", "./node_modules/@heroicons/react/20/solid/TruckIcon.d.ts", "./node_modules/@heroicons/react/20/solid/TvIcon.d.ts", "./node_modules/@heroicons/react/20/solid/UnderlineIcon.d.ts", "./node_modules/@heroicons/react/20/solid/UserCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/UserGroupIcon.d.ts", "./node_modules/@heroicons/react/20/solid/UserMinusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/UserPlusIcon.d.ts", "./node_modules/@heroicons/react/20/solid/UserIcon.d.ts", "./node_modules/@heroicons/react/20/solid/UsersIcon.d.ts", "./node_modules/@heroicons/react/20/solid/VariableIcon.d.ts", "./node_modules/@heroicons/react/20/solid/VideoCameraSlashIcon.d.ts", "./node_modules/@heroicons/react/20/solid/VideoCameraIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ViewColumnsIcon.d.ts", "./node_modules/@heroicons/react/20/solid/ViewfinderCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/WalletIcon.d.ts", "./node_modules/@heroicons/react/20/solid/WifiIcon.d.ts", "./node_modules/@heroicons/react/20/solid/WindowIcon.d.ts", "./node_modules/@heroicons/react/20/solid/WrenchScrewdriverIcon.d.ts", "./node_modules/@heroicons/react/20/solid/WrenchIcon.d.ts", "./node_modules/@heroicons/react/20/solid/XCircleIcon.d.ts", "./node_modules/@heroicons/react/20/solid/XMarkIcon.d.ts", "./node_modules/@heroicons/react/20/solid/index.d.ts", "./node_modules/@heroicons/react/16/solid/AcademicCapIcon.d.ts", "./node_modules/@heroicons/react/16/solid/AdjustmentsHorizontalIcon.d.ts", "./node_modules/@heroicons/react/16/solid/AdjustmentsVerticalIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArchiveBoxArrowDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArchiveBoxXMarkIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArchiveBoxIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowDownCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowDownLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowDownOnSquareStackIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowDownOnSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowDownRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowDownTrayIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLeftCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLeftEndOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLeftStartOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLongDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLongLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLongRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowLongUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowPathRoundedSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowPathIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowRightCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowRightEndOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowRightStartOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTopRightOnSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTrendingDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTrendingUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnDownLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnDownRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnLeftDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnLeftUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnRightDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnRightUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnUpLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowTurnUpRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUpCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUpLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUpOnSquareStackIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUpOnSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUpRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUpTrayIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUturnDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUturnLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUturnRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowUturnUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowsPointingInIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowsPointingOutIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowsRightLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ArrowsUpDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/AtSymbolIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BackspaceIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BackwardIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BanknotesIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Bars2Icon.d.ts", "./node_modules/@heroicons/react/16/solid/Bars3BottomLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Bars3BottomRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Bars3CenterLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Bars3Icon.d.ts", "./node_modules/@heroicons/react/16/solid/Bars4Icon.d.ts", "./node_modules/@heroicons/react/16/solid/BarsArrowDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BarsArrowUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Battery0Icon.d.ts", "./node_modules/@heroicons/react/16/solid/Battery100Icon.d.ts", "./node_modules/@heroicons/react/16/solid/Battery50Icon.d.ts", "./node_modules/@heroicons/react/16/solid/BeakerIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BellAlertIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BellSlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BellSnoozeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BellIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BoldIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BoltSlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BoltIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BookOpenIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BookmarkSlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BookmarkSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BookmarkIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BriefcaseIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BugAntIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BuildingLibraryIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BuildingOffice2Icon.d.ts", "./node_modules/@heroicons/react/16/solid/BuildingOfficeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/BuildingStorefrontIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CakeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CalculatorIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CalendarDateRangeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CalendarDaysIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CalendarIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CameraIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChartBarSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChartBarIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChartPieIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChatBubbleBottomCenterTextIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChatBubbleBottomCenterIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChatBubbleLeftEllipsisIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChatBubbleLeftRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChatBubbleLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChatBubbleOvalLeftEllipsisIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChatBubbleOvalLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CheckBadgeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CheckCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CheckIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronDoubleDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronDoubleLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronDoubleRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronDoubleUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronUpDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ChevronUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CircleStackIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ClipboardDocumentCheckIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ClipboardDocumentListIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ClipboardDocumentIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ClipboardIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ClockIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CloudArrowDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CloudArrowUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CloudIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CodeBracketSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CodeBracketIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Cog6ToothIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Cog8ToothIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CogIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CommandLineIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ComputerDesktopIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CpuChipIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CreditCardIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CubeTransparentIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CubeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CurrencyBangladeshiIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CurrencyDollarIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CurrencyEuroIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CurrencyPoundIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CurrencyRupeeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CurrencyYenIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CursorArrowRaysIcon.d.ts", "./node_modules/@heroicons/react/16/solid/CursorArrowRippleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DevicePhoneMobileIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DeviceTabletIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DivideIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentArrowDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentArrowUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentChartBarIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentCheckIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentCurrencyBangladeshiIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentCurrencyDollarIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentCurrencyEuroIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentCurrencyPoundIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentCurrencyRupeeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentCurrencyYenIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentDuplicateIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentMagnifyingGlassIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentMinusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentPlusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentTextIcon.d.ts", "./node_modules/@heroicons/react/16/solid/DocumentIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EllipsisHorizontalCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EllipsisHorizontalIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EllipsisVerticalIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EnvelopeOpenIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EnvelopeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EqualsIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ExclamationCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ExclamationTriangleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EyeDropperIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EyeSlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/EyeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FaceFrownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FaceSmileIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FilmIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FingerPrintIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FireIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FlagIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FolderArrowDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FolderMinusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FolderOpenIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FolderPlusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FolderIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ForwardIcon.d.ts", "./node_modules/@heroicons/react/16/solid/FunnelIcon.d.ts", "./node_modules/@heroicons/react/16/solid/GifIcon.d.ts", "./node_modules/@heroicons/react/16/solid/GiftTopIcon.d.ts", "./node_modules/@heroicons/react/16/solid/GiftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/GlobeAltIcon.d.ts", "./node_modules/@heroicons/react/16/solid/GlobeAmericasIcon.d.ts", "./node_modules/@heroicons/react/16/solid/GlobeAsiaAustraliaIcon.d.ts", "./node_modules/@heroicons/react/16/solid/GlobeEuropeAfricaIcon.d.ts", "./node_modules/@heroicons/react/16/solid/H1Icon.d.ts", "./node_modules/@heroicons/react/16/solid/H2Icon.d.ts", "./node_modules/@heroicons/react/16/solid/H3Icon.d.ts", "./node_modules/@heroicons/react/16/solid/HandRaisedIcon.d.ts", "./node_modules/@heroicons/react/16/solid/HandThumbDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/HandThumbUpIcon.d.ts", "./node_modules/@heroicons/react/16/solid/HashtagIcon.d.ts", "./node_modules/@heroicons/react/16/solid/HeartIcon.d.ts", "./node_modules/@heroicons/react/16/solid/HomeModernIcon.d.ts", "./node_modules/@heroicons/react/16/solid/HomeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/IdentificationIcon.d.ts", "./node_modules/@heroicons/react/16/solid/InboxArrowDownIcon.d.ts", "./node_modules/@heroicons/react/16/solid/InboxStackIcon.d.ts", "./node_modules/@heroicons/react/16/solid/InboxIcon.d.ts", "./node_modules/@heroicons/react/16/solid/InformationCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ItalicIcon.d.ts", "./node_modules/@heroicons/react/16/solid/KeyIcon.d.ts", "./node_modules/@heroicons/react/16/solid/LanguageIcon.d.ts", "./node_modules/@heroicons/react/16/solid/LifebuoyIcon.d.ts", "./node_modules/@heroicons/react/16/solid/LightBulbIcon.d.ts", "./node_modules/@heroicons/react/16/solid/LinkSlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/LinkIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ListBulletIcon.d.ts", "./node_modules/@heroicons/react/16/solid/LockClosedIcon.d.ts", "./node_modules/@heroicons/react/16/solid/LockOpenIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MagnifyingGlassCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MagnifyingGlassMinusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MagnifyingGlassPlusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MagnifyingGlassIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MapPinIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MapIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MegaphoneIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MicrophoneIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MinusCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MinusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MoonIcon.d.ts", "./node_modules/@heroicons/react/16/solid/MusicalNoteIcon.d.ts", "./node_modules/@heroicons/react/16/solid/NewspaperIcon.d.ts", "./node_modules/@heroicons/react/16/solid/NoSymbolIcon.d.ts", "./node_modules/@heroicons/react/16/solid/NumberedListIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PaintBrushIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PaperAirplaneIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PaperClipIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PauseCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PauseIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PencilSquareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PencilIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PercentBadgeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PhoneArrowDownLeftIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PhoneArrowUpRightIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PhoneXMarkIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PhoneIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PhotoIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PlayCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PlayPauseIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PlayIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PlusCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PlusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PowerIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PresentationChartBarIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PresentationChartLineIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PrinterIcon.d.ts", "./node_modules/@heroicons/react/16/solid/PuzzlePieceIcon.d.ts", "./node_modules/@heroicons/react/16/solid/QrCodeIcon.d.ts", "./node_modules/@heroicons/react/16/solid/QuestionMarkCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/QueueListIcon.d.ts", "./node_modules/@heroicons/react/16/solid/RadioIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ReceiptPercentIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ReceiptRefundIcon.d.ts", "./node_modules/@heroicons/react/16/solid/RectangleGroupIcon.d.ts", "./node_modules/@heroicons/react/16/solid/RectangleStackIcon.d.ts", "./node_modules/@heroicons/react/16/solid/RocketLaunchIcon.d.ts", "./node_modules/@heroicons/react/16/solid/RssIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ScaleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ScissorsIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ServerStackIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ServerIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ShareIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ShieldCheckIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ShieldExclamationIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ShoppingBagIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ShoppingCartIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SignalSlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SignalIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SparklesIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SpeakerWaveIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SpeakerXMarkIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Square2StackIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Square3Stack3DIcon.d.ts", "./node_modules/@heroicons/react/16/solid/Squares2X2Icon.d.ts", "./node_modules/@heroicons/react/16/solid/SquaresPlusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/StarIcon.d.ts", "./node_modules/@heroicons/react/16/solid/StopCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/StopIcon.d.ts", "./node_modules/@heroicons/react/16/solid/StrikethroughIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SunIcon.d.ts", "./node_modules/@heroicons/react/16/solid/SwatchIcon.d.ts", "./node_modules/@heroicons/react/16/solid/TableCellsIcon.d.ts", "./node_modules/@heroicons/react/16/solid/TagIcon.d.ts", "./node_modules/@heroicons/react/16/solid/TicketIcon.d.ts", "./node_modules/@heroicons/react/16/solid/TrashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/TrophyIcon.d.ts", "./node_modules/@heroicons/react/16/solid/TruckIcon.d.ts", "./node_modules/@heroicons/react/16/solid/TvIcon.d.ts", "./node_modules/@heroicons/react/16/solid/UnderlineIcon.d.ts", "./node_modules/@heroicons/react/16/solid/UserCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/UserGroupIcon.d.ts", "./node_modules/@heroicons/react/16/solid/UserMinusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/UserPlusIcon.d.ts", "./node_modules/@heroicons/react/16/solid/UserIcon.d.ts", "./node_modules/@heroicons/react/16/solid/UsersIcon.d.ts", "./node_modules/@heroicons/react/16/solid/VariableIcon.d.ts", "./node_modules/@heroicons/react/16/solid/VideoCameraSlashIcon.d.ts", "./node_modules/@heroicons/react/16/solid/VideoCameraIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ViewColumnsIcon.d.ts", "./node_modules/@heroicons/react/16/solid/ViewfinderCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/WalletIcon.d.ts", "./node_modules/@heroicons/react/16/solid/WifiIcon.d.ts", "./node_modules/@heroicons/react/16/solid/WindowIcon.d.ts", "./node_modules/@heroicons/react/16/solid/WrenchScrewdriverIcon.d.ts", "./node_modules/@heroicons/react/16/solid/WrenchIcon.d.ts", "./node_modules/@heroicons/react/16/solid/XCircleIcon.d.ts", "./node_modules/@heroicons/react/16/solid/XMarkIcon.d.ts", "./node_modules/@heroicons/react/16/solid/index.d.ts", "./node_modules/react-icons/md/index.d.ts", "./src/components/markets/MarketStrategyModal.tsx", "./src/components/ui/Tooltip.tsx", "./src/components/webhooks/NewWebhookModal.tsx", "./src/components/markets/MarketChart.tsx", "./src/components/markets/MarketFilters.tsx", "./src/components/markets/MarketStats.tsx", "./src/types/market.tsx", "./src/components/markets/MarketTable.tsx", "./node_modules/react-loading-skeleton/dist/SkeletonStyleProps.d.ts", "./node_modules/react-loading-skeleton/dist/Skeleton.d.ts", "./node_modules/react-loading-skeleton/dist/SkeletonTheme.d.ts", "./node_modules/react-loading-skeleton/dist/index.d.ts", "./src/components/metatrader/AccountCard.tsx", "./node_modules/@heroicons/react/24/outline/AcademicCapIcon.d.ts", "./node_modules/@heroicons/react/24/outline/AdjustmentsHorizontalIcon.d.ts", "./node_modules/@heroicons/react/24/outline/AdjustmentsVerticalIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArchiveBoxArrowDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArchiveBoxXMarkIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArchiveBoxIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowDownCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowDownLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowDownOnSquareStackIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowDownOnSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowDownRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowDownTrayIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLeftCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLeftEndOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLeftOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLeftStartOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLongDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLongLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLongRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowLongUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowPathRoundedSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowPathIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowRightCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowRightEndOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowRightOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowRightStartOnRectangleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowSmallDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowSmallLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowSmallRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowSmallUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTopRightOnSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTrendingDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTrendingUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnDownLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnDownRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnLeftDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnLeftUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnRightDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnRightUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnUpLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowTurnUpRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUpCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUpLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUpOnSquareStackIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUpOnSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUpRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUpTrayIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUturnDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUturnLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUturnRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowUturnUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowsPointingInIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowsPointingOutIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowsRightLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ArrowsUpDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/AtSymbolIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BackspaceIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BackwardIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BanknotesIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Bars2Icon.d.ts", "./node_modules/@heroicons/react/24/outline/Bars3BottomLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Bars3BottomRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Bars3CenterLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Bars3Icon.d.ts", "./node_modules/@heroicons/react/24/outline/Bars4Icon.d.ts", "./node_modules/@heroicons/react/24/outline/BarsArrowDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BarsArrowUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Battery0Icon.d.ts", "./node_modules/@heroicons/react/24/outline/Battery100Icon.d.ts", "./node_modules/@heroicons/react/24/outline/Battery50Icon.d.ts", "./node_modules/@heroicons/react/24/outline/BeakerIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BellAlertIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BellSlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BellSnoozeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BellIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BoldIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BoltSlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BoltIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BookOpenIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BookmarkSlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BookmarkSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BookmarkIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BriefcaseIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BugAntIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BuildingLibraryIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BuildingOffice2Icon.d.ts", "./node_modules/@heroicons/react/24/outline/BuildingOfficeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/BuildingStorefrontIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CakeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CalculatorIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CalendarDateRangeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CalendarDaysIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CalendarIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CameraIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChartBarSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChartBarIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChartPieIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChatBubbleBottomCenterTextIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChatBubbleBottomCenterIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChatBubbleLeftEllipsisIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChatBubbleLeftRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChatBubbleLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChatBubbleOvalLeftEllipsisIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChatBubbleOvalLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CheckBadgeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CheckCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CheckIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronDoubleDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronDoubleLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronDoubleRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronDoubleUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronUpDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ChevronUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CircleStackIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ClipboardDocumentCheckIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ClipboardDocumentListIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ClipboardDocumentIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ClipboardIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ClockIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CloudArrowDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CloudArrowUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CloudIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CodeBracketSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CodeBracketIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Cog6ToothIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Cog8ToothIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CogIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CommandLineIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ComputerDesktopIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CpuChipIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CreditCardIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CubeTransparentIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CubeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CurrencyBangladeshiIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CurrencyDollarIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CurrencyEuroIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CurrencyPoundIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CurrencyRupeeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CurrencyYenIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CursorArrowRaysIcon.d.ts", "./node_modules/@heroicons/react/24/outline/CursorArrowRippleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DevicePhoneMobileIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DeviceTabletIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DivideIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentArrowDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentArrowUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentChartBarIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentCheckIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentCurrencyBangladeshiIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentCurrencyDollarIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentCurrencyEuroIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentCurrencyPoundIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentCurrencyRupeeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentCurrencyYenIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentDuplicateIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentMagnifyingGlassIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentMinusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentPlusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentTextIcon.d.ts", "./node_modules/@heroicons/react/24/outline/DocumentIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EllipsisHorizontalCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EllipsisHorizontalIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EllipsisVerticalIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EnvelopeOpenIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EnvelopeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EqualsIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ExclamationCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ExclamationTriangleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EyeDropperIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EyeSlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/EyeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FaceFrownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FaceSmileIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FilmIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FingerPrintIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FireIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FlagIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FolderArrowDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FolderMinusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FolderOpenIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FolderPlusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FolderIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ForwardIcon.d.ts", "./node_modules/@heroicons/react/24/outline/FunnelIcon.d.ts", "./node_modules/@heroicons/react/24/outline/GifIcon.d.ts", "./node_modules/@heroicons/react/24/outline/GiftTopIcon.d.ts", "./node_modules/@heroicons/react/24/outline/GiftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/GlobeAltIcon.d.ts", "./node_modules/@heroicons/react/24/outline/GlobeAmericasIcon.d.ts", "./node_modules/@heroicons/react/24/outline/GlobeAsiaAustraliaIcon.d.ts", "./node_modules/@heroicons/react/24/outline/GlobeEuropeAfricaIcon.d.ts", "./node_modules/@heroicons/react/24/outline/H1Icon.d.ts", "./node_modules/@heroicons/react/24/outline/H2Icon.d.ts", "./node_modules/@heroicons/react/24/outline/H3Icon.d.ts", "./node_modules/@heroicons/react/24/outline/HandRaisedIcon.d.ts", "./node_modules/@heroicons/react/24/outline/HandThumbDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/HandThumbUpIcon.d.ts", "./node_modules/@heroicons/react/24/outline/HashtagIcon.d.ts", "./node_modules/@heroicons/react/24/outline/HeartIcon.d.ts", "./node_modules/@heroicons/react/24/outline/HomeModernIcon.d.ts", "./node_modules/@heroicons/react/24/outline/HomeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/IdentificationIcon.d.ts", "./node_modules/@heroicons/react/24/outline/InboxArrowDownIcon.d.ts", "./node_modules/@heroicons/react/24/outline/InboxStackIcon.d.ts", "./node_modules/@heroicons/react/24/outline/InboxIcon.d.ts", "./node_modules/@heroicons/react/24/outline/InformationCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ItalicIcon.d.ts", "./node_modules/@heroicons/react/24/outline/KeyIcon.d.ts", "./node_modules/@heroicons/react/24/outline/LanguageIcon.d.ts", "./node_modules/@heroicons/react/24/outline/LifebuoyIcon.d.ts", "./node_modules/@heroicons/react/24/outline/LightBulbIcon.d.ts", "./node_modules/@heroicons/react/24/outline/LinkSlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/LinkIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ListBulletIcon.d.ts", "./node_modules/@heroicons/react/24/outline/LockClosedIcon.d.ts", "./node_modules/@heroicons/react/24/outline/LockOpenIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MagnifyingGlassCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MagnifyingGlassMinusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MagnifyingGlassPlusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MagnifyingGlassIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MapPinIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MapIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MegaphoneIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MicrophoneIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MinusCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MinusSmallIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MinusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MoonIcon.d.ts", "./node_modules/@heroicons/react/24/outline/MusicalNoteIcon.d.ts", "./node_modules/@heroicons/react/24/outline/NewspaperIcon.d.ts", "./node_modules/@heroicons/react/24/outline/NoSymbolIcon.d.ts", "./node_modules/@heroicons/react/24/outline/NumberedListIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PaintBrushIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PaperAirplaneIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PaperClipIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PauseCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PauseIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PencilSquareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PencilIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PercentBadgeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PhoneArrowDownLeftIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PhoneArrowUpRightIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PhoneXMarkIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PhoneIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PhotoIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PlayCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PlayPauseIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PlayIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PlusCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PlusSmallIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PlusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PowerIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PresentationChartBarIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PresentationChartLineIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PrinterIcon.d.ts", "./node_modules/@heroicons/react/24/outline/PuzzlePieceIcon.d.ts", "./node_modules/@heroicons/react/24/outline/QrCodeIcon.d.ts", "./node_modules/@heroicons/react/24/outline/QuestionMarkCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/QueueListIcon.d.ts", "./node_modules/@heroicons/react/24/outline/RadioIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ReceiptPercentIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ReceiptRefundIcon.d.ts", "./node_modules/@heroicons/react/24/outline/RectangleGroupIcon.d.ts", "./node_modules/@heroicons/react/24/outline/RectangleStackIcon.d.ts", "./node_modules/@heroicons/react/24/outline/RocketLaunchIcon.d.ts", "./node_modules/@heroicons/react/24/outline/RssIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ScaleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ScissorsIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ServerStackIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ServerIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ShareIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ShieldCheckIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ShieldExclamationIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ShoppingBagIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ShoppingCartIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SignalSlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SignalIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SparklesIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SpeakerWaveIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SpeakerXMarkIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Square2StackIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Square3Stack3DIcon.d.ts", "./node_modules/@heroicons/react/24/outline/Squares2X2Icon.d.ts", "./node_modules/@heroicons/react/24/outline/SquaresPlusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/StarIcon.d.ts", "./node_modules/@heroicons/react/24/outline/StopCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/StopIcon.d.ts", "./node_modules/@heroicons/react/24/outline/StrikethroughIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SunIcon.d.ts", "./node_modules/@heroicons/react/24/outline/SwatchIcon.d.ts", "./node_modules/@heroicons/react/24/outline/TableCellsIcon.d.ts", "./node_modules/@heroicons/react/24/outline/TagIcon.d.ts", "./node_modules/@heroicons/react/24/outline/TicketIcon.d.ts", "./node_modules/@heroicons/react/24/outline/TrashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/TrophyIcon.d.ts", "./node_modules/@heroicons/react/24/outline/TruckIcon.d.ts", "./node_modules/@heroicons/react/24/outline/TvIcon.d.ts", "./node_modules/@heroicons/react/24/outline/UnderlineIcon.d.ts", "./node_modules/@heroicons/react/24/outline/UserCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/UserGroupIcon.d.ts", "./node_modules/@heroicons/react/24/outline/UserMinusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/UserPlusIcon.d.ts", "./node_modules/@heroicons/react/24/outline/UserIcon.d.ts", "./node_modules/@heroicons/react/24/outline/UsersIcon.d.ts", "./node_modules/@heroicons/react/24/outline/VariableIcon.d.ts", "./node_modules/@heroicons/react/24/outline/VideoCameraSlashIcon.d.ts", "./node_modules/@heroicons/react/24/outline/VideoCameraIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ViewColumnsIcon.d.ts", "./node_modules/@heroicons/react/24/outline/ViewfinderCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/WalletIcon.d.ts", "./node_modules/@heroicons/react/24/outline/WifiIcon.d.ts", "./node_modules/@heroicons/react/24/outline/WindowIcon.d.ts", "./node_modules/@heroicons/react/24/outline/WrenchScrewdriverIcon.d.ts", "./node_modules/@heroicons/react/24/outline/WrenchIcon.d.ts", "./node_modules/@heroicons/react/24/outline/XCircleIcon.d.ts", "./node_modules/@heroicons/react/24/outline/XMarkIcon.d.ts", "./node_modules/@heroicons/react/24/outline/index.d.ts", "./src/components/modal/DeleteModal.tsx", "./src/components/modal/UpdateModal.tsx", "./node_modules/react-icons/lu/index.d.ts", "./node_modules/react-icons/bs/index.d.ts", "./node_modules/react-spinners/helpers/props.d.ts", "./node_modules/react-spinners/BarLoader.d.ts", "./src/components/metatrader/AccountStatsModal.tsx", "./src/components/metatrader/AccountList.tsx", "./src/components/metatrader/AccountMenu.tsx", "./src/components/metatrader/AddAccountModal.tsx", "./src/components/metatrader/MetaTrader.tsx", "./src/components/modal/ConfirmModal.tsx", "./src/components/model/ActionButton.tsx", "./src/components/model/Background.tsx", "./src/components/model/CheckBox.tsx", "./node_modules/swiper/types/shared.d.ts", "./node_modules/swiper/types/modules/a11y.d.ts", "./node_modules/swiper/types/modules/autoplay.d.ts", "./node_modules/swiper/types/modules/controller.d.ts", "./node_modules/swiper/types/modules/effect-coverflow.d.ts", "./node_modules/swiper/types/modules/effect-cube.d.ts", "./node_modules/swiper/types/modules/effect-fade.d.ts", "./node_modules/swiper/types/modules/effect-flip.d.ts", "./node_modules/swiper/types/modules/effect-creative.d.ts", "./node_modules/swiper/types/modules/effect-cards.d.ts", "./node_modules/swiper/types/modules/hash-navigation.d.ts", "./node_modules/swiper/types/modules/history.d.ts", "./node_modules/swiper/types/modules/keyboard.d.ts", "./node_modules/swiper/types/modules/mousewheel.d.ts", "./node_modules/swiper/types/modules/navigation.d.ts", "./node_modules/swiper/types/modules/pagination.d.ts", "./node_modules/swiper/types/modules/parallax.d.ts", "./node_modules/swiper/types/modules/scrollbar.d.ts", "./node_modules/swiper/types/modules/thumbs.d.ts", "./node_modules/swiper/types/modules/virtual.d.ts", "./node_modules/swiper/types/modules/zoom.d.ts", "./node_modules/swiper/types/modules/free-mode.d.ts", "./node_modules/swiper/types/modules/grid.d.ts", "./node_modules/swiper/types/swiper-events.d.ts", "./node_modules/swiper/types/swiper-options.d.ts", "./node_modules/swiper/types/modules/manipulation.d.ts", "./node_modules/swiper/types/swiper-class.d.ts", "./node_modules/swiper/types/modules/public-api.d.ts", "./node_modules/swiper/types/index.d.ts", "./node_modules/swiper/swiper-react.d.ts", "./node_modules/swiper/types/modules/index.d.ts", "./src/components/model/CheckBoxCarousel.tsx", "./src/components/model/EarthModel.tsx", "./src/components/model/StartButton.tsx", "./src/components/model/StartTradingButton.tsx", "./src/hooks/useTelegram.ts", "./src/components/notifications/TelegramConnect.tsx", "./src/components/page/discliamertest.tsx", "./src/components/providers/AppProviders.tsx", "./src/components/tradelocker/TradeLockerAccountList.tsx", "./node_modules/react-icons/ci/index.d.ts", "./src/components/tradelocker/TradeLockerLogin.tsx", "./src/components/trades/AIInsights.tsx", "./src/components/trades/AdvancedAnalytics.tsx", "./src/components/trades/FilterDropdown.tsx", "./src/components/trades/FilterSelect.tsx", "./src/components/trades/LiveTradesCounter.tsx", "./src/components/trades/ProfitCard.tsx", "./src/components/trades/TradeCounts.tsx", "./src/components/trades/TradesList.tsx", "./src/components/trades/LiveTradesPanel.tsx", "./src/components/trades/ModifyTradeModal.tsx", "./src/components/trades/PerformanceMetrics.tsx", "./src/components/trades/QuickStats.tsx", "./src/components/trades/RiskAnalytics.tsx", "./src/components/trades/TotalProfitDisplay.tsx", "./src/components/trades/TradeDetailsChart.tsx", "./src/components/trades/TradeDetailsModal.tsx", "./src/store/tradesFilterStore.ts", "./src/components/trades/TradeFilters.tsx", "./src/components/trades/TradeHistoryTable.tsx", "./src/components/trades/TradeStats.tsx", "./src/components/trades/TradesCalendar.tsx", "./src/components/trades/TradesTable.tsx", "./src/components/trades/TradesView.tsx", "./src/components/trades/index.ts", "./src/components/user/ChallengeAccountSelectionModal.tsx", "./src/components/user/CreateAccountButton.tsx", "./src/constant/webhook.tsx", "./src/components/webhooks/CloseOrderAppModal.tsx", "./src/components/webhooks/CloseOrderMenu.tsx", "./src/components/webhooks/EditCloseOrderModal.tsx", "./src/components/webhooks/CloseOrderCard.tsx", "./src/components/webhooks/DashboardNavbarButton.tsx", "./src/components/webhooks/EditWebhookModal.tsx", "./src/components/webhooks/OpenTradeModal.tsx", "./src/components/webhooks/RiskManagementModal.tsx", "./src/components/webhooks/SetPriceModal.tsx", "./src/components/webhooks/WebhookAppsModal.tsx", "./src/components/webhooks/WebhookMenu.tsx", "./src/components/webhooks/WebhookCard.tsx", "./src/components/webhooks/WebhookColorPicker.tsx", "./src/components/webhooks/WebhookStatsModal.tsx", "./src/config/global.ts", "./src/utils/error-handler.tsx", "./src/hooks/useErrorHandler.ts", "./src/hooks/useLoadingState.ts", "./src/hooks/useMetaAccount.ts", "./src/providers/AppProviders.tsx", "./src/store/queries.ts", "./src/utils/scrollRestore.tsx", "./src/views/AITradeAnalyzerView.tsx", "./src/views/AlertsView.tsx", "./src/views/AnalyticsView.tsx", "./src/views/HistoryView.tsx", "./src/views/LeaderboardView.tsx", "./src/views/MarketsView.tsx", "./src/views/MetaTraderView.tsx", "./src/views/PortfolioView.tsx", "./node_modules/react-icons/gi/index.d.ts", "./src/views/SignalsView.tsx", "./src/views/TelegramView.tsx", "./src/views/TestLoginView.tsx", "./src/views/TradeLockerView.tsx", "./node_modules/react-icons/gr/index.d.ts", "./src/views/TradesView.tsx"], "fileIdsList": [[585, 628, 1644], [585, 628, 1645, 1646], [50, 585, 628, 1647], [50, 585, 628, 1648], [585, 628], [50, 585, 628, 1638, 1639], [50, 585, 628, 1640], [50, 585, 628, 1638, 1639, 1643, 1650, 1651], [50, 585, 628, 1638, 1639, 1654], [50, 585, 628, 1638, 1639, 1651], [50, 585, 628, 1638, 1639, 1650], [50, 585, 628, 1638, 1639, 1643, 1651, 1654], [50, 585, 628, 1638, 1639, 1651, 1654], [585, 628, 1640, 1641, 1642, 1651, 1652, 1653, 1654, 1655, 1656, 1657, 1658, 1659, 1660, 1661, 1662, 1663, 1664, 1665, 1666, 1667, 1668, 1669, 1670, 1671, 1672], [50, 585, 628], [50, 585, 628, 1648, 1649], [50, 585, 628, 1638], [585, 628, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030, 2031, 2032, 2033, 2034, 2035, 2036, 2037, 2038, 2039, 2040, 2041, 2042, 2043, 2044, 2045, 2046, 2047, 2048, 2049, 2050, 2051, 2052, 2053, 2054, 2055, 2056, 2057, 2058, 2059, 2060, 2061, 2062, 2063, 2064, 2065, 2066, 2067, 2068, 2069, 2070, 2071, 2072, 2073, 2074, 2075, 2076, 2077, 2078, 2079, 2080, 2081, 2082, 2083, 2084, 2085, 2086, 2087, 2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096, 2097, 2098, 2099, 2100, 2101, 2102, 2103, 2104, 2105, 2106, 2107, 2108, 2109, 2110, 2111, 2112, 2113, 2114, 2115, 2116, 2117, 2118, 2119, 2120, 2121, 2122, 2123, 2124, 2125, 2126, 2127, 2128, 2129, 2130, 2131, 2132, 2133, 2134, 2135, 2136, 2137, 2138, 2139, 2140, 2141, 2142, 2143, 2144, 2145, 2146, 2147, 2148, 2149, 2150, 2151, 2152, 2153, 2154, 2155, 2156, 2157, 2158, 2159, 2160, 2161, 2162, 2163, 2164, 2165, 2166, 2167, 2168, 2169, 2170, 2171, 2172, 2173, 2174, 2175, 2176, 2177, 2178, 2179, 2180, 2181, 2182, 2183, 2184, 2185, 2186, 2187, 2188, 2189, 2190, 2191, 2192, 2193, 2194, 2195, 2196, 2197, 2198, 2199, 2200, 2201, 2202, 2203, 2204, 2205, 2206, 2207, 2208, 2209, 2210, 2211, 2212, 2213, 2214, 2215, 2216, 2217, 2218, 2219, 2220, 2221, 2222, 2223, 2224, 2225, 2226, 2227, 2228, 2229, 2230, 2231, 2232, 2233, 2234, 2235, 2236, 2237, 2238, 2239, 2240, 2241, 2242, 2243, 2244, 2245, 2246, 2247, 2248, 2249, 2250, 2251, 2252, 2253, 2254, 2255, 2256, 2257, 2258, 2259, 2260, 2261, 2262, 2263, 2264, 2265, 2266, 2267, 2268, 2269, 2270, 2271, 2272, 2273, 2274, 2275, 2276, 2277, 2278, 2279, 2280, 2281, 2282, 2283, 2284, 2285, 2286, 2287, 2288, 2289, 2290, 2291, 2292, 2293, 2294, 2295, 2296, 2297, 2298, 2299, 2300, 2301, 2302, 2303, 2304, 2305, 2306, 2307, 2308, 2309, 2310, 2311, 2312, 2313, 2314], [585, 628, 1674, 1675, 1676, 1677, 1678, 1679, 1680, 1681, 1682, 1683, 1684, 1685, 1686, 1687, 1688, 1689, 1690, 1691, 1692, 1693, 1694, 1695, 1696, 1697, 1698, 1699, 1700, 1701, 1702, 1703, 1704, 1705, 1706, 1707, 1708, 1709, 1710, 1711, 1712, 1713, 1714, 1715, 1716, 1717, 1718, 1719, 1720, 1721, 1722, 1723, 1724, 1725, 1726, 1727, 1728, 1729, 1730, 1731, 1732, 1733, 1734, 1735, 1736, 1737, 1738, 1739, 1740, 1741, 1742, 1743, 1744, 1745, 1746, 1747, 1748, 1749, 1750, 1751, 1752, 1753, 1754, 1755, 1756, 1757, 1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, 1768, 1769, 1770, 1771, 1772, 1773, 1774, 1775, 1776, 1777, 1778, 1779, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1788, 1789, 1790, 1791, 1792, 1793, 1794, 1795, 1796, 1797, 1798, 1799, 1800, 1801, 1802, 1803, 1804, 1805, 1806, 1807, 1808, 1809, 1810, 1811, 1812, 1813, 1814, 1815, 1816, 1817, 1818, 1819, 1820, 1821, 1822, 1823, 1824, 1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, 1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841, 1842, 1843, 1844, 1845, 1846, 1847, 1848, 1849, 1850, 1851, 1852, 1853, 1854, 1855, 1856, 1857, 1858, 1859, 1860, 1861, 1862, 1863, 1864, 1865, 1866, 1867, 1868, 1869, 1870, 1871, 1872, 1873, 1874, 1875, 1876, 1877, 1878, 1879, 1880, 1881, 1882, 1883, 1884, 1885, 1886, 1887, 1888, 1889, 1890, 1891, 1892, 1893, 1894, 1895, 1896, 1897, 1898, 1899, 1900, 1901, 1902, 1903, 1904, 1905, 1906, 1907, 1908, 1909, 1910, 1911, 1912, 1913, 1914, 1915, 1916, 1917, 1918, 1919, 1920, 1921, 1922, 1923, 1924, 1925, 1926, 1927, 1928, 1929, 1930, 1931, 1932, 1933, 1934, 1935, 1936, 1937, 1938, 1939, 1940, 1941, 1942, 1943, 1944, 1945, 1946, 1947, 1948, 1949, 1950, 1951, 1952, 1953, 1954, 1955, 1956, 1957, 1958, 1959, 1960, 1961, 1962, 1963, 1964, 1965, 1966, 1967, 1968, 1969, 1970, 1971, 1972, 1973, 1974, 1975, 1976, 1977, 1978, 1979, 1980, 1981, 1982, 1983, 1984, 1985, 1986, 1987, 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996, 1997], [585, 628, 2330, 2331, 2332, 2333, 2334, 2335, 2336, 2337, 2338, 2339, 2340, 2341, 2342, 2343, 2344, 2345, 2346, 2347, 2348, 2349, 2350, 2351, 2352, 2353, 2354, 2355, 2356, 2357, 2358, 2359, 2360, 2361, 2362, 2363, 2364, 2365, 2366, 2367, 2368, 2369, 2370, 2371, 2372, 2373, 2374, 2375, 2376, 2377, 2378, 2379, 2380, 2381, 2382, 2383, 2384, 2385, 2386, 2387, 2388, 2389, 2390, 2391, 2392, 2393, 2394, 2395, 2396, 2397, 2398, 2399, 2400, 2401, 2402, 2403, 2404, 2405, 2406, 2407, 2408, 2409, 2410, 2411, 2412, 2413, 2414, 2415, 2416, 2417, 2418, 2419, 2420, 2421, 2422, 2423, 2424, 2425, 2426, 2427, 2428, 2429, 2430, 2431, 2432, 2433, 2434, 2435, 2436, 2437, 2438, 2439, 2440, 2441, 2442, 2443, 2444, 2445, 2446, 2447, 2448, 2449, 2450, 2451, 2452, 2453, 2454, 2455, 2456, 2457, 2458, 2459, 2460, 2461, 2462, 2463, 2464, 2465, 2466, 2467, 2468, 2469, 2470, 2471, 2472, 2473, 2474, 2475, 2476, 2477, 2478, 2479, 2480, 2481, 2482, 2483, 2484, 2485, 2486, 2487, 2488, 2489, 2490, 2491, 2492, 2493, 2494, 2495, 2496, 2497, 2498, 2499, 2500, 2501, 2502, 2503, 2504, 2505, 2506, 2507, 2508, 2509, 2510, 2511, 2512, 2513, 2514, 2515, 2516, 2517, 2518, 2519, 2520, 2521, 2522, 2523, 2524, 2525, 2526, 2527, 2528, 2529, 2530, 2531, 2532, 2533, 2534, 2535, 2536, 2537, 2538, 2539, 2540, 2541, 2542, 2543, 2544, 2545, 2546, 2547, 2548, 2549, 2550, 2551, 2552, 2553, 2554, 2555, 2556, 2557, 2558, 2559, 2560, 2561, 2562, 2563, 2564, 2565, 2566, 2567, 2568, 2569, 2570, 2571, 2572, 2573, 2574, 2575, 2576, 2577, 2578, 2579, 2580, 2581, 2582, 2583, 2584, 2585, 2586, 2587, 2588, 2589, 2590, 2591, 2592, 2593, 2594, 2595, 2596, 2597, 2598, 2599, 2600, 2601, 2602, 2603, 2604, 2605, 2606, 2607, 2608, 2609, 2610, 2611, 2612, 2613, 2614, 2615, 2616, 2617, 2618, 2619, 2620, 2621, 2622, 2623, 2624, 2625, 2626, 2627, 2628, 2629, 2630, 2631, 2632, 2633, 2634, 2635, 2636, 2637, 2638, 2639, 2640, 2641, 2642, 2643, 2644, 2645, 2646, 2647, 2648, 2649, 2650, 2651, 2652, 2653], [585, 628, 734], [585, 628, 736], [585, 628, 735, 737, 738], [585, 628, 733, 735, 737, 738, 739, 740, 741, 742, 743], [50, 585, 628, 754], [50, 585, 628, 744], [50, 585, 628, 743], [50, 585, 628, 743, 771], [585, 628, 754, 772], [50, 585, 628, 752], [585, 628, 754], [585, 628, 744, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772], [585, 628, 746], [585, 628, 746, 747, 749], [585, 628, 744, 745, 747, 750], [585, 628, 745, 748, 749, 751, 752, 753], [585, 628, 743], [50, 585, 628, 744, 748, 750], [50, 585, 628, 1105, 1129, 1130, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [585, 628, 1105, 1129, 1130, 1407, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1129, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1129, 1415, 1416, 1418, 1434, 1456, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [585, 628, 1105, 1129, 1130, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [585, 628, 1105, 1129, 1130, 1415, 1416, 1418, 1434, 1450, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [585, 628, 1130], [585, 628, 1130, 1407, 1408], [50, 585, 628, 1105, 1118, 1129, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1474], [585, 628, 1105, 1130, 1407, 1408, 1474], [50, 585, 628, 1105, 1129, 1130, 1407, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1472], [50, 585, 628, 1105, 1129, 1407, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1507, 1508, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1428, 1474], [585, 628, 1129, 1130, 1407, 1415, 1416, 1418, 1434, 1456, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [585, 628, 1105, 1129, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1129, 1407, 1415, 1416, 1418, 1428, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1110, 1474], [50, 585, 628, 1105, 1118, 1129, 1130, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [585, 628, 1129, 1130, 1415, 1416, 1418, 1434, 1456, 1484, 1487, 1488, 1489, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1129, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1491, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [585, 628, 1105, 1474], [50, 585, 628, 1114], [50, 585, 628, 1105, 1118, 1129, 1415, 1416, 1418, 1434, 1456, 1464, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1129, 1415, 1416, 1418, 1434, 1456, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1502, 1507, 1509, 1510, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1130, 1467], [50, 585, 628, 1129, 1130, 1415, 1416, 1418, 1434, 1456, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1129, 1130, 1407, 1414, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1130, 1424, 1474], [50, 585, 628, 1105, 1462, 1474], [50, 585, 628, 1105, 1129, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1524, 1525, 1527, 1528, 1541], [585, 628, 1139, 1140, 1141, 1408, 1409, 1410, 1411, 1412, 1413, 1414, 1415, 1416, 1417, 1418, 1419, 1420, 1425, 1426, 1427, 1428, 1429, 1430, 1431, 1432, 1433, 1434, 1435, 1436, 1437, 1438, 1439, 1440, 1441, 1442, 1443, 1444, 1445, 1446, 1451, 1452, 1453, 1454, 1455, 1456, 1457, 1458, 1459, 1460, 1461, 1463, 1464, 1465, 1466, 1468, 1469, 1470, 1471, 1473, 1475, 1476, 1477, 1478, 1479, 1480, 1481, 1482, 1483, 1484, 1485, 1486, 1487, 1488, 1490, 1492, 1493, 1494, 1495, 1496, 1497, 1498, 1499, 1500, 1501, 1502, 1503, 1504, 1505, 1506, 1508, 1509, 1510, 1511, 1512, 1513, 1514, 1515, 1516, 1517, 1518, 1519, 1520, 1521, 1522, 1523, 1525, 1526, 1527, 1528, 1529, 1530, 1531, 1532, 1533, 1534, 1535, 1536, 1537, 1538, 1539, 1540, 1541, 1542], [585, 628, 1105, 1474, 1507], [585, 628, 1407], [585, 628, 1584], [50, 585, 628, 1105, 1130, 1474, 1581], [50, 585, 628, 1105, 1463, 1474, 1549, 1550], [50, 585, 628, 1549], [50, 585, 628, 1105, 1110, 1129, 1130, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541], [50, 585, 628, 1105, 1462, 1463, 1474], [50, 585, 628, 1105, 1130, 1474], [585, 628, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1543, 1544, 1546, 1547, 1548, 1550, 1551, 1582, 1583], [50, 585, 628, 1105, 1130, 1474, 1545], [585, 628, 1105, 1116, 1118, 1474], [50, 585, 628, 1105, 1116, 1117, 1118, 1474], [585, 628, 1116, 1117, 1118, 1119, 1120, 1121, 1122], [585, 628, 949, 1116], [50, 585, 628, 1105, 1111, 1116, 1118, 1119, 1124, 1474], [50, 585, 628, 1105, 1116, 1117, 1118, 1119, 1124, 1474], [50, 585, 628, 949, 1105, 1114, 1115, 1118, 1119, 1474], [50, 585, 628, 1105, 1111, 1116, 1117, 1474], [585, 628, 1123, 1124, 1126, 1127], [50, 51, 102, 585, 628, 1105, 1118, 1123, 1474], [50, 51, 585, 628, 1123, 1124, 1125], [585, 628, 1116, 1119], [585, 628, 1128], [149, 150, 151, 152, 153, 585, 628], [51, 585, 628, 1124], [111, 585, 628], [110, 111, 585, 628], [110, 111, 112, 113, 114, 115, 116, 117, 118, 585, 628], [110, 111, 112, 585, 628], [50, 119, 585, 628], [50, 51, 585, 628, 1124], [50, 51, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 585, 628, 1124], [119, 120, 585, 628], [119, 585, 628], [119, 120, 129, 585, 628], [119, 120, 122, 585, 628], [471, 585, 628], [489, 585, 628], [518, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 585, 628], [518, 519, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 585, 628], [519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 585, 628], [518, 519, 520, 522, 523, 524, 525, 526, 527, 528, 529, 530, 585, 628], [518, 519, 520, 521, 523, 524, 525, 526, 527, 528, 529, 530, 585, 628], [518, 519, 520, 521, 522, 524, 525, 526, 527, 528, 529, 530, 585, 628], [518, 519, 520, 521, 522, 523, 525, 526, 527, 528, 529, 530, 585, 628], [518, 519, 520, 521, 522, 523, 524, 526, 527, 528, 529, 530, 585, 628], [518, 519, 520, 521, 522, 523, 524, 525, 527, 528, 529, 530, 585, 628], [518, 519, 520, 521, 522, 523, 524, 525, 526, 528, 529, 530, 585, 628], [518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 529, 530, 585, 628], [518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 530, 585, 628], [518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 585, 628], [585, 625, 628], [585, 627, 628], [628], [585, 628, 633, 662], [585, 628, 629, 634, 640, 641, 648, 659, 670], [585, 628, 629, 630, 640, 648], [580, 581, 582, 585, 628], [585, 628, 631, 671], [585, 628, 632, 633, 641, 649], [585, 628, 633, 659, 667], [585, 628, 634, 636, 640, 648], [585, 627, 628, 635], [585, 628, 636, 637], [585, 628, 638, 640], [585, 627, 628, 640], [585, 628, 640, 641, 642, 659, 670], [585, 628, 640, 641, 642, 655, 659, 662], [585, 623, 628], [585, 628, 636, 640, 643, 648, 659, 670], [585, 628, 640, 641, 643, 644, 648, 659, 667, 670], [585, 628, 643, 645, 659, 667, 670], [583, 584, 585, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676], [585, 628, 640, 646], [585, 628, 647, 670, 675], [585, 628, 636, 640, 648, 659], [585, 628, 649], [585, 628, 650], [585, 627, 628, 651], [585, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676], [585, 628, 653], [585, 628, 654], [585, 628, 640, 655, 656], [585, 628, 655, 657, 671, 673], [585, 628, 640, 659, 660, 662], [585, 628, 661, 662], [585, 628, 659, 660], [585, 628, 662], [585, 628, 663], [585, 625, 628, 659, 664], [585, 628, 640, 665, 666], [585, 628, 665, 666], [585, 628, 633, 648, 659, 667], [585, 628, 668], [585, 628, 648, 669], [585, 628, 643, 654, 670], [585, 628, 633, 671], [585, 628, 659, 672], [585, 628, 647, 673], [585, 628, 674], [585, 628, 640, 642, 651, 659, 662, 670, 673, 675], [585, 628, 659, 676], [48, 49, 585, 628], [585, 628, 1104], [585, 628, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 917, 918, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 950, 951, 952, 953, 963, 964, 965, 966, 967, 968, 969, 970, 971, 972, 973, 974, 975, 976, 977, 978, 979, 980, 981, 982, 983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 1006, 1007, 1008, 1009, 1010, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1041, 1042, 1043, 1044, 1045, 1046, 1047, 1048, 1049, 1050, 1051, 1052, 1053, 1054, 1055, 1056, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092], [585, 628, 919, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 953, 954, 955, 956, 957, 958, 959, 960, 961, 1093, 1094, 1095, 1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103], [585, 628, 859, 882, 966, 968], [585, 628, 859, 875, 876, 881, 966], [585, 628, 859, 882, 894, 966, 967, 969], [585, 628, 966], [585, 628, 863, 882], [585, 628, 859, 863, 878, 879, 880], [585, 628, 963, 966], [585, 628, 971], [585, 628, 881], [585, 628, 859, 881], [585, 628, 966, 979, 980], [585, 628, 981], [585, 628, 966, 979], [585, 628, 980, 981], [585, 628, 950], [585, 628, 859, 860, 868, 869, 875, 966], [585, 628, 859, 870, 899, 966, 984], [585, 628, 870, 966], [585, 628, 861, 870, 966], [585, 628, 870, 950], [585, 628, 859, 862, 868], [585, 628, 861, 863, 865, 866, 868, 875, 888, 891, 893, 894, 895], [585, 628, 863], [585, 628, 896], [585, 628, 863, 864], [585, 628, 859, 863, 865], [585, 628, 862, 863, 864, 868], [585, 628, 860, 862, 866, 867, 868, 870, 875, 882, 886, 894, 896, 897, 902, 903, 932, 955, 962, 963, 965], [585, 628, 860, 861, 870, 875, 953, 964, 966], [585, 628, 859, 869, 894, 898, 903], [585, 628, 899], [585, 628, 859, 894, 917], [585, 628, 894, 966], [585, 628, 875, 901, 903, 927, 932, 955], [585, 628, 861], [585, 628, 859, 903], [585, 628, 861, 875], [585, 628, 861, 875, 883], [585, 628, 861, 884], [585, 628, 861, 885], [585, 628, 861, 872, 885, 886], [585, 628, 996], [585, 628, 875, 883], [585, 628, 861, 883], [585, 628, 996, 997, 998, 999, 1000, 1001, 1002, 1003, 1004, 1005], [585, 628, 1014], [585, 628, 1016], [585, 628, 861, 875, 883, 886, 896], [585, 628, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031], [585, 628, 861, 896], [585, 628, 886, 896], [585, 628, 875, 883, 896], [585, 628, 872, 875, 952, 966, 1033], [585, 628, 872, 1035], [585, 628, 872, 891, 1035], [585, 628, 872, 896, 904, 966, 1035], [585, 628, 868, 870, 872, 1035], [585, 628, 868, 872, 966, 1033, 1041], [585, 628, 872, 896, 904, 1035], [585, 628, 868, 872, 906, 966, 1044], [585, 628, 889, 1035], [585, 628, 868, 872, 966, 1048], [585, 628, 868, 876, 966, 1035, 1051], [585, 628, 868, 872, 929, 966, 1035], [585, 628, 872, 929], [585, 628, 872, 875, 929, 966, 1040], [585, 628, 928, 986], [585, 628, 872, 875, 929], [585, 628, 872, 928, 966], [585, 628, 929, 1055], [585, 628, 861, 868, 869, 870, 926, 927, 929, 966], [585, 628, 872, 929, 1047], [585, 628, 928, 929, 950], [585, 628, 872, 875, 903, 929, 966, 1058], [585, 628, 928, 950], [585, 628, 882, 1060, 1061], [585, 628, 1060, 1061], [585, 628, 896, 990, 1060, 1061], [585, 628, 900, 1060, 1061], [585, 628, 901, 1060, 1061], [585, 628, 934, 1060, 1061], [585, 628, 1060], [585, 628, 1061], [585, 628, 903, 962, 1060, 1061], [585, 628, 882, 896, 902, 903, 962, 966, 990, 1060, 1061], [585, 628, 903, 1060, 1061], [585, 628, 872, 903, 962], [585, 628, 904], [585, 628, 859, 870, 872, 889, 894, 896, 897, 932, 955, 961, 966, 1104], [585, 628, 904, 905, 906, 907, 908, 909, 910, 911, 912, 913, 914, 915, 916, 920, 921, 922, 923, 962], [585, 628, 859, 867, 872, 903, 962], [585, 628, 859, 903, 962], [585, 628, 875, 903, 962], [585, 628, 859, 861, 867, 872, 903, 962], [585, 628, 859, 861, 872, 903, 962], [585, 628, 859, 861, 903, 962], [585, 628, 861, 872, 903, 913], [585, 628, 920], [585, 628, 859, 861, 862, 868, 869, 875, 918, 919, 962, 966], [585, 628, 872, 962], [585, 628, 863, 868, 875, 888, 889, 890, 966], [585, 628, 862, 863, 865, 871, 875], [585, 628, 859, 862, 872, 875], [585, 628, 875], [585, 628, 866, 868, 875], [585, 628, 859, 868, 875, 888, 889, 891, 925, 966], [585, 628, 868, 875], [585, 628, 866], [585, 628, 861, 868, 875], [585, 628, 859, 862, 866, 867, 875], [585, 628, 862, 868, 875, 887, 888, 891], [585, 628, 863, 865, 867, 868, 875], [585, 628, 868, 875, 888, 889, 891], [585, 628, 868, 875, 889, 891], [585, 628, 861, 863, 865, 869, 875, 889, 891], [585, 628, 862, 863], [585, 628, 862, 863, 865, 866, 867, 868, 870, 872, 873, 874], [585, 628, 863, 866, 868], [585, 628, 877], [585, 628, 868, 870, 872, 888, 891, 896, 952, 962], [585, 628, 863, 868, 872, 888, 891, 896, 934, 952, 962, 966, 989], [585, 628, 896, 962, 966], [585, 628, 896, 962, 966, 1033], [585, 628, 875, 896, 962, 966], [585, 628, 868, 876, 934], [585, 628, 859, 868, 875, 888, 891, 896, 952, 962, 963, 966], [585, 628, 861, 896, 924, 966], [585, 628, 899, 927, 935], [585, 628, 899, 927, 936], [585, 628, 899, 901, 903, 927, 955], [585, 628, 899, 903], [585, 628, 859, 861, 863, 869, 870, 872, 875, 889, 891, 896, 903, 927, 932, 933, 935, 936, 937, 938, 939, 940, 944, 945, 946, 948, 954, 962, 966], [585, 628, 863, 892], [585, 628, 919], [585, 628, 861, 862, 872], [585, 628, 918, 919], [585, 628, 863, 865, 895], [585, 628, 863, 896, 944, 956, 962, 966], [585, 628, 938, 945], [585, 628, 859], [585, 628, 870, 889, 939, 962], [585, 628, 955], [585, 628, 903, 955], [585, 628, 863, 896, 945, 956, 966], [585, 628, 944], [585, 628, 938], [585, 628, 943, 955], [585, 628, 859, 919, 929, 932, 937, 938, 944, 955, 957, 958, 959, 960, 962, 966], [585, 628, 870, 896, 897, 932, 939, 944, 962, 966], [585, 628, 859, 870, 929, 932, 937, 947, 955], [585, 628, 859, 869, 927, 938, 962], [585, 628, 937, 938, 939, 940, 941, 945], [585, 628, 942, 944], [585, 628, 859, 938], [585, 628, 875, 897, 966], [585, 628, 903, 952, 954, 955], [585, 628, 869, 894, 903, 949, 950, 951, 952, 953, 955], [585, 628, 872], [585, 628, 867, 872, 901, 903, 930, 931, 962, 966], [585, 628, 859, 900], [585, 628, 859, 863, 903], [585, 628, 859, 903, 934], [585, 628, 859, 903, 935], [585, 628, 859, 861, 862, 894, 899, 900, 901, 902], [585, 628, 859, 1090], [585, 628, 1578], [585, 628, 1558, 1559, 1564], [585, 628, 1560, 1564], [585, 628, 1557, 1564], [585, 628, 1564], [585, 628, 1558, 1559, 1560, 1564], [585, 628, 1563], [585, 628, 1554, 1557, 1560, 1561], [585, 628, 1552, 1553], [585, 628, 1552, 1553, 1554], [585, 628, 1552, 1553, 1554, 1555, 1556, 1562], [585, 628, 1552, 1554], [585, 628, 1575], [585, 628, 1576], [585, 628, 1565, 1566], [585, 628, 1565, 1567, 1568, 1569, 1570, 1571, 1572, 1573, 1574, 1577, 1579], [50, 585, 628, 1565], [585, 628, 1580], [585, 628, 1105, 1447, 1448, 1474], [585, 628, 1448, 1449], [185, 585, 628], [183, 185, 585, 628], [183, 585, 628], [185, 249, 250, 585, 628], [252, 585, 628], [253, 585, 628], [270, 585, 628], [185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 585, 628], [346, 585, 628], [185, 250, 370, 585, 628], [183, 367, 368, 585, 628], [367, 585, 628], [369, 585, 628], [183, 184, 585, 628], [50, 51, 585, 628, 713, 714, 1124], [50, 51, 585, 628, 713, 714, 715, 1124], [95, 96, 97, 98, 585, 628], [95, 96, 97, 585, 628], [95, 585, 628], [95, 96, 585, 628], [63, 68, 585, 628], [64, 65, 66, 67, 585, 628], [50, 63, 585, 628], [63, 65, 585, 628], [63, 64, 585, 628], [86, 87, 88, 89, 585, 628], [63, 68, 85, 585, 628], [85, 90, 585, 628], [60, 61, 62, 585, 628], [60, 585, 628], [61, 585, 628], [59, 61, 585, 628], [71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 585, 628], [63, 585, 628], [63, 71, 585, 628], [585, 628, 1421, 1422, 1423], [585, 628, 1105, 1422, 1474], [585, 628, 713], [50, 99, 102, 585, 628], [102, 585, 628], [50, 94, 99, 100, 101, 102, 585, 628, 1124], [99, 102, 585, 628], [559, 585, 628], [556, 557, 558, 585, 628], [50, 585, 628, 2325], [585, 628, 2325, 2326, 2327], [50, 149, 585, 628], [54, 57, 585, 628], [50, 52, 53, 585, 628], [50, 52, 53, 55, 56, 585, 628], [50, 52, 585, 628], [585, 628, 2659], [50, 585, 628, 801], [585, 628, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799], [50, 585, 628, 800], [50, 474, 475, 476, 492, 495, 585, 628], [50, 474, 475, 476, 485, 493, 513, 585, 628], [50, 473, 476, 585, 628], [50, 476, 585, 628], [50, 474, 475, 476, 585, 628], [50, 474, 475, 476, 511, 514, 517, 585, 628], [50, 474, 475, 476, 485, 492, 495, 585, 628], [50, 474, 475, 476, 485, 493, 505, 585, 628], [50, 474, 475, 476, 485, 495, 505, 585, 628], [50, 474, 475, 476, 485, 505, 585, 628], [50, 474, 475, 476, 480, 486, 492, 497, 515, 516, 585, 628], [476, 585, 628], [50, 476, 530, 533, 534, 535, 585, 628], [50, 476, 493, 585, 628], [50, 476, 530, 532, 533, 534, 585, 628], [50, 476, 532, 585, 628], [50, 476, 485, 585, 628], [50, 476, 477, 478, 585, 628], [50, 476, 478, 480, 585, 628], [469, 470, 474, 475, 476, 477, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 506, 507, 508, 509, 510, 511, 512, 514, 515, 516, 517, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 585, 628], [50, 476, 547, 585, 628], [50, 476, 488, 585, 628], [50, 476, 495, 499, 500, 585, 628], [50, 476, 486, 488, 585, 628], [50, 476, 491, 585, 628], [50, 476, 514, 585, 628], [50, 476, 491, 531, 585, 628], [50, 479, 532, 585, 628], [50, 473, 474, 475, 585, 628], [156, 585, 628], [157, 174, 585, 628], [158, 174, 585, 628], [159, 174, 585, 628], [160, 174, 585, 628], [156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 585, 628], [161, 174, 585, 628], [50, 162, 174, 585, 628], [149, 163, 164, 174, 585, 628], [149, 164, 174, 585, 628], [149, 165, 174, 585, 628], [166, 174, 585, 628], [167, 175, 585, 628], [168, 175, 585, 628], [169, 175, 585, 628], [170, 174, 585, 628], [171, 174, 585, 628], [172, 174, 585, 628], [173, 174, 585, 628], [149, 174, 585, 628], [149, 585, 628], [567, 570, 573, 574, 575, 577, 579, 585, 628, 678, 680, 681, 682], [50, 570, 585, 628], [570, 585, 628], [570, 576, 585, 628], [50, 570, 571, 585, 628], [50, 570, 578, 585, 628], [570, 572, 585, 628, 683], [565, 570, 585, 628], [50, 565, 585, 628, 659, 677], [50, 565, 570, 585, 628, 679], [565, 585, 628], [563, 570, 585, 628], [564, 585, 628], [49, 50, 566, 567, 568, 569, 585, 628], [50, 585, 628, 2698], [585, 628, 2670, 2693, 2694, 2696, 2697], [585, 628, 2696], [585, 628, 2670], [585, 628, 2670, 2696], [585, 628, 2671, 2672, 2673, 2674, 2675, 2676, 2677, 2678, 2679, 2680, 2681, 2682, 2683, 2684, 2685, 2686, 2687, 2688, 2689, 2690, 2691, 2692, 2695], [585, 628, 2698], [585, 628, 2670, 2671, 2672, 2673, 2674, 2675, 2676, 2677, 2678, 2679, 2680, 2681, 2682, 2683, 2684, 2685, 2686, 2687, 2688, 2689, 2690, 2691, 2693, 2694, 2695], [585, 628, 2671, 2672, 2673, 2674, 2675, 2676, 2677, 2678, 2679, 2680, 2681, 2682, 2683, 2684, 2685, 2686, 2687, 2688, 2689, 2690, 2691, 2694, 2696], [585, 628, 2670, 2671, 2672, 2673, 2674, 2675, 2676, 2677, 2678, 2679, 2680, 2681, 2682, 2683, 2684, 2685, 2686, 2687, 2688, 2689, 2690, 2691, 2692, 2693], [585, 628, 1105, 1175, 1176, 1474], [585, 628, 1105, 1201, 1474], [585, 628, 1105, 1212, 1218, 1474], [585, 628, 1105, 1212, 1474], [585, 628, 1105, 1281, 1474], [585, 628, 1105, 1282, 1474], [585, 628, 1105, 1272, 1474], [585, 628, 1105, 1279, 1474], [585, 628, 1142, 1143, 1144, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1152, 1153, 1154, 1155, 1156, 1157, 1158, 1159, 1160, 1161, 1162, 1163, 1164, 1165, 1166, 1167, 1168, 1169, 1170, 1171, 1172, 1173, 1174, 1175, 1176, 1177, 1178, 1180, 1181, 1182, 1183, 1184, 1185, 1186, 1187, 1188, 1189, 1190, 1191, 1192, 1193, 1194, 1195, 1196, 1197, 1198, 1199, 1200, 1201, 1202, 1203, 1204, 1205, 1206, 1207, 1208, 1209, 1210, 1211, 1213, 1214, 1215, 1216, 1217, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230, 1231, 1232, 1233, 1234, 1235, 1236, 1237, 1238, 1239, 1240, 1241, 1242, 1243, 1244, 1245, 1246, 1247, 1248, 1249, 1250, 1251, 1252, 1253, 1254, 1255, 1256, 1257, 1258, 1259, 1260, 1261, 1262, 1263, 1264, 1265, 1266, 1267, 1268, 1269, 1270, 1271, 1272, 1273, 1274, 1275, 1276, 1277, 1278, 1279, 1280, 1281, 1282, 1283, 1284, 1285, 1286, 1287, 1288, 1289, 1290, 1291, 1292, 1293, 1294, 1295, 1296, 1297, 1298, 1299, 1300, 1301, 1302, 1303, 1304, 1305, 1306, 1307, 1308, 1309, 1310, 1311, 1312, 1313, 1314, 1315, 1316, 1317, 1318, 1319, 1320, 1321, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1337, 1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345, 1346, 1347, 1348, 1349, 1350, 1351, 1352, 1353, 1354, 1355, 1356, 1357, 1358, 1359, 1360, 1361, 1362, 1363, 1364, 1365, 1366, 1367, 1368, 1369, 1370, 1371, 1372, 1373, 1374, 1375, 1376, 1377, 1378, 1379, 1380, 1381, 1382, 1383, 1384, 1385, 1386, 1387, 1388, 1389, 1390, 1391, 1392, 1393, 1394, 1395, 1396, 1397, 1398, 1399, 1400, 1401, 1402, 1403, 1404, 1405, 1406], [585, 628, 1105, 1333, 1474], [585, 628, 949, 1105, 1474], [585, 628, 1387, 1388, 1391], [585, 628, 1105, 1386, 1474], [585, 628, 1105, 1386, 1388, 1474], [585, 628, 1105, 1264, 1265, 1474], [585, 628, 1105, 1356, 1474], [585, 628, 1105, 1350, 1474], [585, 628, 1105, 1152, 1474], [585, 628, 1105, 1347, 1474], [585, 628, 1105, 1264, 1266, 1474], [585, 628, 1105, 1207, 1474], [585, 628, 1105, 1153, 1474], [585, 628, 1105, 1186, 1474], [585, 628, 1105, 1179, 1474], [585, 628, 1105, 1180, 1474], [585, 628, 1105, 1224, 1474], [585, 628, 1105, 1224, 1244, 1474], [585, 628, 1105, 1224, 1255, 1474], [585, 628, 1105, 1224, 1248, 1474], [585, 628, 1105, 1224, 1233, 1474], [585, 628, 1105, 1224, 1229, 1474], [585, 628, 1105, 1226, 1474], [585, 628, 1105, 1189, 1224, 1474], [585, 628, 1105, 1224, 1225, 1474], [585, 628, 1105, 1251, 1474], [585, 628, 1105, 1225, 1474], [585, 628, 1225], [585, 628, 949, 1105, 1259, 1474], [585, 628, 1105, 1266, 1267, 1474], [585, 628, 1105, 1259, 1270, 1474], [585, 628, 1105, 1271, 1474], [585, 595, 599, 628, 670], [585, 595, 628, 659, 670], [585, 590, 628], [585, 592, 595, 628, 667, 670], [585, 628, 648, 667], [585, 628, 677], [585, 590, 628, 677], [585, 592, 595, 628, 648, 670], [585, 587, 588, 591, 594, 628, 640, 659, 670], [585, 595, 602, 628], [585, 587, 593, 628], [585, 595, 616, 617, 628], [585, 591, 595, 628, 662, 670, 677], [585, 616, 628, 677], [585, 589, 590, 628, 677], [585, 595, 628], [585, 589, 590, 591, 592, 593, 594, 595, 596, 597, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 617, 618, 619, 620, 621, 622, 628], [585, 595, 610, 628], [585, 595, 602, 603, 628], [585, 593, 595, 603, 604, 628], [585, 594, 628], [585, 587, 590, 595, 628], [585, 595, 599, 603, 604, 628], [585, 599, 628], [585, 593, 595, 598, 628, 670], [585, 587, 592, 595, 602, 628], [585, 628, 659], [585, 590, 595, 616, 628, 675, 677], [585, 628, 1106, 1107, 1108, 1109], [585, 628, 1106], [585, 628, 1107], [472, 585, 628], [490, 585, 628], [585, 628, 816], [585, 628, 812], [585, 628, 813], [585, 628, 814, 815], [585, 628, 835], [585, 628, 823, 824, 835], [585, 628, 825, 826], [585, 628, 823, 824, 825, 827, 828, 833], [585, 628, 824, 825], [585, 628, 833], [585, 628, 834], [585, 628, 825], [585, 628, 823, 824, 825, 828, 829, 830, 831, 832], [585, 628, 1112, 1113], [585, 628, 1112], [51, 58, 69, 70, 93, 147, 148, 468, 552, 585, 628, 690, 691, 693, 696, 698, 700, 702, 705, 706, 707, 708, 709, 710, 711, 712, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 775, 776, 777, 802, 803, 1124], [51, 140, 585, 628, 1124], [51, 92, 585, 628, 819, 836, 1124], [51, 139, 140, 176, 585, 628, 1124], [51, 92, 139, 140, 585, 628, 1124], [51, 70, 141, 154, 179, 449, 585, 628, 1124], [51, 149, 177, 178, 180, 182, 440, 441, 442, 444, 445, 447, 585, 628, 1124], [51, 70, 141, 154, 176, 449, 585, 628, 1124], [51, 141, 154, 176, 449, 585, 628, 1124], [51, 140, 141, 154, 181, 449, 585, 628, 1124], [51, 141, 154, 181, 439, 449, 585, 628, 1124], [51, 141, 154, 181, 449, 585, 628, 1124], [51, 141, 154, 446, 449, 585, 628, 1124], [51, 141, 154, 443, 449, 585, 628, 1124], [51, 154, 155, 175, 448, 585, 628, 1124], [50, 51, 585, 628, 1105, 1124, 1129, 1415, 1416, 1418, 1434, 1456, 1474, 1484, 1487, 1488, 1490, 1492, 1493, 1494, 1509, 1511, 1515, 1519, 1520, 1521, 1525, 1527, 1528, 1541, 1585], [51, 585, 628, 1124, 1586], [51, 92, 105, 585, 628, 1124], [51, 105, 585, 628, 1124], [50, 51, 105, 585, 628, 1124], [50, 51, 58, 69, 103, 107, 139, 148, 585, 628, 817, 1124], [50, 51, 103, 585, 628, 817, 1124], [50, 51, 58, 69, 70, 93, 103, 107, 139, 141, 148, 553, 554, 555, 560, 561, 562, 585, 628, 685, 686, 1124], [50, 51, 58, 70, 141, 585, 628, 1124], [51, 585, 628, 684, 1124], [50, 51, 58, 69, 105, 148, 585, 628, 688, 1124], [51, 58, 69, 103, 105, 148, 585, 628, 688, 1124], [50, 51, 58, 105, 585, 628, 1124], [51, 105, 585, 628, 855, 1124], [50, 51, 58, 69, 93, 105, 148, 585, 628, 1124], [51, 105, 585, 628, 842, 1124], [50, 51, 69, 103, 105, 139, 141, 148, 585, 628, 697, 1124], [50, 51, 69, 103, 139, 141, 148, 585, 628, 699, 1124], [51, 105, 139, 585, 628, 817, 1124], [50, 51, 93, 103, 585, 628, 817, 1124], [50, 51, 105, 585, 628, 1124, 1595], [50, 51, 585, 628, 1124, 1597], [50, 51, 585, 628, 1124, 1599], [50, 51, 69, 148, 585, 628, 817, 1124], [51, 103, 176, 585, 628, 1124], [50, 51, 93, 103, 105, 585, 628, 698, 1124], [50, 51, 69, 148, 585, 628, 1124], [50, 51, 58, 69, 70, 103, 107, 109, 138, 141, 142, 143, 144, 145, 146, 148, 585, 628, 817, 1124], [50, 51, 58, 69, 70, 103, 105, 109, 138, 141, 148, 585, 628, 817, 1124], [50, 51, 585, 628, 817, 1124], [51, 585, 628, 817, 1124], [51, 103, 585, 628, 817, 1124], [51, 460, 461, 585, 628, 1124], [51, 103, 462, 585, 628, 817, 1124], [51, 58, 69, 103, 107, 148, 449, 450, 585, 628, 817, 1124], [50, 51, 69, 103, 107, 148, 585, 628, 817, 1124], [51, 105, 585, 628, 1124, 1612], [50, 51, 103, 105, 107, 585, 628, 1124], [50, 51, 58, 69, 70, 93, 103, 106, 107, 108, 148, 585, 628, 817, 1124], [51, 103, 585, 628, 1124, 1618, 1619], [50, 51, 69, 103, 148, 585, 628, 817, 1124], [50, 51, 58, 69, 103, 107, 148, 449, 450, 451, 585, 628, 817, 1124], [51, 58, 69, 103, 107, 148, 449, 450, 585, 628, 1124], [51, 58, 69, 103, 107, 148, 155, 450, 455, 585, 628, 817, 1124], [51, 103, 107, 453, 585, 628, 817, 1124], [50, 51, 105, 585, 628, 1124, 1610, 1615, 1625], [51, 585, 628, 1124, 1621], [50, 51, 103, 465, 585, 628, 817, 1124], [50, 51, 585, 628, 1124, 1633], [50, 51, 141, 562, 585, 628, 1124, 1673, 1998, 2315, 2316, 2317, 2319], [50, 51, 69, 70, 105, 148, 179, 180, 442, 443, 449, 562, 585, 628, 1124], [51, 105, 585, 628, 1124, 2323], [51, 105, 176, 585, 628, 694, 817, 1124, 2328], [50, 51, 69, 105, 148, 177, 178, 449, 585, 628, 1124, 2329, 2655, 2656, 2661], [50, 51, 69, 105, 148, 182, 440, 441, 449, 551, 585, 628, 1124, 2657, 2658, 2660], [50, 51, 69, 105, 148, 177, 449, 585, 628, 1124, 2318], [50, 51, 105, 585, 628, 1124, 2662, 2664], [50, 51, 69, 148, 585, 628, 1124, 1673, 2654], [50, 51, 105, 585, 628, 1124, 1673, 2654], [50, 51, 69, 105, 148, 585, 628, 1124, 1673], [51, 451, 585, 628, 1124, 2699, 2700], [51, 103, 585, 628, 684, 1124, 1617], [51, 103, 139, 585, 628, 684, 1124], [50, 51, 69, 104, 105, 585, 628, 1124], [51, 103, 585, 628, 684, 1124], [51, 585, 628, 684, 694, 1124], [50, 51, 105, 139, 585, 628, 1124, 2705], [51, 69, 70, 103, 138, 141, 148, 585, 628, 694, 717, 817, 1124], [50, 51, 58, 69, 70, 103, 105, 138, 141, 148, 585, 628, 719, 817, 1124], [50, 51, 58, 69, 103, 142, 143, 148, 155, 450, 457, 585, 628, 817, 1124], [50, 51, 70, 103, 105, 138, 140, 141, 142, 585, 628, 817, 1124], [50, 51, 58, 69, 70, 93, 103, 138, 139, 141, 142, 148, 585, 628, 694, 817, 1124], [50, 51, 103, 105, 142, 585, 628, 817, 1124], [50, 51, 58, 69, 93, 103, 105, 107, 139, 142, 148, 585, 628, 817, 1124], [50, 51, 58, 103, 107, 142, 585, 628, 817, 1124], [50, 51, 103, 107, 142, 585, 628, 817, 1124], [51, 69, 70, 139, 148, 585, 628, 773, 817, 1124], [50, 51, 69, 138, 585, 628, 773, 1124, 1598], [50, 51, 69, 148, 585, 628, 684, 1124], [50, 51, 105, 443, 444, 445, 449, 585, 628, 1124, 2657], [50, 51, 70, 105, 141, 562, 585, 628, 1124, 2710], [51, 585, 628, 1124, 2717, 2719], [50, 51, 105, 439, 585, 628, 1124], [51, 93, 105, 585, 628, 1124], [50, 51, 69, 148, 449, 585, 628, 1124, 2316, 2728], [50, 51, 93, 105, 585, 628, 1124, 2727], [50, 51, 105, 585, 628, 1124, 2718], [50, 51, 69, 105, 148, 182, 443, 447, 449, 585, 628, 817, 1124, 2328], [50, 51, 105, 585, 628, 1124, 2731, 2732], [51, 585, 628, 1124, 2726, 2731, 2732, 2733], [50, 51, 69, 103, 107, 139, 141, 148, 585, 628, 817, 1124], [51, 69, 148, 585, 628, 801, 1124], [50, 51, 58, 69, 70, 93, 103, 105, 106, 148, 585, 628, 817, 1124, 1622, 1624, 2704], [50, 51, 69, 103, 105, 148, 585, 628, 817, 1124], [50, 51, 58, 105, 139, 585, 628, 1124], [51, 58, 69, 103, 105, 139, 148, 585, 628, 688, 817, 1124], [50, 51, 58, 69, 105, 148, 177, 440, 449, 530, 551, 585, 628, 817, 1124], [50, 51, 69, 105, 148, 177, 179, 442, 449, 585, 628, 1124, 1673, 1998, 2315, 2738], [50, 51, 69, 70, 105, 148, 179, 442, 449, 562, 585, 628, 1124, 2739, 2740, 2741], [51, 105, 179, 585, 628, 1124], [50, 51, 69, 105, 148, 179, 442, 449, 585, 628, 1124, 2318, 2738], [50, 51, 69, 70, 105, 148, 179, 180, 449, 585, 628, 1124, 2318, 2710, 2738], [50, 51, 69, 105, 148, 179, 180, 442, 449, 585, 628, 1124, 2316, 2318], [50, 51, 105, 179, 585, 628, 1124, 1673, 2654], [50, 51, 105, 179, 585, 628, 1124], [50, 51, 105, 179, 585, 628, 1124, 2318], [50, 51, 69, 70, 105, 148, 177, 179, 180, 443, 444, 449, 585, 628, 1124, 2738], [50, 51, 69, 70, 105, 148, 179, 180, 443, 449, 562, 585, 628, 1124, 2744, 2745, 2746, 2747, 2748, 2749], [51, 179, 585, 628, 1124], [51, 138, 139, 585, 628, 1124, 1593, 1594], [50, 51, 585, 628, 1124, 2754], [51, 92, 138, 585, 628, 822, 1124], [51, 99, 102, 103, 585, 628, 805, 806, 807, 808, 1124], [50, 51, 58, 69, 141, 148, 177, 443, 449, 585, 628, 687, 701, 1124], [50, 51, 58, 69, 141, 148, 177, 443, 449, 585, 628, 687, 1124], [51, 69, 138, 155, 449, 585, 628, 692, 773, 804, 809, 810, 817, 1124], [50, 51, 69, 138, 585, 628, 1124, 1598], [51, 139, 140, 585, 628, 1124], [50, 51, 69, 91, 92, 147, 585, 628, 1124], [51, 138, 585, 628, 839, 840, 1124], [51, 69, 585, 628, 1124], [50, 51, 58, 585, 628, 687, 689, 1124], [51, 58, 103, 138, 141, 585, 628, 1124], [51, 585, 628, 703, 704, 1124], [50, 51, 58, 69, 103, 107, 139, 140, 148, 585, 628, 686, 817, 1124], [50, 51, 58, 69, 103, 105, 107, 148, 182, 440, 441, 449, 530, 551, 585, 628, 817, 1124], [50, 51, 69, 70, 103, 107, 139, 140, 148, 585, 628, 686, 817, 1124], [50, 51, 58, 69, 70, 103, 107, 139, 140, 148, 555, 585, 628, 685, 686, 817, 1124], [51, 109, 142, 452, 454, 456, 457, 458, 459, 463, 464, 466, 467, 585, 628, 1124], [50, 51, 105, 585, 628, 1124, 1635, 1636, 1637], [51, 105, 585, 628, 1124, 2320], [50, 51, 105, 585, 628, 1124, 2665], [50, 51, 70, 585, 628, 716, 718, 720, 1124], [51, 58, 70, 585, 628, 716, 774, 1124], [50, 51, 103, 109, 142, 585, 628, 817, 1124], [50, 51, 69, 105, 139, 141, 148, 585, 628, 687, 1124], [50, 51, 58, 69, 70, 103, 107, 139, 140, 148, 555, 585, 628, 685, 694, 817, 1124], [50, 51, 69, 105, 148, 180, 442, 449, 585, 628, 1124, 2319, 2658, 2742, 2750, 2769], [50, 51, 58, 69, 70, 103, 107, 139, 140, 148, 555, 585, 628, 685, 694, 695, 817, 1124], [50, 51, 58, 69, 70, 103, 107, 139, 140, 148, 555, 585, 628, 685, 692, 817, 1124], [50, 51, 105, 585, 628, 1124, 2318], [50, 51, 585, 628, 1124, 2709, 2711], [50, 51, 69, 105, 148, 585, 628, 1124, 2712, 2713, 2729, 2733, 2774], [585, 628, 817]], "fileInfos": [{"version": "e41c290ef7dd7dab3493e6cbe5909e0148edf4a8dad0271be08edec368a0f7b9", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "4fd3f3422b2d2a3dfd5cdd0f387b3a8ec45f006c6ea896a4cb41264c2100bb2c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69e65d976bf166ce4a9e6f6c18f94d2424bf116e90837ace179610dbccad9b42", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "62bb211266ee48b2d0edf0d8d1b191f0c24fc379a82bd4c1692a082c540bc6b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f1e2a172204962276504466a6393426d2ca9c54894b1ad0a6c9dad867a65f876", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "170d4db14678c68178ee8a3d5a990d5afb759ecb6ec44dbd885c50f6da6204f6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "d4d7d3f832882a4b2d611a7eaaa80c780c3342b5732090130fa9af4a40bd051e", "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "af506b94086f9baf8f107acf522b5cdc6b3dc1bd1b7e07d6845221c25a4a0f21", "impliedFormat": 99}, {"version": "071d8b050b3d496fa3d04214417535189f37b24ef39c79deb39ec060ac9d8392", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "0181a41111afd61a1358d94818981102945a64788bd54f51df81a233c5e24d98", "impliedFormat": 99}, {"version": "5a7ebcf5fe8ac590dd03af1bbe426dfed639a3490fb1e5d6b934e45643b8ea1b", "impliedFormat": 1}, {"version": "41c56f27ef419e2c5d8faf66b29a9718dd182e0636565115f7c975c6f6cdcac2", "impliedFormat": 99}, {"version": "dfa56c284347a88e27731cb68e20669ec856bcaed79a80574ebec3f5a2223c1e", "impliedFormat": 99}, {"version": "9f49b8064f63b7b3275a8247692967da2458734ea9afcf5ffd86b5c177674740", "impliedFormat": 99}, {"version": "7e78d892463fedf71952f2476d53eb874d3ddd85dafef67f17f249fa769d48a5", "impliedFormat": 99}, {"version": "fce8fc3270f9ca57bbaafa85c874ebdb56467f283f1c7af0ab1642745dad34da", "impliedFormat": 99}, {"version": "bc1d9cf49ddbc7c4de8ed62d598e52109ccbda19ae421bbebea6512a5b6aa8a8", "impliedFormat": 99}, {"version": "a77de3d536866c603794061a04b219dc4268946f2c7c728a7fed33600f05e8fe", "impliedFormat": 99}, {"version": "40db523fa785a3b46bd70b1a25a695c27d46ab166ee776702fabb179a147822b", "impliedFormat": 99}, {"version": "6309b212d11255199800461e71b9739211ce20f22de754cecf0aca9176a6f72b", "impliedFormat": 99}, {"version": "50875ff16ada1ff33d8e18733603003776bab2a16335ed9cc809326bc6c61b43", "impliedFormat": 99}, {"version": "e645540c570c2e1046691874adc1b7376aea883755ccf9aa48a654065684882c", "impliedFormat": 99}, {"version": "aba4a3c7faa69d25a40e7600c4fc2db23f0488a2fa951dfb4827db5aa38ebff1", "impliedFormat": 99}, {"version": "0e053682370a25a792dc16878e2af8657a444cf97c12618274db74eabb0f9010", "impliedFormat": 99}, {"version": "867ecd736f6f31716faadf9a3574a6694f987128388edfa4e687eb5e89a67065", "impliedFormat": 99}, {"version": "e3df9681db1915c3fc871adb448f392807463b60925c3fe62c5bb6880dd5070f", "impliedFormat": 1}, {"version": "245a6d49a8c543c8224fc8e936e5cdfe9f8031397a0baca21df7875d3e1c0793", "impliedFormat": 99}, {"version": "56277c308ec8aeb151402bbe00936244f731912bd52bbe404f9861a1ac56ce38", "impliedFormat": 99}, {"version": "a65e2b08014c58503f4736c4d2acf8bd0fa9bf8bb26594661e5589878fa12777", "impliedFormat": 99}, {"version": "a97b6028bf8b97761ab449f213794a7df511c5294c63c1da723bcce4e4359237", "impliedFormat": 99}, {"version": "ab35d635fbc9525840a47c7ae869682a34e237e7693163dcd905f512e5754c60", "impliedFormat": 99}, {"version": "ee7a1f5f7f64294940b5de9e1f2176b32283466e7deb7e2b59c285226cd91e11", "impliedFormat": 99}, {"version": "b659e5187910757fde10ae751a7daf1d5223b5ede3e7da4fd8a6802377a8c066", "impliedFormat": 99}, {"version": "e3f1c29c7d361320939a701c19344515ef1ece81679fd93b05d23068f82d5af8", "impliedFormat": 99}, {"version": "b864648301753313ece820ecba983f75d24fd41d557b0251bb1d3d80aefdf71a", "impliedFormat": 99}, {"version": "0b30c7a5def9f7dc3e903ec849471f6fa4f53e4eea9d93f8e6c5f6506f0343ff", "impliedFormat": 99}, {"version": "0a044cb626f8ca7f5150d9c545b92b67d1ce65a0f0c06af1c310bdfe79adf9ed", "impliedFormat": 99}, {"version": "7a34bc48c16e36599d2557e0c3551c02c2d2c65786d636fa4642dddb7861ef01", "impliedFormat": 99}, {"version": "074aca4e6e82a8965426ec5e5926ca3aa10240a3815b77b40307a9be3ddd15a8", "impliedFormat": 99}, {"version": "b092c5b5d580b68d779275cb451b3f7dc07efe4f6bb33fb704fa93ec10175dc2", "impliedFormat": 99}, {"version": "d084de09e030e1af6f242f4a52d0c5fbd68bde6d32920e01a684bec2d3453852", "impliedFormat": 99}, {"version": "b7933efc957e83aec2f30c831212adbd4d15215f756d7ab33fb9e3ebf856fdad", "impliedFormat": 99}, {"version": "949eb33fa405ee20dca30f39f9b9056fa9c1feb9b71a3ef0e8ef22cdc5925c62", "impliedFormat": 99}, {"version": "a3bd0fcb5f2725be426dd059489112c116078fab537ed46b0ef30240dd5fefa9", "impliedFormat": 99}, {"version": "d50c869a1403f37e2afdb439ed434112a5f21f51d9b2f9df2c42a7aee183a4ce", "impliedFormat": 99}, {"version": "1f699f13de7568a6d155915f6df922d37d28c21385d8870c5d0b39631114f2ec", "impliedFormat": 99}, {"version": "c9c5c1285ed64b9d73561bab43177b87dbff5d70763bf9a0a94fb5db6877ce00", "impliedFormat": 99}, "e951a78d936784d0cb8e2358aec1ff5d119b1986c1c5480a759430eed6e0c73e", "1e04cb19f85f9a903bfe4b2444d7d75b1d617fff1d0baa94bd656e570029d994", {"version": "890bdcec61a6fe8e39e35a1a9e4e0cad8c99b371646077bed13724862c4ab711", "impliedFormat": 1}, {"version": "e30accdbef6f904f20354b6f598d7f2f7ff29094fc5410c33f63b29b4832172a", "impliedFormat": 1}, {"version": "d46f6e5d2f8ffc47b103d452bda041eb1b714ceff051079ac96f1057e2ba1e02", "impliedFormat": 1}, {"version": "17e5b4890145a3adf7eafd52b35981cef7aaf385d073c027e37a1126305970b5", "impliedFormat": 1}, {"version": "4d39c150715cb238da715e5c3fbeac2e2b2d0ef3f23c632996dd59ae35ba1f45", "impliedFormat": 1}, {"version": "e9f80c5934982b97886eadab6684c073344a588d1758b12fba2d0184e6f450a2", "impliedFormat": 99}, {"version": "9d3ebddb2b7d90a93d19ddae7fc3fe28bf2d3233f16c97f89b880fd045b068e4", "impliedFormat": 1}, {"version": "b3e571e9f098c30db463d30d16d395ad8dd2457ee6e8d1561e2e1527bc2b6ce0", "impliedFormat": 1}, {"version": "cd141139eea89351f56a0acb87705eb8b2ff951bb252a3dfe759de75c6bfed87", "impliedFormat": 1}, {"version": "a7661d2413b60a722b7d7ff8e52dd3efad86600553eba1c88c990a0b2c11870b", "impliedFormat": 99}, "bcaefddbc0ca616624427da6807269cb1ddecf4b772425fef494ea6e3f2801c3", {"version": "46c9b97d2cf765a080a86b1b9bf1c240f9b02ecc2cc1ef5168f20207d9559c27", "impliedFormat": 1}, "20d76b8f99ac12dd20d8d635e799883bf32dbb4698936714864c09471e57758b", "478d970f65bef2ae101b91fc293652e040113c16b2dd6ccad4966563421d6133", "67b7aa83b12a1b2d2ec7944200607545296c90f7169635edfb4031ee43510056", "d09c0e6db4fc35f534b6d8fbcff636b4db9156d57e4aad58d4cdbea0171cfa56", {"version": "9971931daaf18158fc38266e838d56eb5d9d1f13360b1181bb4735a05f534c03", "impliedFormat": 99}, {"version": "50cf7a23fc93928995caec8d7956206990f82113beeb6b3242dae8124edc3ca0", "impliedFormat": 99}, {"version": "62443d6fbad6830cc1ec6a96ccb346b9c8fac320d954f7ba14ec84ac489c89e5", "impliedFormat": 99}, {"version": "bd85074aed3cfd83d906643c80cda912732d688b0a20791cd8df5b7ff0eba59e", "impliedFormat": 99}, {"version": "909e8848dd7005329d4841027b51d09efcfb131e14268b091e830ab7adea9849", "impliedFormat": 99}, {"version": "0c5b705d31420477189618154d1b6a9bb62a34fa6055f56ade1a316f6adb6b3a", "impliedFormat": 99}, {"version": "352031ac2e53031b69a09355e09ad7d95361edf32cc827cfe2417d80247a5a50", "impliedFormat": 99}, {"version": "853b8bdb5da8c8e5d31e4d715a8057d8e96059d6774b13545c3616ed216b890c", "impliedFormat": 99}, {"version": "b9bd72693123f4548f67d5ba060cedc22755606d3bd63bb1d719970086799965", "impliedFormat": 99}, {"version": "9bd5be6049c58f5a7a1699c3c8c4db44d634f2a861de445dda907011167317b1", "impliedFormat": 99}, {"version": "476a3b1fb75bdc87b3dd9e3eff4f0ac4b014200f12b7bc7468c889325ce00700", "impliedFormat": 99}, {"version": "c363b57a3dfab561bfe884baacf8568eea085bd5e11ccf0992fac67537717d90", "impliedFormat": 99}, {"version": "1757a53a602a8991886070f7ba4d81258d70e8dca133b256ae6a1a9f08cd73b3", "impliedFormat": 99}, {"version": "084c09a35a9611e1777c02343c11ab8b1be48eb4895bbe6da90222979940b4a6", "impliedFormat": 99}, {"version": "4b3049a2c849f0217ff4def308637931661461c329e4cf36aeb31db34c4c0c64", "impliedFormat": 99}, {"version": "6245aa515481727f994d1cf7adfc71e36b5fc48216a92d7e932274cee3268000", "impliedFormat": 99}, {"version": "d542fb814a8ceb7eb858ecd5a41434274c45a7d511b9d46feb36d83b437b08d5", "impliedFormat": 99}, {"version": "660ce583eaa09bb39eef5ad7af9d1b5f027a9d1fbf9f76bf5b9dc9ef1be2830e", "impliedFormat": 99}, {"version": "b7d9ca4e3248f643fa86ff11872623fdc8ed2c6009836bec0e38b163b6faed0c", "impliedFormat": 99}, {"version": "ac7a28ab421ea564271e1a9de78d70d68c65fab5cbb6d5c5568afcf50496dd61", "impliedFormat": 99}, {"version": "d4f7a7a5f66b9bc6fbfd53fa08dcf8007ff752064df816da05edfa35abd2c97c", "impliedFormat": 99}, {"version": "1f38ecf63dead74c85180bf18376dc6bc152522ef3aedf7b588cadbbd5877506", "impliedFormat": 99}, {"version": "24af06c15fba5a7447d97bcacbcc46997c3b023e059c040740f1c6d477929142", "impliedFormat": 99}, {"version": "facde2bec0f59cf92f4635ece51b2c3fa2d0a3bbb67458d24af61e7e6b8f003c", "impliedFormat": 99}, {"version": "4669194e4ca5f7c160833bbb198f25681e629418a6326aba08cf0891821bfe8f", "impliedFormat": 99}, {"version": "f919471289119d2e8f71aba81869b01f30f790e8322cf5aa7e7dee8c8dadd00a", "impliedFormat": 99}, {"version": "a95cd11c5c8bc03eab4011f8e339a48f9a87293e90c0bf3e9003d7a6f833f557", "impliedFormat": 99}, {"version": "e9bc0db0144701fab1e98c4d595a293c7c840d209b389144142f0adbc36b5ec2", "impliedFormat": 99}, {"version": "f2f2d8bbb50156631dead40948f350006607ccf431134d8d6278a7d82d1654fa", "impliedFormat": 99}, "fa245d62a3bfea95e6186f854c672c43da68261b1af38a8fcd6078ac12154192", {"version": "1d7ee0c4eb734d59b6d962bc9151f6330895067cd3058ce6a3cd95347ef5c6e8", "impliedFormat": 99}, "21a0fb2683a15958b8cddd1e6505cac38349bbafda3d20c67572c5291dee1030", "6060d3983fac806676fe0b1f68f2ea9d5bc65600cb2fe162837eedaee9611abd", "8fbd9cc7cb3e47e758590dc334aae41ad495c34a3d142d747519797f6bfeb89b", "f7446dc341e06e95e135b8b2ff99ac6c25a015adf5dd5a79499e55310f2506a7", "1c7bb8bfc46e51adb1f926026ba76f32842dd1ad6c97b3176f28cc6f970cd513", "30324301f1d0149451111ee4a298b62074b8cd0dfd02f4decfc017647feb0062", "aac1e941faeab28fa00c28320fc21b7e3ac8c6ddcb2118802e5b40d6b62d9674", "1eecd162b6a7450175e75f92559c20de1d5ead064ee16dd5082405db0acb98a3", {"version": "f734b58ea162765ff4d4a36f671ee06da898921e985a2064510f4925ec1ed062", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07cbc706c24fa086bcc20daee910b9afa5dc5294e14771355861686c9d5235fd", "impliedFormat": 1}, {"version": "37f96daaddc2dd96712b2e86f3901f477ac01a5c2539b1bc07fd609d62039ee1", "impliedFormat": 1}, {"version": "9c5c84c449a3d74e417343410ba9f1bd8bfeb32abd16945a1b3d0592ded31bc8", "impliedFormat": 1}, {"version": "c0bd5112f5e51ab7dfa8660cdd22af3b4385a682f33eefde2a1be35b60d57eb1", "impliedFormat": 1}, {"version": "be5bb7b563c09119bd9f32b3490ab988852ffe10d4016087c094a80ddf6a0e28", "impliedFormat": 99}, {"version": "9b643d11b5bca11af760795e56096beae0ed29e9027fec409481f2ee1cb54bbc", "impliedFormat": 1}, {"version": "f6bf4eeea6b947bd5466496b19a57e04d9a7c60f76c235ac84cb92d31b360de3", "impliedFormat": 1}, {"version": "eaa9681ffd4ce68e0f42a5b911b80fca55bbda891c468e1ad45e89fb58af5966", "impliedFormat": 1}, {"version": "b9ab55716f628f25bef04bb84a76ba910b15fbca258cd4823482192f2afae64d", "impliedFormat": 1}, {"version": "97d10f67bd43dd329f12330c1a220c878aeac7dca9b4c24d1a9608a9eae9adf3", "impliedFormat": 1}, {"version": "71be818689f367754c0f7b7422bef2a9be542f179420409f2c23fbe19e59ff1f", "impliedFormat": 1}, {"version": "3e6a9f8e638eec92423c8417902b3b32db6f78a6863e02c6c0f0395aad288697", "impliedFormat": 1}, {"version": "8cb476b8463d4eb8efb00004d692ba4b794d1002ea09c692a4c1c47c123a9b48", "impliedFormat": 1}, {"version": "9209c55d0addb75d1f69f49c36a71871b0363e4fda3c5a1bcb50e3fb19160e61", "impliedFormat": 1}, {"version": "d56b6ecb736371007bd1883aec48299e8b1299455f5dd2cc6eca457250dd6439", "impliedFormat": 1}, {"version": "02c0a7d3009a070e95bc4f0158519f29b0cd0b2d7b1d53bfa6211160787d437c", "impliedFormat": 1}, {"version": "696ea6804dccb58691bc9e2fa3e51f7f025e2b2a6c52725ab5c0ea75932096a5", "impliedFormat": 1}, {"version": "ef3b3a5ffbebafdc0df711687920124f4685654ac9d21394e7de76729a414a6c", "impliedFormat": 1}, {"version": "a9fd68d0615b5e130919a5643fad4f3e32fecea55f6681842a46602c24d667cf", "impliedFormat": 1}, {"version": "d968f31bc24df80105cafde207e8b9043f6c203f046ccee6675f8d7455018e7d", "impliedFormat": 1}, {"version": "86ab8a80432163184a66c7498351a21c291a12851b2aa5bbbf4fb6fcb04d965b", "impliedFormat": 1}, {"version": "7d52d5b507a5750f91079713dc2ec0d07c3aed30a97f4378663c13916340c487", "impliedFormat": 1}, {"version": "1f5035cfd165814e5e32a3f2a6544d6f98a080405475275dc85b30df276977df", "impliedFormat": 1}, {"version": "bf1fe30d276cb51bd4def431640f5fd017d3e0a15ceb1c9a9e67d1d4db7cf7ef", "impliedFormat": 1}, {"version": "7a3f06f9bf17b412923c78a2b3a262085e57aaf929f845af3cbf54812345e8cc", "impliedFormat": 1}, {"version": "aaf024f54e41c7f5ecfffc665861acee7289f62f7ef3a28b423f36b4ed13200a", "impliedFormat": 1}, "52274fe33270f78025947859300c30ebe80129b76bd82faca4c78d9c295bfb6e", "77550256e145a5cc68add39a09084043c013dc9fdd35ceeb1a86dfac442da024", "cb2f02a9d9b8936866b704b891d29c1d7d263c3ff5ff81ce28e21dd06851a04b", "732148e34d65d8fe96910712358e72140f3261715906944bb44f8821b5a77244", "ce53d9224fd7f88d160ab381d251e1fbaf029d69edcc213d455f49d90ba1bbd4", "a156795953ecb6ac8ae1e07b3a0ea98a00d49a910c67d699824fe6f67e4c157f", "2c2275150030dbbf61adc23f6c6446f29f56d75b7d4e5f65d0474069b2066769", {"version": "f4e8f4151c3490cf7b68c685aabe901cbab19f962aaa2f118a97550e22689a76", "impliedFormat": 1}, {"version": "799003c0ab928582fca04977f47b8d85b43a8de610f4eef0ad2d069fbb9f9399", "impliedFormat": 1}, {"version": "d998eea476c695d8e4ff9d007d5b46d49ca2ffa052f74dc20ca516425abd57b1", "impliedFormat": 1}, {"version": "a0bd46d587005aad4819980f6cf2dbcd80ebf584ed1a946202326a27158ba70e", "impliedFormat": 1}, {"version": "07fcbb61a71bd69a92a5bbde69e60654666cf966b5675c2010c3bf9f436f056a", "impliedFormat": 1}, {"version": "88b2eb23d36692162f2bf1e50577ebcde26de017260473e03ed9a0e61e2726a4", "impliedFormat": 1}, {"version": "23ffbd8c0e20a697d2ea5a0cf7513fb6e42c955a7648f021da12541728f62182", "impliedFormat": 1}, {"version": "43fba5fc019a4ce721a6f53ddb97fdc34c55049cfb793bc544d5c864ee5560b9", "impliedFormat": 1}, {"version": "f4e12292c9a7663a13d152195019711c427c552eb0fa02705e0f61370cd5547a", "impliedFormat": 1}, {"version": "c127ebf14d1b59d1604865008fb072865c5ca52277621f566092fe1f42ce0954", "impliedFormat": 1}, {"version": "def638da26d84825a312113a20649d3086861de7c06a18ea13121278702976fd", "impliedFormat": 1}, {"version": "fbaf86f8ba11298dea2727ce0da84b4ab6ae6c265e1919d44aff7d9b2bbc578a", "impliedFormat": 1}, {"version": "c1010caaeaca8e420c6e040c2e822dbe18702459c93a7d2d5de38597d477b8cd", "impliedFormat": 1}, {"version": "e1f0d8392efd9d71f2644eb97d3f33d90827e30ea8051d93b6f92bb11dff520a", "impliedFormat": 1}, {"version": "085211167559ca307d4053bb8d2298d5ad83cbc3d2ae9bb4c8435a4cabf59369", "impliedFormat": 1}, {"version": "55fc49198d8a85a73cdb79e596d9381cfdc9de93c32c77d42e661c1c1e7268ef", "impliedFormat": 1}, {"version": "6a53fb3df8dd32ed1a65502ca30aeae19cfe80990e78ba68162d6cb2a7fed129", "impliedFormat": 1}, {"version": "b5dcc18d7902597a5584a43c1146ca4fe0295ceb5125f724c1348f6a851dd6ed", "impliedFormat": 1}, {"version": "0c6b0f3fbe6eb6a3805170b3766a341118c92ed7b6d1f193b9f35aa82f594846", "impliedFormat": 1}, {"version": "60eaadb36cf157c5cae9c40e84fa367d04f52a150db3920dbe35139780739143", "impliedFormat": 1}, {"version": "4680a32b1098c49dc87881329af1e68af9af94e051e1b9e19fed555a786f6ce6", "impliedFormat": 1}, {"version": "89fcd129ec37f321cddcdb6b258ffe562de4281e90ec3ccbe7c1199ba39359ca", "impliedFormat": 1}, {"version": "4313011f692861c2c1f5205d7f9a473e763adab6444f9853b96937b187fb19f7", "impliedFormat": 1}, {"version": "caa57157e7bdb8d5f1efe56826fb84a6c8f22a1927bba7fa21fd54e2a44ccba2", "impliedFormat": 1}, {"version": "6b74700abfe4a9b88be957fd8e373cfd998efb1a5f6ad122da49a92997e183ad", "impliedFormat": 1}, {"version": "9ef1342f193bd8bae86c64e450c3ac468ef08652110355e1f3cdd45362eb95c4", "impliedFormat": 1}, {"version": "6853c91662c36a2bf4c8371a87177c819007c76a23c293ef3f686ce9157ae4c8", "impliedFormat": 1}, {"version": "9be1c5dabce43380d13fc621100676b03d420b5687b08d1288f479bee68ab7a8", "impliedFormat": 1}, {"version": "8996d218010896712678e6a0337d8ef8b81c1066ab76f637dd8253f0d6ff838d", "impliedFormat": 1}, {"version": "a15603bf387fc45defe28a68f405a6c29105e135c4e8538eeb6d0a1ef5b69a81", "impliedFormat": 1}, {"version": "84e2532e4d42949a2775cdd8bb7b2b97370dd6ddb683d0c199b21bf6978b152d", "impliedFormat": 1}, {"version": "22bf5f19f620db3b8392cfece44bdd587cdbed80ba39c88a53697d427135bf37", "impliedFormat": 1}, {"version": "23ebbd8d484d07e1c1d8783169c20570ed8409966b28f6be6cf8e970d76ef491", "impliedFormat": 1}, {"version": "18b6fa2c778cad6489f2febf76433453f5e2432ec3535f2d45ae7d803b93cc17", "impliedFormat": 1}, {"version": "609d0d7419999cf44529e6ba687e2944b2fc7ad2570d278fd4e6b1683c075149", "impliedFormat": 1}, {"version": "249cf421b8878a3fe948d9c02f6b0bae65491b3bb974c2ffc612341406fa78ff", "impliedFormat": 1}, {"version": "b4aa22522d653428c8148ddbf1dcc1fb3a3471e15eb1964429a67c390d8c7f38", "impliedFormat": 1}, {"version": "30b2cee905b1848b61c7d28082ebfa2675dd5545c0d25d1c093ce21a905cdccc", "impliedFormat": 1}, {"version": "0a2a2eed4137368735205de97c245f2a685af1a7f1bf8d636b918a0ee4ff4326", "impliedFormat": 1}, {"version": "69f342ce86706aa2835a62898e93ea7a1f21b1d89c70845da69371441bb6cd56", "impliedFormat": 1}, {"version": "b5ab4282affcfd860dd1cc3201653f591509a586d110f8e5b1b010508ba79b2c", "impliedFormat": 1}, {"version": "d396233f6cd3edf0d33c2fbfc84ded029c3ea4a05af3c94d09d31a367cced111", "impliedFormat": 1}, {"version": "bc41a726c817624a5136ae893d7aac7c4dc93c771e8d243a670324bccf39b02b", "impliedFormat": 1}, {"version": "710728600e4b3197f834c4dd1956443be787d2e647a72f190bf6519f235aaadd", "impliedFormat": 1}, {"version": "a45097e01ef30ba26640fed365376ab3ccd5faf97d03f20daff3355a7e60286a", "impliedFormat": 1}, {"version": "763cbb7c22199f43fd5c2b1566af5ba96bf7366f125dd31a038a2291cbc89254", "impliedFormat": 1}, {"version": "031933bf279b7563e11100b5e1746397caf3a278596796a87bc0db23cf68dc9e", "impliedFormat": 1}, {"version": "a4a54c1f58fc6e25a82e2c0f651bf680058bd7f72cfb2d43b85ee0ab5fe2e87e", "impliedFormat": 1}, {"version": "9613d789b6f1037f2523a8f70e1b736f1da4566b470593da062be5c9e13dac57", "impliedFormat": 1}, {"version": "0d2a320763a0c9c71493f8f1069971018c8720a6e7e5a8f10c26b6de79aa2f7d", "impliedFormat": 1}, {"version": "817e0df27a237a268dc16e5acffc19f9a74467093af7a0ba164ee927007a4d25", "impliedFormat": 1}, {"version": "43102521b5ca50ff1865188c3c60790feaed94dc9262b25d4adec4dbc76f9035", "impliedFormat": 1}, {"version": "f99947f8d873b960b0115e506ef9c43f4e40c2071b1d20375564538af4a6023b", "impliedFormat": 1}, {"version": "c1e5ad5ca89d18d2a36d25e8ec105623648cf35615825e202c7d8295a49d61ab", "impliedFormat": 1}, {"version": "2b6c9cb81da4e0a2e32a58230e8c0dec49fc5b345efb7f7a3648b98956be4b13", "impliedFormat": 1}, {"version": "99e34af3ede50062dcc826a1c3ce2d45562060dfd0f29f8066381a6ef548bf2a", "impliedFormat": 1}, {"version": "49f5c2a23ea5fc4b2cdb4426f09d1c8b83f8409fa2af13ef38845cc9b9d4bc3d", "impliedFormat": 1}, {"version": "e935227675144b64ecde3489e4a5e242eeb25fdd6b7464b8c21ad1f7a0faa88b", "impliedFormat": 1}, {"version": "b42e6bbe88dc79c2d6dc5605fb9c15184e70f64bdd7b8d4069b802b90ce86df6", "impliedFormat": 1}, {"version": "b9cd712399fdc00fdae07e96c9b39c3cb311e2a8a5425f1bd583f13cab35e44b", "impliedFormat": 1}, {"version": "5a978550ae131b7fef441d67372fd972abab98ea9fdb9fa266e8bdc89edcb8d6", "impliedFormat": 1}, {"version": "4f287919cfc1d26420db9f0457cd5c8780b1ef0a9f949570936abe48d3a43d91", "impliedFormat": 1}, {"version": "496b23b2fd07e614bc01d90dd4388996cb18cd5f3a612d98201e9f683e58ad2e", "impliedFormat": 1}, {"version": "dcfbe42824f37c5fb6dc7b9427ef2500791ec0d30825ecb614f15b8d5bf5a667", "impliedFormat": 1}, {"version": "390124ad2361b46bf01851d25e331cd7eed355d04451d8b2a4aa985c9de4f8ce", "impliedFormat": 1}, {"version": "14d94f17772c3a58eda01b6603490983d845ee2012cd643f7497b4e22566aacb", "impliedFormat": 1}, {"version": "03ef2386c683707ce741a1c30cb126e8c51a908aa0acc01c3471fafb9baaacd5", "impliedFormat": 1}, {"version": "66a372e03c41d2d5e920df5282dadcec2acae4c629cb51cab850825d2a144cea", "impliedFormat": 1}, {"version": "5b48ba9a30a93176a93c87f9e0abf26a9df457eeb808928009439ca578b56f27", "impliedFormat": 1}, {"version": "4707625392316d3c16edbd0716f4ac310e8ff5d346d58f4d01a2b7e0533a23df", "impliedFormat": 1}, {"version": "154d58a4b2d9c552dc864ea39c223d66efd0ed2dd8b55bd13db5225d14322915", "impliedFormat": 1}, {"version": "6a830433fa072931b4ea3eb9aa5fa7d283f470080586a27bfe69837a0f12de9a", "impliedFormat": 1}, {"version": "d25e930e181f4f69b2b128514538f2abb54ef1d48a046ad776ac6f1cda885a72", "impliedFormat": 1}, {"version": "0259b4c21bc93b52ca82c755f97fc90481072bcc44a8010131b2ea7326cf03fe", "impliedFormat": 1}, {"version": "bea43a13a1104a640da0cb049db85c6993f484a6cc03660496b97824719ecc91", "impliedFormat": 1}, {"version": "0224239d61fe66d4900544d912b2e11c2cca24b4707d53fdb94b874a01e29f48", "impliedFormat": 1}, {"version": "2bce8fd2d16a9432110bbe0ba1e663fd02f7d8b8968cd10178ea7bc306c4a5df", "impliedFormat": 1}, {"version": "9c4ad63738346873d685e5c086acbf41199e7022eff5b72bb668931e9ca42404", "impliedFormat": 1}, {"version": "cfb6329bf8ce324e83fe4bbdee537d866a0d5328246f149a0958b75d033de409", "impliedFormat": 1}, {"version": "efc3816f19ea87a7050c84271ea3d3aad9631a517c168013c4f4b6724c287ce0", "impliedFormat": 1}, {"version": "f99f6737336140047e8dd4ade3859f08331aa4b17bc2bd5f156a25c54e0febbc", "impliedFormat": 1}, {"version": "12a2b25c7c9c05c8994adf193e65749926acfcc076381f7166c2f709a97bdf0a", "impliedFormat": 1}, {"version": "0f93a3fdd517c1e45218cd0027c1d6b82237e379dc6b66d693aab1fe74c82e81", "impliedFormat": 1}, {"version": "03c753da0bee80ad0d0f1819b9b42dfe9bf9f436664caf15325aa426246fd891", "impliedFormat": 1}, {"version": "18f5bf1dae429c451f20171427c9e3223fade4346af4dfd817725cbeb247a09d", "impliedFormat": 1}, {"version": "a4eece5fab202e840dd84f7239e511017a8162edb8fc8b54ff2851c5c844125c", "impliedFormat": 1}, {"version": "c4a94af483a63bf947d89f97553a55df5107c605ec8a26f0b9b8bdcc14bd6d89", "impliedFormat": 1}, {"version": "19de2915ccebc0a1482c2337b34cb178d446def2493bf775c4018a4ea355adb8", "impliedFormat": 1}, {"version": "9be8fc03c8b5392cd17d40fd61063d73f08d0ee3457ecf075dcb3768ae1427bd", "impliedFormat": 1}, {"version": "3b568b63f0e8b3873629a4d7a918dce4266ad41461004ab979f8dcdfd13532bb", "impliedFormat": 1}, {"version": "a5e5223c775fe30d606b8aaa521953c925d5ad176a531c2b69437d2461aaabbd", "impliedFormat": 1}, {"version": "8cbf41d2d1ce8ac2066783ae00613c33feef07493796f638e30beaf892e4354a", "impliedFormat": 1}, {"version": "e22ad737718160df198cd428f18da707177d0467934cecdeed4be6e067b0c619", "impliedFormat": 1}, {"version": "15bf5ed8cb7c1a1e1db53fa9b45bc1a1c73c0497735343a8d0c59fdb596a3744", "impliedFormat": 1}, {"version": "791fce84bce8b6948e4f23422d9cbbd7d08c74b3f91cca12dcae83d96079798b", "impliedFormat": 1}, {"version": "8a2619c8e24305f6b9700b35af178394b995dcb28690a57a71cca87ee7e709ae", "impliedFormat": 1}, {"version": "f95fd2fc3cc164921a891f5d6c935fa0d014a576223dd098fc64677e696b0025", "impliedFormat": 1}, {"version": "8c9cecaaa9caba9a8caa47f46dcf24b524b27899b286d8edcc75a81b370d2ba3", "impliedFormat": 1}, {"version": "2b7a82692ecc877c5379df9653902e23f2d0d0bc9f210ec3cf9e47be54413c5c", "impliedFormat": 1}, {"version": "e2ad09c011cf9d7ee128875406bef787eeb504659495f42656a0098c15fe646c", "impliedFormat": 1}, {"version": "eb518567ea6b0b2623f9a6d37c364e1b1ac9d8b508d79e558f64ac05c17e2685", "impliedFormat": 1}, {"version": "630a48fb8f6b07161588e0aee3f9d301c59c97e1532c884118f89368baf4073b", "impliedFormat": 1}, {"version": "14736c608aa46120f8d6d0bc5e0721b46b927bc7eba20e479600571935f27062", "impliedFormat": 1}, {"version": "7574803692d2230db13205a7749b9c3587dccaccdf9e76f003f9e08078bb6d09", "impliedFormat": 1}, {"version": "f3cc1588e666651c51353b1728460bee8acbc6e0f36be8c025eaaf292dca525d", "impliedFormat": 1}, {"version": "0d4ea8a20527dcf3ad6cf1bd188b8ad4e449df174fad09b9e540ed81080af834", "impliedFormat": 1}, {"version": "aa82876d59912d25becff5a79ed7341af04c71bfeb2221cc0417bc34531125e2", "impliedFormat": 1}, {"version": "6f4b0389f439adc84cba35d45428668eabcfbdd351ba17e459d414ca51ab8eb8", "impliedFormat": 1}, {"version": "d5dd33d15fbb07668c264b38065ac542a07a7650af4917727bbc09b58570e862", "impliedFormat": 1}, {"version": "7d90202d0212e9cdc91a20bfddf04a539c89f09fe1d64db3343546fa2eb37e71", "impliedFormat": 1}, {"version": "1a5d073c95a3a4480b17d2fa7fd41862a9df0cb2afaee86834b13649e96bdb45", "impliedFormat": 1}, {"version": "2092495a5b3116c760527a690c4529748f2d8b126cdd5f56b2ce2230b48aba3f", "impliedFormat": 1}, {"version": "620b29d6adbd4061bc0a8fedf145fcc8e8fc9648fb6e0a39726e33babb4e07bc", "impliedFormat": 1}, {"version": "931eda51b5977f7f3fa7a0d9afde01cfd8b0cc1df0bb66dcf8c2cf6e7090384e", "impliedFormat": 1}, {"version": "b084a412374bdd124048c52c4e8a82d64f3adec6c0a9ad5ecbb7317636039b0f", "impliedFormat": 1}, {"version": "11199daa694c3ced3cc2a382a3fa7bd64e95eb40f9bbc3979fc8fb43f5ba38cc", "impliedFormat": 1}, {"version": "2c86f279d7db3c024de0f21cd9c8c2c972972f842357016bfbbd86955723b223", "impliedFormat": 1}, {"version": "dfb53b9d748df3e140b0fddb75f74d21d7623e800bb1f233817a1a2118d4bb24", "impliedFormat": 1}, {"version": "8cfc293b33082003cacbf7856b8b5e2d6dd3bde46abbd575b0c935dc83af4844", "impliedFormat": 1}, {"version": "7730c538d6d35efe95d2c0d246b1371565b13037e893178033360b4c9d2ac863", "impliedFormat": 1}, {"version": "b256694544b0d45495942720852d9597116979d52f2b53c559fda31f635c60df", "impliedFormat": 1}, {"version": "794e8831c68cc471671430ee0998397ea7a62c3b706b30304efdc3eaff77545a", "impliedFormat": 1}, {"version": "9cfc1b227477e31988e3fb18d26b6988618f4a5da9b7da6bc3df7fc12fb2602e", "impliedFormat": 1}, {"version": "264a292b6024567dd901fdabbf3239a8742bea426432cdbda4cf390b224188e1", "impliedFormat": 1}, {"version": "f1556a28bb8e33862dcfa9da7e6f1dca0b149faf433fe6a50153ae76f3362db1", "impliedFormat": 1}, {"version": "1d321aea1c6a77b2a44e02e5c2aeff290e3f1675ead1a86652b6d77f5fea2b32", "impliedFormat": 1}, {"version": "4910efc2ce1f96d6e71a9e7c9437812ffae5764b33ab3831c614663f62294124", "impliedFormat": 1}, {"version": "e3ceab51a36e8b34ab787af1a7cf02b9312b6651bac67c750579b3f05af646c1", "impliedFormat": 1}, {"version": "baf9f145bcee1b765bed6e79fd45e1ff0ca297a81315944de81eb5d6fff2d13d", "impliedFormat": 1}, {"version": "2afd62362b83db93cd20de22489fe4d46c6f51822069802620589a51ccad4b99", "impliedFormat": 1}, {"version": "9f0cd9bd4ab608123b88328c78814738cbdee620f29258b89ef8cd923f07ff9c", "impliedFormat": 1}, {"version": "801186c9e765583c825f28dab63a7ad12db5609e36dc6d9acbdc97d23888a463", "impliedFormat": 1}, {"version": "96c515141c6135ccd6fb655fb9e3500074a9216ba956fb685dc8edc33f689594", "impliedFormat": 1}, {"version": "416af6d65fc76c9ced6795f255cb1096c9d7947bede75b82289732b74d902784", "impliedFormat": 1}, {"version": "a280c68b128ebba35fb044965d67895201c2f83b6b28281bb8b023ade68bf665", "impliedFormat": 1}, {"version": "6fa118f15723b099a41d3beea98ed059bcd1b3eda708acf98c5eff0c7e88832f", "impliedFormat": 1}, {"version": "dcbf582243e20ea50d283f28f4f64e9990b4ed4a608757e996160c63cff6aa99", "impliedFormat": 1}, {"version": "efa432d8fd562529c4e9f859fd936676dd8fef5d3b4bedb06f754e4740056ea9", "impliedFormat": 1}, {"version": "a59b66720b2ccf2e0150fafb49e8da8dabdf4e1be36244a4ccd92f5bd18e1e9e", "impliedFormat": 1}, {"version": "c657fb1ec3b727d6a14a24c71ea20c41cb7d26a503e8e41b726bb919eb964534", "impliedFormat": 1}, {"version": "50d6d3174868f6e974355bf8e8db8c8b3fcf059315282a0c359ecf799d95514a", "impliedFormat": 1}, {"version": "86bf79091014a1424fc55122caa47f08622b721a4d614b97dd620e3037711541", "impliedFormat": 1}, {"version": "7a63313dff3a57f824a926e49a7262f7bd14e0e833cf45fa5af6da25286769c2", "impliedFormat": 1}, {"version": "36dcaeffe1a1aed1cb84d4feba32895bf442795170edccc874fa32232b2354e5", "impliedFormat": 1}, {"version": "686c6962d04d90edafc174aa5940acb9c9db8949c8d425131c01d796cf9a3aef", "impliedFormat": 1}, {"version": "2b1dbc3d5762d6865744b6e7be94b8b9004097698c37e93e06983e42dd8fe93b", "impliedFormat": 1}, {"version": "eb5e8f74826bdf3a6a0644d37a0f48133f8ad0b5298cc2c574102868542ba4eb", "impliedFormat": 1}, {"version": "c6a82a9673ba517cf04dd0803513257d0adf101aed2e3b162a54d840c9a1a3b2", "impliedFormat": 1}, {"version": "fc9f0f415abaa323efcecc4a4e0b6763bfe576e32043546d44f1de6541b6399b", "impliedFormat": 1}, {"version": "2c4d772ac7ac56a44deef82903364eb7c78dd7bc997701123df0ce4639fe39bb", "impliedFormat": 1}, {"version": "9369ef11eed17c1c223fdea9c0fa39e83f3722914ef390b1448db3d71620c93a", "impliedFormat": 1}, {"version": "aa84130dbc9049bba6095f87932138698f53259b642635f6c9e92dd0ddc7512c", "impliedFormat": 1}, {"version": "084ceadd21efabd4b58667dca00d4f644306099151d2ee18cd28a395855b8009", "impliedFormat": 1}, {"version": "b9503e29f06c99b352b7cae052da19e3599fa42899509d32b23a27c9bb5bebf6", "impliedFormat": 1}, {"version": "75188920fe6ccc14070fe9a65c036049f1141d968c627b623d4a897ec3587e15", "impliedFormat": 1}, {"version": "e2e1df7f45013d2b34f8d08e6ae5a9339724b0ea251b5445fcca3e170e640105", "impliedFormat": 1}, {"version": "af06feb5d18a6ea11c088b683bdb571800d1f76b98d848eecdf41e5ec8f317fd", "impliedFormat": 1}, {"version": "0596af52b95e0c8adc2c07f49f109d746b164739c5866fa8bb394dd6329a3725", "impliedFormat": 1}, {"version": "c3365d08fe7a1ccc3b8e8638edc30123007f3241b4604e2585b9f14422ab97d8", "impliedFormat": 1}, {"version": "a7a3d96b04bb0ec8cb7d2669767c4756f97dd70d08548f9e6522dde4de8e8a03", "impliedFormat": 1}, {"version": "745e960e885a4ba04c872225cbb44bd67a7490d169ceaefab7c0dfc444768676", "impliedFormat": 1}, {"version": "0b1ce1768cde3535493a9daf99e3bbb8c7dcc3a7f9d8cd358cb846af71ce5cdf", "impliedFormat": 1}, {"version": "48b9603f6e8a7c94b727277592a089f94261baa64e6c9d18165da0481663a69e", "impliedFormat": 1}, {"version": "3c20a3bb0c50c819419f44aa55acc58476dad4754a16884cef06012d02b0722f", "impliedFormat": 1}, {"version": "4dc64902cb86e677a928293593658fbf53388f9a30d2b934140c70a7267b07ec", "impliedFormat": 1}, {"version": "cb4fd56539a61d163ea9befe6b0292c32aa68a104c1f68f61416f1bc769bcfba", "impliedFormat": 1}, {"version": "0d852bdc2b72b22393a8eebe374ee3efe3e0d44e630037b5e1b6087985388e62", "impliedFormat": 1}, {"version": "b6c9a2deefb6a57ff68d2a38d33c34407b9939487fc9ee9f32ba3ecf2987a88a", "impliedFormat": 1}, {"version": "f6b371377bab3018dac2bca63e27502ecbd5d06f708ad7e312658d3b5315d948", "impliedFormat": 1}, {"version": "faa72893e85cb8ebb1dafde6b427e5204e60bb5f3ee6576bb64c01db1f255bc8", "impliedFormat": 1}, {"version": "95b7ed47b31a6eaddcdd853ee0871f2bb61e39ce36a01d03dfafb83766f6c10c", "impliedFormat": 1}, {"version": "19287d6b76288c2814f1633bdd68d2b76748757ffd355e73e41151644e4773d6", "impliedFormat": 1}, {"version": "fc4e6ec7dade5f9d422b153c5d8f6ad074bd9cc4e280415b7dc58fb5c52b5df1", "impliedFormat": 1}, {"version": "3aea973106e1184db82d8880f0ca134388b6cbc420f7309d1c8947b842886349", "impliedFormat": 1}, {"version": "765e278c464923da94dda7c2b281ece92f58981642421ae097862effe2bd30fa", "impliedFormat": 1}, {"version": "de260bed7f7d25593f59e859bd7c7f8c6e6bb87e8686a0fcafa3774cb5ca02d8", "impliedFormat": 1}, {"version": "d95c4eaad4df9e564859f0c74a177fa0b2e5f8a155939b52580566ab6b311c3f", "impliedFormat": 1}, {"version": "7192a6d17bfa06e83ba14287907b7c671bef9b7111c146f59c6ea753cfc736b9", "impliedFormat": 1}, {"version": "5156d3d392db5d77e1e2f3ea723c0a8bd3ca8acffe3b754b10c84b12f55a6e10", "impliedFormat": 1}, {"version": "a6494e7833ee04386a9f0c686726f7cb05f52f6e069d9293475ccb1e791ee0da", "impliedFormat": 1}, {"version": "d9af0c89a310256851238f509a22aa1071a464d35dc22ea8c2a0bae42dd81bc5", "impliedFormat": 1}, {"version": "291642a66e55e6ca38b029bc6921c7301f5c7b7acf21ae588a5f352e6c1f6d58", "impliedFormat": 1}, {"version": "43cd7c37298b051d1ce0307d94105bcd792c6c7e017282c9d13f1097c27408e8", "impliedFormat": 1}, {"version": "e00d8cce6e2e627654e49c543b582568ad0bf27c1d4ad1018d26aff78d7599df", "impliedFormat": 1}, {"version": "ed13354f0d96fb6d5878655b1fead51722b54875e91d5e53ef16de5b71a0e278", "impliedFormat": 1}, {"version": "fcb934d0fcdee06a8571bd90aa3a63aa288c784b3ebcecfe7ae90d3104d321f4", "impliedFormat": 1}, {"version": "af682dfabe85688289b420d939020a10eb61f0120e393d53c127f1968b3e9f66", "impliedFormat": 1}, {"version": "0dca04006bf13f72240c6a6a502df9c0b49c41c3cab2be75e81e9b592dcd4ea8", "impliedFormat": 1}, {"version": "7dc0b5e3d7be8e1f451f0545448c2eaa02683f230797d24434b36f9820d5a641", "impliedFormat": 1}, {"version": "247af61cdc3f4ec7876b9e993a2ecdd069e10934ff790c9cee5811842bff49eb", "impliedFormat": 1}, {"version": "4be8c2c63d5cd1381081d90021ddfaef106881df4129eddeeaba906f2d0f75d0", "impliedFormat": 1}, {"version": "012f621d6eb28172afb1b2dc23898d8bc74cf35a6d76b63e5581aa8e50fa71b3", "impliedFormat": 1}, {"version": "3a561fa91097e4580c5349ce72e69d247c31c11d29f39e1d0bd3716042ff2c0b", "impliedFormat": 1}, {"version": "bc9981a79dda3badea61d716d368a280c370267e900f43321f828495f4fef23c", "impliedFormat": 1}, {"version": "2ed3b93d55aea416d7be8d49fe25016430caab0fe64c87d641e4c2c551130d17", "impliedFormat": 1}, {"version": "3d66dfc31dd26092c3663d9623b6fc5cec90878606941a19e2b884c4eacd1a24", "impliedFormat": 1}, {"version": "6916c678060af14a8ce8d78a1929d84184e9507fba7ab75142c1bcb646e1c789", "impliedFormat": 1}, {"version": "3eea74afae095028597b3954bde69390f568afc66d457f64fff56e416ea47811", "impliedFormat": 1}, {"version": "549fb2d19deb7d7cae64922918ddddf190109508cc6c7c47033478f7359556d2", "impliedFormat": 1}, {"version": "e7023afc677a74f03f8ccb567532fe9eedd1f5241ee74be7b75ac2336514f6f6", "impliedFormat": 1}, {"version": "ff55505622eac7d104b9ab9570f4cc67166ba47dd8f3badfb85605d55dd6bdc9", "impliedFormat": 1}, {"version": "102fac015b1eebfa13305cb90fd91a4f0bbcabb10f2343556b3483bbb0a04b62", "impliedFormat": 1}, {"version": "18a1f4493f2dbad5fd4f7d9bfba683c98cf5ed5a4fa704fa0d9884e3876e2446", "impliedFormat": 1}, {"version": "f57e6707d035ab89a03797d34faef37deefd3dd90aa17d90de2f33dce46a2c56", "impliedFormat": 1}, {"version": "cc8b559b2cf9380ca72922c64576a43f000275c72042b2af2415ce0fb88d7077", "impliedFormat": 1}, {"version": "1a337ca294c428ba8f2eb01e887b28d080ee4a4307ae87e02e468b1d26af4a74", "impliedFormat": 1}, {"version": "310fe80ff40a158c2de408efbe9de11e249c53d2de5e33ca32798e6f3fbc8822", "impliedFormat": 1}, {"version": "d6ce96c7bb34945c1d444101f44e0f8ba0bba8ab7587a6cc009a9934b538c335", "impliedFormat": 1}, {"version": "1b10a2715917601939a9288d49beccd45b591723256495b229569cd67bbe48a8", "impliedFormat": 1}, {"version": "7498dfdeed2e003ec49cdf726ff6c293002d1d7fdadbc398ce8aafe6d0688de7", "impliedFormat": 1}, {"version": "8492306a4864a1dc6fc7e0cc0de0ae9279cbd37f3aae3e9dc1065afcdc83dddc", "impliedFormat": 1}, {"version": "9c86abbc4fd0248f56abc12aaecd76854517389af405d5ec2eb187fdb00a606f", "impliedFormat": 1}, {"version": "9ffd906f14f8b059d6b95d6640920f530507e596e548f7a595da58ab66e3ce76", "impliedFormat": 1}, {"version": "1884bccc10ce40adca470c2c371c1c938b36824f169c56f7f43d860416ca0a4c", "impliedFormat": 1}, {"version": "986b55b4f920c99d77c1845f2542df6f746cb5adc9ab93eb1545a7e6ef37590d", "impliedFormat": 1}, {"version": "cd00906068b81fbd8a22d021580ac505e272844408174520fafed0ae00627a5d", "impliedFormat": 1}, {"version": "69fab68a769c17a52a24b868aeb644f3ee14abaa5064115f575ddd59231105ce", "impliedFormat": 1}, {"version": "e181eb86b2caf80fe18c72efce6b913bc226e4a69a5456eaf4f859f1c29c6fd6", "impliedFormat": 1}, {"version": "93f7871380478bc6acf02ad9f3dc7da0c21997caebbe782eb93a11b7bd06a46d", "impliedFormat": 1}, {"version": "d00279ab020713264f570d5181c89ca362b7de8abddf96733de86bce0eca082c", "impliedFormat": 1}, {"version": "f7db473f1d5d2a124f14886ac9dbfeccfbb94a98bbe1610a47c30c2933afa279", "impliedFormat": 1}, {"version": "f44cf6c6d608ef925831e550b19841b5d71bd87195bd346604ff05644fb0d29c", "impliedFormat": 1}, {"version": "154f23902d7a3fcdace4c20b654da7355fee4b7f807d1f77d6c9a24a8756013a", "impliedFormat": 1}, {"version": "562f4f3c75a497d3ad7709381f850bb8c7646a9c6e94fdf8e91928e23d155411", "impliedFormat": 1}, {"version": "4583380b676ee59b70a9696b42acfa986cd5f32430f37672e04f31f40b05df74", "impliedFormat": 1}, {"version": "ad0a13f35a0d88803979f8ea9050ad7441e09d21a509abf2f303e18c1267af17", "impliedFormat": 1}, {"version": "ba9781c718ab3d09cbde1216029072698d2da6135f0d2f856ba387d6caceb13e", "impliedFormat": 1}, {"version": "d7c597c14698ba5fc8010076afa426f029b2d8edabb5073270c070cc645ba638", "impliedFormat": 1}, {"version": "bd2afc69cf1d85cd950a99813bc7eff007d8afa496e7c2142a845cd1181d0474", "impliedFormat": 1}, {"version": "558b462b23ea186d094dbff158d652acd58c0988c9fd53af81a8903412aa5901", "impliedFormat": 1}, {"version": "0e984ae642a15973d652fd7b0d2712a284787d0d7a1db99aa49af0121e47f1df", "impliedFormat": 1}, {"version": "0ad53ee208a23eef2a5cb3d85f2a9dc1019fd5e69179c4b0c02dc56c40d611c4", "impliedFormat": 1}, {"version": "7a6898b26947bd356f33f4efef3eb23e61174d85dca19f41a8780d6bb4bfb405", "impliedFormat": 1}, {"version": "9fe30349d26f34e85209fb06340bac34177f7eae3d6bb69dc12cd179d2c13ddf", "impliedFormat": 1}, {"version": "d568c51d2c4360fd407445e39f4d86891dba04083402602bf5f24fd3969cacbb", "impliedFormat": 1}, {"version": "b2483a924349ec835f4d778dd6787447a2f8bfbb651164851bff29d5b3d990a6", "impliedFormat": 1}, {"version": "aae66889332cff4b2f7586c5c8758abc394d8d1c48f9b04b0c257e58f629d285", "impliedFormat": 1}, {"version": "0f86c85130c64d6dbe6a9090bb3df71c4b0987bce4a08afe1ac4ece597655b9c", "impliedFormat": 1}, {"version": "0ce28ad2671baed24517e1c1f4f2a986029137635bce788ee8fb542f002ac5b8", "impliedFormat": 1}, {"version": "cd12e4fe77d24db98d66049360a4269299bcfb9dc3a1b47078ab1b4afac394cb", "impliedFormat": 1}, {"version": "1589e5ac394b2b2e64264da3e1798d0e103b4f408f5bae1527d9e706f98269c7", "impliedFormat": 1}, {"version": "ff8181aa0fde5ec2d737aecc5ebaa9e881379041f13e5ce1745620e17f78dcf9", "impliedFormat": 1}, {"version": "0b2e54504b568c08df1e7db11c105786742866ba51e20486ab9b2286637d268f", "impliedFormat": 1}, {"version": "bc1ffc3a2dca8ee715571739be3ec74d079e60505e1d0d2446e4978f6c75ba5c", "impliedFormat": 1}, {"version": "770a40373470dff27b3f7022937ea2668a0854d7977c9d22073e1c62af537727", "impliedFormat": 1}, {"version": "a0f8ce72cb02247a112ce4a2fa0f122478a8e99c90a5e6b676b41a68b1891ad2", "impliedFormat": 1}, {"version": "6e957ea18b2bf951cf3995d115ad9bfa439e8d891aeb1afc901d793202c0b90d", "impliedFormat": 1}, {"version": "a1c65bd78725f9172b5846c3c58ddf4bcbb43a30ab19e951f0102552fbfd3d5d", "impliedFormat": 1}, {"version": "04718c7325e7df4bac9a6d026a0a2bd5a8b54501f274aaf93a03b5d1d0635bd1", "impliedFormat": 1}, {"version": "405205f932d4e0ce688a380fa3150b1c7ff60e7fc89909e11a33eab7af240edb", "impliedFormat": 1}, {"version": "566fc1a6616a522f8b45082032a33e6d37ff7df3f7d4d63c3cce9017d0345178", "impliedFormat": 1}, {"version": "3b699b08db04559803b85aa0809748e61427b3d831f77834b8206e9f2ed20c93", "impliedFormat": 1}, {"version": "b27242dd3af2a5548d0c7231db7da63d6373636d6c4e72d9b616adaa2acef7e1", "impliedFormat": 1}, {"version": "e0ee7ba0571b83c53a3d6ec761cf391e7128d8f8f590f8832c28661b73c21b68", "impliedFormat": 1}, {"version": "072bfd97fc61c894ef260723f43a416d49ebd8b703696f647c8322671c598873", "impliedFormat": 1}, {"version": "e70875232f5d5528f1650dd6f5c94a5bed344ecf04bdbb998f7f78a3c1317d02", "impliedFormat": 1}, {"version": "8e495129cb6cd8008de6f4ff8ce34fe1302a9e0dcff8d13714bd5593be3f7898", "impliedFormat": 99}, "a9ceeae30a23dea45cde506f245da1f4a0024f780836d03d2ddd7dd39ced1d0a", "4a1dec565342ccd13e7b15d94eda91b7953ff63d3b812a6da59ed7909505cdc8", "46ec5fdad8a1e4e390887d6e777a8da467619b16fd94411b8c5121204f5006e8", "884c9d5cc41f695e515b7b8838fd76d32297c232ceab938d4bce72db8c6f89e6", "a94564fd508325e999722c226316b393d6726b14fed772ceeaae50f93038f97d", "187688f755562f4494e456ccf949865e190f02f3b0b9ea4d5f97fbb1601e823c", "427f1e343daa002d3f7ab565394caf0fb1efd7ab71b890999db24eb8d9c287c8", "dde139c9dc5a2bec0d6a81800e4e0f669573d8b33b677330eab0ff5faaec00cb", "fa1690a9c4f7b088daa60126e5bbbaa40ef49a3f71880aad3fadb6b5e2a53c27", "0ad37f5b5df2ba161a733e36dbc2820c6fd5078c57a480a22b5a0877a00188b3", "736722562afe6c1a3130c9cd03040ece2cfbed727578ebbf52d2d7bc29e37be4", "808a34c6c9e6c0d57006e691473c74609f0dacfe8ce588cbe138db4b25939097", "85a10b916f14bcaa9f9051e5a3325024c0f3f301012c47439b0859e939e4c98d", "6027e9a0fc5e99d4f6aefae01b73801eaedfa22c6c6f265892f43241c475c62a", "e0197a906d063fba1e3efaff4a05860bf4533ec19a2090f8c0d8bf71f7442914", "17a8b6ddd6bf03d2d394ddb71791cd0022fcd996cfa35771a03338a7382f8d55", "2af6343482b204fad54817dceb273912f010838bbdd55df7f84e1a166dec7770", "4e56e4df9454cec00a9e8070415cc7a271e61ee4c28305794d5ffa34df7f3df8", "a7446c88cfbc46f23e7f3139764549314b982d1c92acf676f25aae57f89aaf5a", "468ac622029242faea1053cb4852e17564e7a2db86827dcb8cbcf09b37ba6743", "7e3e1ea763b980ca2c8e902dc8e400b4c6450a31e8f61cd34d717ae9a780ea5e", "96c5a15be70123c62e2dd54366a6c3f2d0d15e0d737d004150e12098bc840c51", "d8e7c4b4d21498f8817b64526201a67e711efb154de9eed1de7b31d38e11dcf9", "1da9345774b7a7b99d3cac1c6864de0384c87b6a936618dce72cc181cc46e9fc", "559d326c440035c881c8ea4a7e9648043cd779857933d65b59991c4d765e7616", "0437a7c3f261fa76245defe2342e3be09abf831eb69a5269183c49dd58fb3aa4", "3fdc9f95dc66237c3ea1ae8c9ae1acc50d368fe86e487ea9cace85af3c1712e4", "cdbbb142d0f8e44c2bd13e83fc28eb68eeff16851eab48713cd311f907003439", "9081b3e5cee795a78cc6e23c34ce12d7e89806f7ab501b4f6049823eca21da4c", {"version": "7e3373dde2bba74076250204bd2af3aa44225717435e46396ef076b1954d2729", "impliedFormat": 1}, {"version": "1c3dfad66ff0ba98b41c98c6f41af096fc56e959150bc3f44b2141fb278082fd", "impliedFormat": 1}, {"version": "56208c500dcb5f42be7e18e8cb578f257a1a89b94b3280c506818fed06391805", "impliedFormat": 1}, {"version": "0c94c2e497e1b9bcfda66aea239d5d36cd980d12a6d9d59e66f4be1fa3da5d5a", "impliedFormat": 1}, {"version": "eb9271b3c585ea9dc7b19b906a921bf93f30f22330408ffec6df6a22057f3296", "impliedFormat": 1}, {"version": "0205ee059bd2c4e12dcadc8e2cbd0132e27aeba84082a632681bd6c6c61db710", "impliedFormat": 1}, {"version": "a694d38afadc2f7c20a8b1d150c68ac44d1d6c0229195c4d52947a89980126bc", "impliedFormat": 1}, {"version": "9f1e00eab512de990ba27afa8634ca07362192063315be1f8166bc3dcc7f0e0f", "impliedFormat": 1}, {"version": "9674788d4c5fcbd55c938e6719177ac932c304c94e0906551cc57a7942d2b53b", "impliedFormat": 1}, {"version": "86dac6ce3fcd0a069b67a1ac9abdbce28588ea547fd2b42d73c1a2b7841cf182", "impliedFormat": 1}, {"version": "4d34fbeadba0009ed3a1a5e77c99a1feedec65d88c4d9640910ff905e4e679f7", "impliedFormat": 1}, {"version": "9d90361f495ed7057462bcaa9ae8d8dbad441147c27716d53b3dfeaea5bb7fc8", "impliedFormat": 1}, {"version": "8fcc5571404796a8fe56e5c4d05049acdeac9c7a72205ac15b35cb463916d614", "impliedFormat": 1}, {"version": "a3b3a1712610260c7ab96e270aad82bd7b28a53e5776f25a9a538831057ff44c", "impliedFormat": 1}, {"version": "33a2af54111b3888415e1d81a7a803d37fada1ed2f419c427413742de3948ff5", "impliedFormat": 1}, {"version": "d5a4fca3b69f2f740e447efb9565eecdbbe4e13f170b74dd4a829c5c9a5b8ebf", "impliedFormat": 1}, {"version": "56f1e1a0c56efce87b94501a354729d0a0898508197cb50ab3e18322eb822199", "impliedFormat": 1}, {"version": "8960e8c1730aa7efb87fcf1c02886865229fdbf3a8120dd08bb2305d2241bd7e", "impliedFormat": 1}, {"version": "27bf82d1d38ea76a590cbe56873846103958cae2b6f4023dc59dd8282b66a38a", "impliedFormat": 1}, {"version": "0daaab2afb95d5e1b75f87f59ee26f85a5f8d3005a799ac48b38976b9b521e69", "impliedFormat": 1}, {"version": "2c378d9368abcd2eba8c29b294d40909845f68557bc0b38117e4f04fc56e5f9c", "impliedFormat": 1}, {"version": "bb220eaac1677e2ad82ac4e7fd3e609a0c7b6f2d6d9c673a35068c97f9fcd5cd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c60b14c297cc569c648ddaea70bc1540903b7f4da416edd46687e88a543515a1", "impliedFormat": 1}, {"version": "94a802503ca276212549e04e4c6b11c4c14f4fa78722f90f7f0682e8847af434", "impliedFormat": 1}, {"version": "9c0217750253e3bf9c7e3821e51cff04551c00e63258d5e190cf8bd3181d5d4a", "impliedFormat": 1}, {"version": "5c2e7f800b757863f3ddf1a98d7521b8da892a95c1b2eafb48d652a782891677", "impliedFormat": 1}, {"version": "21317aac25f94069dbcaa54492c014574c7e4d680b3b99423510b51c4e36035f", "impliedFormat": 1}, {"version": "c61d8275c35a76cb12c271b5fa8707bb46b1e5778a370fd6037c244c4df6a725", "impliedFormat": 1}, {"version": "c7793cb5cd2bef461059ca340fbcd19d7ddac7ab3dcc6cd1c90432fca260a6ae", "impliedFormat": 1}, {"version": "fd3bf6d545e796ebd31acc33c3b20255a5bc61d963787fc8473035ea1c09d870", "impliedFormat": 1}, {"version": "c7af51101b509721c540c86bb5fc952094404d22e8a18ced30c38a79619916fa", "impliedFormat": 1}, {"version": "59c8f7d68f79c6e3015f8aee218282d47d3f15b85e5defc2d9d1961b6ffed7a0", "impliedFormat": 1}, {"version": "93a2049cbc80c66aa33582ec2648e1df2df59d2b353d6b4a97c9afcbb111ccab", "impliedFormat": 1}, {"version": "d04d359e40db3ae8a8c23d0f096ad3f9f73a9ef980f7cb252a1fdc1e7b3a2fb9", "impliedFormat": 1}, {"version": "84aa4f0c33c729557185805aae6e0df3bd084e311da67a10972bbcf400321ff0", "impliedFormat": 1}, {"version": "cf6cbe50e3f87b2f4fd1f39c0dc746b452d7ce41b48aadfdb724f44da5b6f6ed", "impliedFormat": 1}, {"version": "3cf494506a50b60bf506175dead23f43716a088c031d3aa00f7220b3fbcd56c9", "impliedFormat": 1}, {"version": "f2d47126f1544c40f2b16fc82a66f97a97beac2085053cf89b49730a0e34d231", "impliedFormat": 1}, {"version": "724ac138ba41e752ae562072920ddee03ba69fe4de5dafb812e0a35ef7fb2c7e", "impliedFormat": 1}, {"version": "e4eb3f8a4e2728c3f2c3cb8e6b60cadeb9a189605ee53184d02d265e2820865c", "impliedFormat": 1}, {"version": "f16cb1b503f1a64b371d80a0018949135fbe06fb4c5f78d4f637b17921a49ee8", "impliedFormat": 1}, {"version": "f4808c828723e236a4b35a1415f8f550ff5dec621f81deea79bf3a051a84ffd0", "impliedFormat": 1}, {"version": "3b810aa3410a680b1850ab478d479c2f03ed4318d1e5bf7972b49c4d82bacd8d", "impliedFormat": 1}, {"version": "0ce7166bff5669fcb826bc6b54b246b1cf559837ea9cc87c3414cc70858e6097", "impliedFormat": 1}, {"version": "6ea095c807bc7cc36bc1774bc2a0ef7174bf1c6f7a4f6b499170b802ce214bfe", "impliedFormat": 1}, {"version": "3549400d56ee2625bb5cc51074d3237702f1f9ffa984d61d9a2db2a116786c22", "impliedFormat": 1}, {"version": "5327f9a620d003b202eff5db6be0b44e22079793c9a926e0a7a251b1dbbdd33f", "impliedFormat": 1}, {"version": "b60f6734309d20efb9b0e0c7e6e68282ee451592b9c079dd1a988bb7a5eeb5e7", "impliedFormat": 1}, {"version": "f4187a4e2973251fd9655598aa7e6e8bba879939a73188ee3290bb090cc46b15", "impliedFormat": 1}, {"version": "380b919bfa0516118edaf25b99e45f855e7bc3fd75ce4163a1cfe4a666388804", "impliedFormat": 1}, {"version": "0b24a72109c8dd1b41f94abfe1bb296ba01b3734b8ac632db2c48ffc5dccaf01", "impliedFormat": 1}, {"version": "fcf79300e5257a23ed3bacaa6861d7c645139c6f7ece134d15e6669447e5e6db", "impliedFormat": 1}, {"version": "187119ff4f9553676a884e296089e131e8cc01691c546273b1d0089c3533ce42", "impliedFormat": 1}, {"version": "aa2c18a1b5a086bbcaae10a4efba409cc95ba7287d8cf8f2591b53704fea3dea", "impliedFormat": 1}, {"version": "b88749bdb18fc1398370e33aa72bc4f88274118f4960e61ce26605f9b33c5ba2", "impliedFormat": 1}, {"version": "0aaef8cded245bf5036a7a40b65622dd6c4da71f7a35343112edbe112b348a1e", "impliedFormat": 1}, {"version": "00baffbe8a2f2e4875367479489b5d43b5fc1429ecb4a4cc98cfc3009095f52a", "impliedFormat": 1}, {"version": "a873c50d3e47c21aa09fbe1e2023d9a44efb07cc0cb8c72f418bf301b0771fd3", "impliedFormat": 1}, {"version": "7c14ccd2eaa82619fffc1bfa877eb68a012e9fb723d07ee98db451fadb618906", "impliedFormat": 1}, {"version": "49c36529ee09ea9ce19525af5bb84985ea8e782cb7ee8c493d9e36d027a3d019", "impliedFormat": 1}, {"version": "df996e25faa505f85aeb294d15ebe61b399cf1d1e49959cdfaf2cc0815c203f9", "impliedFormat": 1}, {"version": "4f6a12044ee6f458db11964153830abbc499e73d065c51c329ec97407f4b13dd", "impliedFormat": 1}, {"version": "44c1a26f578277f8ccef3215a4bd642a0a4fbbaf187cf9ae3053591c891fdc9c", "impliedFormat": 1}, {"version": "a5989cd5e1e4ca9b327d2f93f43e7c981f25ee12a81c2ebde85ec7eb30f34213", "impliedFormat": 1}, {"version": "f65b8fa1532dfe0ef2c261d63e72c46fe5f089b28edcd35b3526328d42b412b8", "impliedFormat": 1}, {"version": "1060083aacfc46e7b7b766557bff5dafb99de3128e7bab772240877e5bfe849d", "impliedFormat": 1}, {"version": "d61a3fa4243c8795139e7352694102315f7a6d815ad0aeb29074cfea1eb67e93", "impliedFormat": 1}, {"version": "1f66b80bad5fa29d9597276821375ddf482c84cfb12e8adb718dc893ffce79e0", "impliedFormat": 1}, {"version": "1ed8606c7b3612e15ff2b6541e5a926985cbb4d028813e969c1976b7f4133d73", "impliedFormat": 1}, {"version": "c086ab778e9ba4b8dbb2829f42ef78e2b28204fc1a483e42f54e45d7a96e5737", "impliedFormat": 1}, {"version": "dd0b9b00a39436c1d9f7358be8b1f32571b327c05b5ed0e88cc91f9d6b6bc3c9", "impliedFormat": 1}, {"version": "a951a7b2224a4e48963762f155f5ad44ca1145f23655dde623ae312d8faeb2f2", "impliedFormat": 1}, {"version": "cd960c347c006ace9a821d0a3cffb1d3fbc2518a4630fb3d77fe95f7fd0758b8", "impliedFormat": 1}, {"version": "fe1f3b21a6cc1a6bc37276453bd2ac85910a8bdc16842dc49b711588e89b1b77", "impliedFormat": 1}, {"version": "1a6a21ff41d509ab631dbe1ea14397c518b8551f040e78819f9718ef80f13975", "impliedFormat": 1}, {"version": "0a55c554e9e858e243f714ce25caebb089e5cc7468d5fd022c1e8fa3d8e8173d", "impliedFormat": 1}, {"version": "3a5e0fe9dcd4b1a9af657c487519a3c39b92a67b1b21073ff20e37f7d7852e32", "impliedFormat": 1}, {"version": "977aeb024f773799d20985c6817a4c0db8fed3f601982a52d4093e0c60aba85f", "impliedFormat": 1}, {"version": "d59cf5116848e162c7d3d954694f215b276ad10047c2854ed2ee6d14a481411f", "impliedFormat": 1}, {"version": "50098be78e7cbfc324dfc04983571c80539e55e11a0428f83a090c13c41824a2", "impliedFormat": 1}, {"version": "08e767d9d3a7e704a9ea5f057b0f020fd5880bc63fbb4aa6ffee73be36690014", "impliedFormat": 1}, {"version": "dd6051c7b02af0d521857069c49897adb8595d1f0e94487d53ebc157294ef864", "impliedFormat": 1}, {"version": "79c6a11f75a62151848da39f6098549af0dd13b22206244961048326f451b2a8", "impliedFormat": 1}, "d4c1be1475d61d90c1a5b058e5caa0f7eaad6513b9f12075f64b66b53734a989", "dcb17d765beaa01b33895c5ddb39086ff9a0c2b0975a7df65156754a6d44212d", {"version": "6d575d93896c413b308c3726eed99ddd17e821a00bdd2cc5929510b46fe64de4", "impliedFormat": 99}, "24ca05cf3a9c50c61d7aeadcb133dcfa39aff2ac4c2504cb4a9283cc3113c9e6", {"version": "d04f947114fa00a20ee3c3182bb2863c30869df93293cc673f200defadbd69d9", "impliedFormat": 1}, {"version": "4c629a21fb1b4f2428660f662d5fef6282e359d369f9e5ec5fd6ac197c1906ee", "impliedFormat": 1}, {"version": "785926dee839d0b3f5e479615d5653d77f6a9ef8aa4eea5bbdce2703c860b254", "impliedFormat": 1}, {"version": "66d5c68894bb2975727cd550b53cd6f9d99f7cb77cb0cbecdd4af1c9332b01dd", "impliedFormat": 1}, {"version": "c991b4ab8278b08f73510d383ae74a9df03a876ba4aa66efa9d87d0bfdbf486b", "impliedFormat": 1}, "e354b53467d5003bc415ace2a66da43c6ef5259c034ad6e292c76a28c5f21182", {"version": "6e2669a02572bf29c6f5cea36a411c406fff3688318aee48d18cc837f4a4f19c", "impliedFormat": 1}, {"version": "796d35ad18e3f2467aaf54b9b3fd6a94c77f8f9df1b41aaefe1c3dab8ce97438", "impliedFormat": 1}, {"version": "40191405914c9e13ed32ed31eca4a74ef06be535b44594eb76b9ba04680d5031", "impliedFormat": 1}, {"version": "e27bbd0b7b7e54b3703765eebb805658672c52752342d8dfaa56820c88fc8333", "impliedFormat": 1}, {"version": "da2472f38d0822ed781c936487b660252404b621b37dd5da33759f13ba86c54e", "impliedFormat": 1}, {"version": "3a02910d744549b39a5d3f47ae69f3d34678496d36e07bd3bf27ee3c8736241c", "impliedFormat": 1}, {"version": "e4e0883cbb3029c517406d2956c0745e44403afd820e89a473485129ad66359b", "impliedFormat": 1}, {"version": "5f4138fcf24316124b815f3ab41a903ef327104836cdcb21dc91f0ca4fe28eb4", "impliedFormat": 1}, {"version": "4fd59922851bbd5b81a3a00d60538d7d6eebf8cb3484ab126c02fd80baf30df3", "impliedFormat": 1}, {"version": "76e70ccd3b742aa3c1ef281b537203232c5b4f920c4dcb06417c8e165f7ea028", "impliedFormat": 1}, {"version": "f53e235ded29e288104880b8efa5a7f57c93ca95dc2315abfbd97e0b96763af7", "impliedFormat": 1}, {"version": "b0e1cfe960f00ad8bdab0c509cf212795f747b17b96b35494760e8d1fae2e885", "impliedFormat": 1}, {"version": "a6c5c2ac61526348cfe38229080a552b7016d614df208b7c3ad2bbd8219c4a95", "impliedFormat": 1}, {"version": "9971dead65b4e7c286ed2ca96d76e47681700005a8485e3b0c72b41f03c7c4b0", "impliedFormat": 1}, {"version": "d870bf94d9274815d95f0d5658825747d3afc24bd010e607392b3f034e695199", "impliedFormat": 1}, {"version": "bbdac91149ba4f40bf869adc0e15fa41815ef212b452948fc8e773ff6ee38808", "impliedFormat": 1}, {"version": "0c2f32cb837a6de3b2bec65646a2e04f0a56cd408749cbddc016ddba732ef1a0", "impliedFormat": 1}, {"version": "ef86116cceeaf8833204f4c55e309c385622614bb052cb1534b2c26e38d466c7", "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4967529644e391115ca5592184d4b63980569adf60ee685f968fd59ab1557188", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "1ca84b44ad1d8e4576f24904d8b95dd23b94ea67e1575f89614ac90062fc67f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6d586db0a09a9495ebb5dece28f54df9684bfbd6e1f568426ca153126dac4a40", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "impliedFormat": 1}, {"version": "6c09ec7dab82153ee79c7fcc302c3510d287b86b157b76ccbb5d646233373af4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "9d19808c8c291a9010a6c788e8532a2da70f811adb431c97520803e0ec649991", "impliedFormat": 1}, {"version": "5a369483ac4cfbdf0331c248deeb36140e6907db5e1daed241546b4a2055f82c", "impliedFormat": 1}, {"version": "e8f5b5cc36615c17d330eaf8eebbc0d6bdd942c25991f96ef122f246f4ff722f", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "impliedFormat": 1}, {"version": "b76cc102b903161a152821ed3e09c2a32d678b2a1d196dabc15cfb92c53a4fd0", "impliedFormat": 1}, {"version": "de735eca2c51dd8b860254e9fdb6d9ec19fe402dfe597c23090841ce3937cfc5", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "5650cf3dace09e7c25d384e3e6b818b938f68f4e8de96f52d9c5a1b3db068e86", "impliedFormat": 1}, {"version": "1354ca5c38bd3fd3836a68e0f7c9f91f172582ba30ab15bb8c075891b91502b7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "e16d218a30f6a6810b57f7e968124eaa08c7bb366133ea34bbf01e7cd6b8c0ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb8692dea24c27821f77e397272d9ed2eda0b95e4a75beb0fdda31081d15a8ae", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "5b6844ad931dcc1d3aca53268f4bd671428421464b1286746027aede398094f2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "0dbcebe2126d03936c70545e96a6e41007cf065be38a1ce4d32a39fcedefead4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "461e54289e6287e8494a0178ba18182acce51a02bca8dea219149bf2cf96f105", "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "e31e51c55800014d926e3f74208af49cb7352803619855c89296074d1ecbb524", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "dfb96ba5177b68003deec9e773c47257da5c4c8a74053d8956389d832df72002", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "92d3070580cf72b4bb80959b7f16ede9a3f39e6f4ef2ac87cfa4561844fdc69f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "613deebaec53731ff6b74fe1a89f094b708033db6396b601df3e6d5ab0ec0a47", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "e56eb632f0281c9f8210eb8c86cc4839a427a4ffffcfd2a5e40b956050b3e042", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}, {"version": "16a684817cfa7433281c6cd908240b60c4b8fe95ca108079e2052bafbd86dca9", "impliedFormat": 1}, {"version": "480ffa66827143d60025514f0d979f7bc790024821e5ecc12967ce13a7e3e08a", "impliedFormat": 1}, {"version": "303f2d7549e1ae66106064405824e6ae141e9ff2c05ead507afff445610dbf76", "impliedFormat": 1}, {"version": "1a18fcd7ea90842d336fb814801c837368c8ad16807f167b875b89267f1c2530", "impliedFormat": 1}, {"version": "ed0c5e5f3b30334bbd99a73ee4faa47a799b4e5928114131f7b2d123f3d22ca0", "impliedFormat": 1}, {"version": "6c2ad16b31ef481da774dd641a36f124dbcedeb3653891b9869639fa6f2f4a30", "impliedFormat": 1}, {"version": "ee5e067150f421651188289a1e84f9bdf513da63cc82e8d6998b3d41a3cc39bf", "impliedFormat": 1}, "f05a26a278c51b4e456be4207d0c3fc7ffaafe9599938d502ff384d04cc98b98", "5e507030099acecc788c357f462ae98623438029cc2df92c8f7c5a8d426cc872", "f6be8dff6e9a430213d83fc55bd5fec75db7ed50d4b909644a96969fc0bc48ab", "fc65f9e637ec7b2d8960c6267cfd8cc1f63f30cd1b3e8705c67613e7f1e2b2bc", "66669b6491edeff16c38b4a610e7e3c9c0054c60205e32f2bb99ba01bb9d1e13", "c70b5317605ecd0538ecae3ca6934bcc0d4202883ca53fcc5ac0e7caae18f2ae", "8bd547a5871c8d3d6642b097921381c6ed05fd27acd1aa99378ec81e82f10b6a", {"version": "e6f8836732c6a24f1faec2539e5d98d9cd578d3d78ba893c469ab6504f85a0fa", "impliedFormat": 1}, "b23c6ae4d505b6a08dc581ffe7a2faa7f6b781296ffec62c82b0f6e9c77a8351", {"version": "26773a9f9c5106d014ee7e56462ed17902fd5e2ab55c8f31bf213cbda4afaf3b", "impliedFormat": 1}, "ee3e1c0ac4f3b38ab3deac786bc0569b3bfcc7c0ad6e4918a1894acaf9b09ddf", "c3102d6a986f837d4434ea5ec18cfb0e74230437282dd1d7a8360458d6c4ffa9", "479fdc85954e8b7204b7a3c6812c66385a8e85c2ddd9986a24be374e083850dd", "6ecfad46e03b06af9ee5ab162d4b9d1de3e758e6e1ee3fa79cf60674ee57cda6", "206200f7b16fe1c1c9ec80c0a70a4c266a57fb6e375b4210efc848961180f190", "568dfb6c7206f0358169ace6bc718f287aa06615d5c26daa95da2e5eea1d8dd1", "908ae57b3d1eb498b0a917be0f6eea1a55bab5fbf7684be6883228c009ca85ca", "ef8bbae227c2fd5feb80bec312177b1fc9de60f30f4635986ce72266ae31fc9b", "fddfbe4ce674dff6e8663ce1ede0c027cec396017c4ed0fad3b0db407a692f2f", "97e5617df37a607d815a5583ce66a4f84f86e19fca2126227b1289dc165d93b2", "c3b3e5b61b4c4c4f53a483e8f98d51adcdd0b6febab9d231bc021d2a75d8ff59", "e5540802a98b349aff8f196387c4e05ca2ec14fbc913790a392467f3710feec0", "c112ace851a415e0d01fb0f2b38e0884567b1ddf2f6983d5873916b941c0db55", "f7b5a326ae0cf7d86c84f0cae6d1935e8c8ed7b993a98f2327b1051050b499ed", "40ee2831a0d905bad778c804657053d1714fda952b229d54218cf6ec3dc66f94", "2082421d77a43a872d851de3a6c25d0e2bebe90f52b5f3d0e535ecf5d25338a9", "1121ca8b174e2b496b234a23c617017004c4183702eeb5bce531e57bcd64176e", "c1b4d2678a01d169850550159391c051f160ceea2024e564f1ca4497a0f8d39c", {"version": "2920053ac2e193a0a4384d5268540ffd54dd27769e51b68f80802ec5bba88561", "impliedFormat": 1}, {"version": "19feb8551285d9041090b2aac242de4089aa0abe3777a89ff9a804225e561832", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6bf3b5a1771b2c2af6aef2cd05212d8d01c4326c03ea0426c9534ce9e07ff286", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "97be22bf67808ba0c04841f451fda38ff1af3cdcb04913a8ead19526e74d7c05", "impliedFormat": 1}, "db9bcb75f6cfbc6bee98a3a932c4ea56b9e03bec020da027e49c9a01af16d9c9", "96bdd62dcc7e92e4579d2d4c6e3904852695602af08708b6594c3f4172aea54f", "cad2902edbc3f936de6fbf17e0979537f459f689cce88ae0b65897bdf2cfdc0a", "445daa804175f7d5b9ae4bcfdab6a2e01ff0fa1dc2a5bccc263aceda1ad5e28e", "b38b3665d9e2d765ba6e3668d6cc54a49bd907778ddcfc5bf20ba6678f69c11e", "2ae75cca24efe50fe2a93ffd48ebe97a87cbe02c114cae00885314c5d69d7239", "cfa0a7644e559045cb42c0e5807949299ee5c34774d299fbd9720b3babc07586", "12c25529dc67e99d27c910bccc88020e169dd2b4b22da66374ad2e2e0213dc73", "e7d05fd6d04aa9b047accce2e947e3671706abe7877a66099c1e16f63b93eed3", "2d089f4cb6ffd86293ae9165548e1f7c5f31130dfccb9be38c6b842e4a1c1569", "16e2a479b8c48236b860ded73c548ff63060457ffb1156fb80b2105be81c3322", "ded6b81b33ab02cbbbb671588ccd4911a475e36274e7f099694d3a2ed27ae064", "6dbbfe0724d781fd15fde3536fee95772b288229da1d237a34300b2774fd4a53", "459a4a941a6c151dc1ec9894556379ad4435f966dc6d01a369e4bffbaeb9ca57", "cd097829a9aa696a1f0c8b261676114656c8b5d804a319d8fb4b7a6baeddbe24", "2c9054964b4bfb49e4b9e6c3e419b618bb3b1e340bfe20256ee592e00b760da0", {"version": "c5ddb3e94afede269e28c295a4e9fa1505f941dda413a7f86b8558a6b7295a60", "impliedFormat": 1}, {"version": "5f343aa083ce243013572975da666042391f851a1d34a68cfe7bfd049c6d99e9", "impliedFormat": 1}, {"version": "d4383da2ec6ec8698d0cc2eca290fcb665c88e9f860dadea94ff8bfb605e24bb", "impliedFormat": 1}, {"version": "4d81f20cd152b47dbdfae3c22378f28c3e6746d154ccac305ab105a31b891d06", "impliedFormat": 1}, {"version": "ef395432526ed7290de1901e96c57c71f40d11a9ece54cbe2b0354a7e674e50f", "impliedFormat": 1}, {"version": "ff67c9a02f520e13087ae622313f686e2e3aea1c174fb41e6f76601b2447b87d", "impliedFormat": 1}, {"version": "05a2e9dc8c091c681c8ff403bc3d9375c745b80b79d079563e3455557dc7acbe", "impliedFormat": 1}, {"version": "a0f096ddb28c89a8b51828a8c2c7d7b859bad74fd1d273dff28f5f446fab1fff", "impliedFormat": 1}, {"version": "cd8817180b7f68d894f3886957e09d24123d782c31b98e3836e80808c32bf4e2", "impliedFormat": 1}, {"version": "e61c29cec79a56e346291c64f33477f484d7e531bcd357478c5a61cb454c9e71", "impliedFormat": 1}, {"version": "6d39263c05fa7499d3275c09b1cd8435b3f1f913f6963ce847232d9e010a75ae", "impliedFormat": 1}, {"version": "a47f3f9f209a174a60d028dc2d765501593bdc686458511fadfe4efe6e6fed2d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d5b873690bc007ff318cbd8d2532f1dd97c699f52fe1306824a86fba22f94ebd", "impliedFormat": 1}, {"version": "875b28fcfde2a43f7a33fad75cde926685e82fec5587798f78437071f65963be", "impliedFormat": 1}, {"version": "84cb719308517276ff26deca42dbb950dea6edc3db686cc95d8febba93e84cc0", "impliedFormat": 1}, {"version": "61e28844c025575e531c8f3664440f22115ea2583a4783923bfd6ef28f87f529", "impliedFormat": 1}, {"version": "e59ef9ea5e1e487463143184732fbb95112cd982ae242dd7db2d286ef4bef39b", "impliedFormat": 1}, {"version": "343365d978be77a4524ad9a2106fc3220662d1d796469b2b0f82f85eb0ba7b6b", "impliedFormat": 1}, {"version": "fca30cb8fafe4f77c7866719a2ca27f90dcffc81c13b93fdaed6e272699434ac", "impliedFormat": 1}, {"version": "6811fb4c0770b00b4d6df4f54eaa269121062c0f71cbcf512699f2c3014978aa", "impliedFormat": 1}, {"version": "b616b04178a579c2ea21168dd471a78ae5bc5ab41605ac5f9697113823ab3a3c", "impliedFormat": 1}, {"version": "63be613bb299e2990749717a1cf50f4d02cda870a11313656ec9ad984fb20e9b", "impliedFormat": 1}, {"version": "9b7402110b6b8555b71ec85cdd25926f92181afbe755c3d6cd0fddd3827f06f2", "impliedFormat": 1}, {"version": "2940fd20b182b5bfd3a86790a767432458176b54e8f5fb0bad58d4fe424cdeb9", "impliedFormat": 1}, {"version": "7c6711bafa5f248e029d085bbd1be9ed4f67494bd9b10d1c40516b30aa74f532", "impliedFormat": 1}, {"version": "2170ef43cd965ab105654ab671ec6a00ee3c81062381f91429c5972101801c8b", "impliedFormat": 1}, {"version": "4541492cc9efcf363b995a307641b222addb1aea8fa35e81658235a1c0c71da1", "impliedFormat": 1}, {"version": "ed06803b8d3964f8b09f2ba1200671febf5b9263867c9431e28ee48cb5840ab4", "impliedFormat": 1}, {"version": "a8985331aa754c9f19cf67b3cebdcf5c96848fdde4a6babe44183701fa59277b", "impliedFormat": 1}, {"version": "c877c66437c2499ffa97f7645ab83526f48a8f0d87e0a04cb6e36e8b74c55cbe", "impliedFormat": 1}, {"version": "f2e8b02f43b27e0ab607896348c84f95d6c689d3e284e2e76fb6153774ffc368", "impliedFormat": 1}, {"version": "50c9cd1303b5a1dd4663f17155770f1e68d52ffc27bfff9f7b2b4e9a0eb3582d", "impliedFormat": 1}, {"version": "f48b84784526d4cd62915a022f1afe53a2e38668b417e24c4cd9dc1167f118ae", "impliedFormat": 1}, {"version": "acc70ee6b612bfc01ac779385a25b5e039ee335048ae8b4574be84499e3453c9", "impliedFormat": 1}, {"version": "bd85bfcd414a6ca3c0cf1bbf81fba8b9cc0e8ca5b8f4ac7b45a8346931a371c7", "impliedFormat": 1}, {"version": "bb7e6cc26cc2c0ece0b38c140a81527eb42e13c6910659d0d92d64434ff68891", "impliedFormat": 1}, {"version": "743842516348b3534e57340aa32baeefa3f3a708ccf96667c1a34f8981024579", "impliedFormat": 1}, {"version": "5bc2228e1bcb7d0a8a29f4b83a5a2fe04e8e09c91c79096159827311ae932a5e", "impliedFormat": 1}, {"version": "cac46392659f436fe1df26265350ac6445ba51a8c269d4a6bb1a7036a1fc50b3", "impliedFormat": 1}, {"version": "7cf3a29f25534a590b1193513be6d69505009a6db3f24eaca8a52b16579d8e70", "impliedFormat": 1}, {"version": "371fd33bbbad016817c40c916ef8571c0d68c642aac112ceab714192634b215c", "impliedFormat": 1}, "f834926f7acd96a82e927a03dc2eb9802e827756f1d8e282f94886cc520ebf35", "8f1945dcc0f93baa12bd5b0a3bfd8e8bbe36c7b8a38e02062646c866b184b0c3", "54b38dc34b15ee568027aa4a23d193d388452f4f5ec5c91dd27b1367791f4f76", "722c1a0b97576a82d5d09149e8b60cc4a215ac296e89afb3308fc18836ba919d", {"version": "0d695d941f533497c9f5da6621f40b201d6a4e332f56f950395e8f4293624142", "impliedFormat": 1}, {"version": "6d5149d9545780ffe1fb138bb50dacf1dc586b78bc4e40afa894d494b45066f5", "impliedFormat": 1}, {"version": "2e75aee9534e9802adf4f98b34932e6db2148a1eef173d3ebc0c8d1e9c4da657", "impliedFormat": 1}, {"version": "b56a4898588dc6e9cce0e9e26204289e66a8a7f8ed723f953f14fbf7c25d441c", "impliedFormat": 1}, {"version": "7a5c18177e8f24d9ad1f6979644772c111da38d8a67793f8d94032991a22ffa5", "impliedFormat": 1}, {"version": "599bb02050a247c786e7f1758b559a75b8ee9c67abe663ed1d70df9239e39a95", "impliedFormat": 1}, {"version": "f930d96fec33c06212f8ec6e9e6de2bf0ecf524aa8a30e2f7ec6452142431274", "impliedFormat": 1}, {"version": "bdfc5e4435aeb1e02a1fe8a861c47b6309108c0876cc904996bc48d84fbe954e", "impliedFormat": 1}, {"version": "cdb3c20e12f996b3c169d529ac6f3325239fd8e33421b54c58d418343edfb106", "impliedFormat": 1}, {"version": "44b856bf1d8c66e0d0cac2ce17dd1e91d5c5321eae61dc396ab4c1a2d343cdf2", "impliedFormat": 1}, {"version": "6130c1b16940e2ea191bd8a846f45ed1ef8ca3798b0a081c8910fa75cf7e1d29", "impliedFormat": 1}, {"version": "105042fd4437c2fce03e17475a9a2b3ec785be9c95452462d9f8ff250c89f414", "impliedFormat": 1}, {"version": "c147a315f763b137938040cd619ae097e223990893b3511ccfd7f358880cb816", "impliedFormat": 1}, {"version": "5fde87aa2996e59fc05c419527bf6a62fed579486adde7ece41bb979737ad044", "impliedFormat": 1}, {"version": "0072956e758e6a031fc4f22dd3bccaff69afad84ccdf4e18279ee3b85c916ae4", "impliedFormat": 1}, {"version": "76cee7016ae49c8bcd0523f197c3688967aac2cc8046339325b53cd5af64546a", "impliedFormat": 1}, {"version": "2e5261e2ce02ad9195ceae199eaa491521b2d0de34d51512584da2f8acb73a63", "impliedFormat": 1}, {"version": "aab6ebf6b1896ffeca40252a9d44b7b2e6b0ff32e826a0e6c0c6146e35f653f2", "impliedFormat": 1}, {"version": "4a18da55692f93298cf39eea77b0ec8051e7f6c5477d6422ff73089ed9a2ac2b", "impliedFormat": 1}, {"version": "e39e5834a343c2e44b0d4edee7e2511787aba51391d032f8515a50de29dbcfb3", "impliedFormat": 1}, {"version": "826e10bd4d2305aba739790614ffc4340400b6acba236e9eb0198068aec4dce2", "impliedFormat": 1}, {"version": "93cc64e0018c653d113bf869c1c6f9c0776841c6282ddf4b5e60e73ffd8ea868", "impliedFormat": 1}, {"version": "f797e606ac6d6e14695da466bd742fa5c0ccdc6f553bac293f5ac6417d7b2871", "impliedFormat": 1}, {"version": "4718a7c195115490322b7ee7de0a9d54ae2f26c54670104fe05f9dc7c5dc205b", "impliedFormat": 1}, "212eb5dc5bab905ffbce07041f8fac494aa4d04b5b493cbc7c55ff7505246363", "cbdb13e9fb41f417f1681164d8920ef938ec6288e1507b79f523e7ed3ed1dbf3", "ed5f227226f07e0972ec34c805baa8c96e501e76e361af52d0d29adc0e438292", "153f269a20256747ea026cd591b6c1d8275181d02aabc169840a4d1c39b911e6", "73eec248c443d21c0a6875369f9dfe3fcdbf0dc7e5d2b1c99071693d72f65660", "d90a2505f598366f1680f8f9ba25ffe2849c6cd053744d8ada3b7859627769fb", "66623b9c1d5ae5ffdfd4e8c3b77902403b3579ccf0f18cbcdba5013cf69dc9d0", "61e69d9216ed3b8f811c35293a796122271f050f5225ff8a00c4e5dc2d74170f", {"version": "88e9caa9c5d2ba629240b5913842e7c57c5c0315383b8dc9d436ef2b60f1c391", "impliedFormat": 1}, "b43f5664b09f45a4abcf57145548318f375d3cf08cb1dc6e1f3b66b0c12c2843", {"version": "a7ca8df4f2931bef2aa4118078584d84a0b16539598eaadf7dce9104dfaa381c", "impliedFormat": 1}, {"version": "11443a1dcfaaa404c68d53368b5b818712b95dd19f188cab1669c39bee8b84b3", "impliedFormat": 1}, {"version": "36977c14a7f7bfc8c0426ae4343875689949fb699f3f84ecbe5b300ebf9a2c55", "impliedFormat": 1}, {"version": "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", "impliedFormat": 1}, {"version": "19efad8495a7a6b064483fccd1d2b427403dd84e67819f86d1c6ee3d7abf749c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1eef826bc4a19de22155487984e345a34c9cd511dd1170edc7a447cb8231dd4a", "affectsGlobalScope": true, "impliedFormat": 99}, "424faf9241dd699dda995b367ed36665732da1e6ec1f33b2fd40394488ecac92", "f3c2bfe6972903d23c654f3e26ea8210a33d229fc42adc6e795c81ae52ffb2df", "a59326061fb9ca0353506f192099cd810d31259b28bf19dd3b2af696771e7ace", "5a75c7f24400d7f960fd9d8b41a74358a379a124c9636da9c387db3d81d17b33", "796e7afcc42ec942af30822e9e044ef86550440ed422911f3610874490993ddf", {"version": "d3cfde44f8089768ebb08098c96d01ca260b88bccf238d55eee93f1c620ff5a5", "impliedFormat": 1}, {"version": "293eadad9dead44c6fd1db6de552663c33f215c55a1bfa2802a1bceed88ff0ec", "impliedFormat": 1}, {"version": "54f6ec6ea75acea6eb23635617252d249145edbc7bcd9d53f2d70280d2aef953", "impliedFormat": 1}, {"version": "c25ce98cca43a3bfa885862044be0d59557be4ecd06989b2001a83dcf69620fd", "impliedFormat": 1}, {"version": "8e71e53b02c152a38af6aec45e288cc65bede077b92b9b43b3cb54a37978bb33", "impliedFormat": 1}, {"version": "754a9396b14ca3a4241591afb4edc644b293ccc8a3397f49be4dfd520c08acb3", "impliedFormat": 1}, {"version": "f672c876c1a04a223cf2023b3d91e8a52bb1544c576b81bf64a8fec82be9969c", "impliedFormat": 1}, {"version": "e4b03ddcf8563b1c0aee782a185286ed85a255ce8a30df8453aade2188bbc904", "impliedFormat": 1}, {"version": "de2316e90fc6d379d83002f04ad9698bc1e5285b4d52779778f454dd12ce9f44", "impliedFormat": 1}, {"version": "25b3f581e12ede11e5739f57a86e8668fbc0124f6649506def306cad2c59d262", "impliedFormat": 1}, {"version": "2da997a01a6aa5c5c09de5d28f0f4407b597c5e1aecfd32f1815809c532650a2", "impliedFormat": 1}, {"version": "5d26d2e47e2352def36f89a3e8bf8581da22b7f857e07ef3114cd52cf4813445", "impliedFormat": 1}, {"version": "3db2efd285e7328d8014b54a7fce3f4861ebcdc655df40517092ed0050983617", "impliedFormat": 1}, {"version": "d5d39a24c759df40480a4bfc0daffd364489702fdbcbdfc1711cde34f8739995", "impliedFormat": 1}, "e1b92d26bd73a5ec77591ea503aa4e21334f6ea4bda3592a68c08ae14085ac96", "bd574e48304f9a038a71576756a86cff1b7aeadaf5c622dfe0653a415af7e274", "f2cd506d244bede76047b01924110bd4356d075e55a1685e330d479606178a7a", "767bf20b5c93f61e35e9f101912a7b0fad8081d90d9e0169bdf37151063c1e46", "80799986fa33445b474593eb4162e6b4019f2eb655151403a8f9ca735e4e3472", "7b20f6d80c163435e3b76b39754a201aef857814d46133b604bd8bc606c70fab", "462b0a4efbf355c458c4efa989f38e9c6e383b0618bd3f2d6e7eefb1b47ae9d3", "4ecda0370e17fa7f77f31eb990919f9ca353c4683f16b144fa3b54c9bb9ef080", "fe499db75545ccf4e2804530d408d4c34d1261bfeb1cf0d3f2ece62430ad0a03", "1483d6d1db4f629b966f50f90e1b4d0e98ec647d8d936f3ca4ba72655894f844", "7fa206a5d1995a664d697d99d1b8dfb4ebdbfd01004d3e6a515d268632fdac58", "3e1335b4e263d637bb2f062e5193a69e2bfa0d1a741cad0e4a0f742b3f4f9dcb", "6d3fe5bb10e7071147fbee03a93bfba04683edd4a990bde17d3738b3f327a5b8", "a46a53068ce0567b6f94c779737828399a0e3be0cda548efe8e6ab8f22af6409", "dcca233bf52351d5357b15331a4669d93891808164b127aada2d238f64611cfb", "d02f343f7c40743a198da8f4fdea25a3eb0dc7581bff3c29c092a69da17838e4", "cc5038d5340491b5d29d583e2445378b8f549fb3451c9c4514552cde46a97f5c", "61edf2a0ddae1ce92b7372ea177046fc08eb4376bb9f56bb4b4942f7443bd42d", "1047000888daf16f8291fcd03da39e717a947bbae8524ae4e900377e971f62e9", "aced50c013396c768d50f8232b679727200c282cbc5fcd8ea3e4ba3b97a83581", "20fd5054bec21929920529ac2070d32160e18ae7cd138e91a3aba094785fc91b", "ee21bc139776100ffc99db4e295c71666c78b650aa0112145bdb4cc84bdea840", {"version": "40b262fd5a968b02d9543ed530506a6e7601298b3c8afd0ba3f0ba7a57c5c2df", "impliedFormat": 99}, {"version": "ae046314c0651da4a01e9e48ddf370ce9d22ad21f48962f25a12c1c09de9b01a", "impliedFormat": 99}, {"version": "d0e136d6bf3c38be7af296b7e01912b6e8944a428ba7fd1e415a10acd9e687e8", "impliedFormat": 99}, {"version": "7a685305685db7f9d2195ae629df44ae5888c13371a032ebe629a615a177a45b", "impliedFormat": 99}, {"version": "026b28bf8f8c6f88e4e3aee7dd69f2523b91df8310bf6557d71c853144ec0720", "impliedFormat": 99}, {"version": "4bc5ace72e3fcd7da9d8872af098c4b157ad8bd98b1996c097212884dc8e09cb", "impliedFormat": 99}, {"version": "c3aa1b9d09adac7ac5e49aba8e8fa7114c2c842d46c2c5f51da53ec889787bac", "impliedFormat": 99}, {"version": "7cd8fbd00f9608795145d427ff641d7abc485cd485d833ea1d9a90222ee73778", "impliedFormat": 99}, {"version": "0f4f54801406a0a67455a9ad950bed9f4d2921fd66a91682f83a985086d60082", "impliedFormat": 99}, {"version": "c06802786181dcc58f54b8db8c2c373d93e2ab2c0ada3a5ba8eba9c07d0ef280", "impliedFormat": 99}, {"version": "8c18a2ccca01e6ec6bb951c9a376d12b08112ee5237826caa913d85b4e3cadb5", "impliedFormat": 99}, {"version": "bb4536df3a096e73ea39b1d125e84fe2920c0e22e07dfac2de646c1d9c7f5581", "impliedFormat": 99}, {"version": "8898b3de958b5a5e4e6ffd5f8dc5310dcfe46e668edf35bbe3e70c3d07591950", "impliedFormat": 99}, {"version": "16f041138a88314d0502f54e9a602040fc4de7845a495a031063038f3126add1", "impliedFormat": 99}, {"version": "6e5aa91099e2fe5d1d05f6f3100a90e5a5d9b8aea7b0ea6f4d05a0f192899a64", "impliedFormat": 99}, {"version": "bd85cba544b37cd32e8d02b138c3a2a4075930d01146b3f5e33d713b39dafe77", "impliedFormat": 99}, {"version": "04a7116aece3802e7ee128fed47d31cd18e5660825a62b42a62929f9508b936e", "impliedFormat": 99}, {"version": "20ca05d62223bf6f117925ef8f9b9781e894cb146d30ac491e0763d34e53a5d0", "impliedFormat": 99}, {"version": "4ba733d1a5ff0a0779b714468b13c9089f0d877e6fbd0147fac7c3af54c89fe0", "impliedFormat": 99}, {"version": "697203f3f5a1fea90e40fe660360325090ab36e630dc9422a1909dd4faa2cacc", "impliedFormat": 99}, {"version": "ad1226eba93a65cdccdb1b4f115d67c5469e12705dbe80139c2988d6b296d04d", "impliedFormat": 99}, {"version": "4ea2c94c3a1c87029d10f11c209674d4c6a0c675a97503dc9668d2815ff6ea11", "impliedFormat": 99}, {"version": "6115f4a134fa54f9d81019ad7f6ebacafffad151b6829c2aed4b1dd0a556e09b", "impliedFormat": 99}, {"version": "f425c404598b37f75688175f11b3c61cffdff004cff0c6a36bd5965173ca8fd3", "impliedFormat": 99}, {"version": "94cfe3be66e4a6a1d52eaff0eb03bea21b4cded83428272c28feedfa5f9a152a", "impliedFormat": 99}, {"version": "c2cf5eb33fc641dd321afd12c726ac3e753a81ab1618270ce6cd508f927989c7", "impliedFormat": 99}, {"version": "a7f2f38cd72a96e7678555a1166a4488771b94e5a9c799d1c8943974ada483bd", "impliedFormat": 99}, {"version": "c519327110a82e5eeaad683dc64f36994f19d9893fe69c4ea2b19d41b7e3e45b", "impliedFormat": 99}, {"version": "23af35a045f9117250e060abdb2789bd34519eb5a6308463f299975a205b2d8c", "impliedFormat": 99}, {"version": "9eaaedc489e28c9f7ff513bc094fe82da02cf2c4a3b2b35fe025699fcc08be78", "impliedFormat": 99}, {"version": "73c4f628937d4e4a94d5af1c04bf57008a9d2c5f94a8fe6d9da8d51783069e15", "impliedFormat": 99}, {"version": "1a7bb0d5979c3081b835f51a7a54b50c50500a897792b66b26a4b8583162ce4f", "impliedFormat": 99}, {"version": "4cd02f2d4d7feae05b035dc1c451070f7536601f4f060d0e944959f1036b3b18", "impliedFormat": 99}, {"version": "6fbdecf06e73381e692ae1c2637a93fe2fa21f08e7cfebfac1cd2d50c6c6df6c", "impliedFormat": 99}, {"version": "e437fb52a096addea9cf385b00cadc5fc34b8b8f6a7e63ef02b26cdc495478ab", "impliedFormat": 99}, {"version": "75ad38105b8decc3c60ee068c8d76e3f546b4db1ca55255d0a509f45e4b52990", "impliedFormat": 99}, {"version": "6e16ba58508a87f231264a5e01b0859669229a40d6edea4485ac2032ddf8a7c6", "impliedFormat": 99}, {"version": "88e6b9a05c5b393e71b2d294a59131b0966c47e682f6cc72a954825cb2da6d3d", "impliedFormat": 99}, {"version": "d45218d368df27abcfd0253d4b1287e1b954156f32ff263f31913bad81a80918", "impliedFormat": 99}, {"version": "73ac47e45d90fb23336a6a01512ae421275cb1c818b7a5068ec84f58e94a5999", "impliedFormat": 99}, {"version": "5f071c7cf6447aa28509349c7f83d072579b76779cd8fad1e1a9f957102d3939", "impliedFormat": 99}, {"version": "6e37e9c8d7d0a0ba8da4d560963737e5fa8bfe2d52416be64f4088216c1514f1", "impliedFormat": 99}, {"version": "9c82c8b18a4f45b08629f90cd6744224d48c0a861ff938effd848aac2de13ac2", "impliedFormat": 99}, {"version": "49455da231ef295ce5fdc0baa019df7622f4b9dc136677109cda3bd20824e903", "impliedFormat": 99}, {"version": "2c9282400f9a7aa142d767fa48ec73bd695af4350746875ff7d22a6077bfbf15", "impliedFormat": 99}, {"version": "350ac1e07b84ae0de1ee61a4e472f207de74c7a515cb2d444f8d8ba5d0feccdb", "impliedFormat": 99}, {"version": "834d6a065229b36947660f25080a1a1d3c2e761094a2868899be41c277f5bb1c", "impliedFormat": 99}, {"version": "029abd015c4923b5877423de146fdb31d76eb0fcd0d965ed86d859fe3591c278", "impliedFormat": 99}, {"version": "458853ee5b6a5e269850a89837ea12f449cc9f0084895c17466a82db64bbcbf1", "impliedFormat": 99}, {"version": "bb19ee300ef7ab22c1a730f01f42623622ccb4b31670d0d9ffb3c3a2717b49ea", "impliedFormat": 99}, {"version": "cba8f9adf50c0648489a1188be75e944a36206477c683ca9d2812fd0ed9c2772", "impliedFormat": 99}, {"version": "5e13162a361014916198c714fda78fade55ad25b49bb8c1c995030dbfc993eb8", "impliedFormat": 99}, {"version": "bf6c93f5eb99910c3271ab4d2be95f486e94a764d8b386d3ba609cc28d835785", "impliedFormat": 99}, {"version": "b829e47c3a6436b2fe53bf7320989c70cb8bfe20e7bba40ec347410b8ab33e82", "impliedFormat": 99}, {"version": "f051d854cff7297ddf8f52736514c6dbd623c88999a17f7ca706372d7a9a6418", "impliedFormat": 99}, {"version": "050f93ca2c319cd4a9e342c902abebba29a98de468cbdcab5e8305eb0c2fca1d", "impliedFormat": 99}, {"version": "3dd9ef9e77420319524dec60c3e950601bd8dd7c1b73b827de442aea038b078b", "impliedFormat": 99}, {"version": "1c1eef2edaef6efd126eec5e4795efbcda71395e0e3fec7db59ca3c07815d44e", "impliedFormat": 99}, {"version": "c91b058ab74323c57dda1cbda7eb8cee56272002249a642deebbbd977c4a0baa", "impliedFormat": 99}, {"version": "cb7f489960477f1f432a3389f691dc243ca075e87f20032a2866321dab05bae2", "impliedFormat": 99}, {"version": "ca885b971dc0c8217ef8aca9f3879c3c2d53415c4dfbe457748045160f6e5205", "impliedFormat": 99}, {"version": "3dfb481b2dba86f0f3bdcb7a63152c8d01b1042217eee8a4049a50be8b1a16cb", "impliedFormat": 99}, {"version": "5a399fe9eeb2a056c1cbced05b1efa5037396828caa2843970d5ed8991683698", "impliedFormat": 99}, {"version": "b98d99e9a1c443ddf21b46649701d8a09425ab79e056503c796ba161ea1a7988", "impliedFormat": 99}, {"version": "a327cdd7126575226a8fa7248c0d450621500ea08f6beccec02583f3328dc186", "impliedFormat": 99}, {"version": "7c7a960997d3470573faaaa089e6effd21cd6233d97ba7245974b4adf46597fd", "impliedFormat": 99}, {"version": "2bb814f26a57746ff80ff0dee91e834d00a5f40d60ee908c9c69265944c3a8b5", "impliedFormat": 99}, {"version": "86e035d87d8f9827b055483b7dfdb86ecbb7d2ca74e9dce8adeaf6972756ac03", "impliedFormat": 99}, {"version": "017907864b01ae728f5be6be99ea7632e68b2a35c2d7c9606bde20f85f10f838", "impliedFormat": 99}, {"version": "a86a5d2ab15be86342869797f056d4861fd0b7cfae4cfa270121f18fe8521eab", "impliedFormat": 99}, {"version": "22f98eae982b7f0d26d3dd7849210e033dc1992f594d87c6fe30075eb94b7a24", "impliedFormat": 99}, {"version": "ec47b34311c3c799d1c90a3dcac1651ed23948c064aca4f0617fa253e648ab15", "impliedFormat": 99}, {"version": "761efac4dfd849586e4fe49fc6cda2aba8e708fa8e4eb19ae85373084cba0d51", "impliedFormat": 99}, {"version": "899ed4016a7a722a6224e78139286f1ab7d05f79be50af0a6492b95170e56fab", "impliedFormat": 99}, {"version": "965bfde0433a808a389b80a8e45b717cd2d5a3a0cdf418707cfda3046e33fa5e", "impliedFormat": 99}, {"version": "db9ca5b1d81456e382831691cd851d15b4c603d23889fb9f12b5be96a8b753e1", "impliedFormat": 99}, {"version": "0dbfa4f383f2dcbe48ab6ced11ad27762eb13cbf3a27a95ae7338922afc2f217", "impliedFormat": 99}, {"version": "57410000658f90295210978d18fe2d488daa49287f21d160ba119c8909ff66c5", "impliedFormat": 99}, {"version": "9a9a3212ac108de497464fc14ab2178cfa037eb981a5b0f461e13362fdd3851a", "impliedFormat": 99}, {"version": "b011f71b5d21579da9f868e56acf3887051fc4027cc7cde7317facb232ed3e95", "impliedFormat": 99}, {"version": "7714308befeeb34cbc1d6715bb650d05e2b4e0516db9e58ef4c399e462d222b1", "impliedFormat": 99}, {"version": "3098f0794f8cecb813ede63e9484a44bb75926c37c9983efc85c9994ebc6e9a6", "impliedFormat": 99}, {"version": "eb8a258495db43e8e4641def32bbbee1b73ecdc680407f948543bd9950668293", "impliedFormat": 99}, {"version": "aa7a83f4acf2686925511ecc32d148062c02984068d563c44f00835fee5b164f", "impliedFormat": 99}, {"version": "d4632bbd2d2afbb1b75163dc7cabab5cc218c2fa933cb8f7d5b7089255faa6fd", "impliedFormat": 99}, {"version": "0cf4827f19c749c5befed9585862c6196a4a5b3d889d20e0f5f4bdb6f734dcc7", "impliedFormat": 99}, {"version": "14d3c7499d1759af5c78eec4f26a6f5b85bdd5b0e41ef3f5e6e813f1ae88c06a", "impliedFormat": 99}, {"version": "0082935dc2cb31cd632eaa6bbdec17f1a9142652e38ede025c0ffab00c50bac4", "impliedFormat": 99}, {"version": "0df7497ada3a4f6459420803ecf7e555f1ad1e7bd43c1e17bdafbc34e19d7162", "impliedFormat": 99}, {"version": "5cccc8d1dd17c789bb6baba06a035e98e378a80d133da3071045c9901bee0094", "impliedFormat": 99}, {"version": "400122441745ebf155bf2988479256580bea7fe7fd563343afa7044674860214", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "64da9a17f7cb5d84731607aed8493e4550a3e613cc7b880c87ce82b209d66b96", "impliedFormat": 99}, {"version": "43e0a9209aaeb16a0e495d1190183635ad1b8d7d47db3ed9a2e527eb001e99aa", "impliedFormat": 99}, {"version": "156ac329be3116b9c1f55ae3cdf8e7586717561ac438ee6b193e7c15b2c87b1a", "impliedFormat": 99}, {"version": "6d056661e4b636cc04e36c36b24a4eb692499b21fe0b18cb81f8bb655d7a3930", "impliedFormat": 99}, {"version": "3481e087d798439d58148bb56c2320d555416010a93d5088888f33c1094fce0c", "impliedFormat": 99}, {"version": "7748a99c840cc3e5a56877528289aa9e4522552d2213d3c55a227360130f2a5c", "impliedFormat": 99}, {"version": "1f957c8657bb868e8cb92e46eac8c8b1877a96708e962015a1ed47fd42c697f6", "impliedFormat": 99}, {"version": "217800577a2c9a7232e5a9d1abd1c1836acbb004e7522a5261299aa867713f96", "impliedFormat": 99}, {"version": "60981ae7c2a8926f7855d8068c42e05a3b1959f0bb795a8bb9773c912a9a6f16", "impliedFormat": 99}, {"version": "4a6de5821d23f5e1781c567ab6550e5357b2c2ae3e8813a277062512f73d4a28", "impliedFormat": 99}, {"version": "618b5aa1f8b9791938f8033f1855238774b555f9dd35f0b8a5443cc066721605", "impliedFormat": 99}, {"version": "760064e691b40768713d8d4d55c8516c402670fed62d189a67d9c9b11ca64cb6", "impliedFormat": 99}, {"version": "fe0f823f30f1c4c8f7b21e314ef7c42beb3eec912477ea303f85c7bf92df8d19", "impliedFormat": 99}, {"version": "68617a52d0596e488c88549c000e964c5f6a241e5361095b2c6203586689b1f3", "impliedFormat": 99}, {"version": "8d4a70e05b1f8450f5fb8997e5bfc336dd0baec3f2c8117f6f260d4eb68de0ac", "impliedFormat": 99}, {"version": "8fa060b55694a9427afa2346181d988302de37181cac7df6e29f252b3741164c", "impliedFormat": 99}, {"version": "e61ce3bbfe37669692af8ac289869baa7b9d01b7e260e5cd0294095a4f6c29a2", "impliedFormat": 99}, {"version": "10f60c4f46231065e5a4815651300d69925049b6d654c141eea7bc3410fa5b4d", "impliedFormat": 99}, {"version": "7b91f1ef3b248dbe1bd3ae0f1b86592d87b66c900b58afe025f9981b961df57b", "impliedFormat": 99}, {"version": "8cc3ab398412f20af6fdd1d307176f933f3a4a6b7eeab11388d3a084b811bec8", "impliedFormat": 99}, {"version": "696116447a588ebeff9d158672b83ce1d26b2be7ffb29acee5925b75c1e29ed4", "impliedFormat": 99}, {"version": "8ca97507cc241216ed30a5c73091a6dd4818dc9cf6dbd3bdab039e40f474202e", "impliedFormat": 99}, {"version": "5676038845e4209868d017df816419f7492d62530eb41bccc2de6783f3df2598", "impliedFormat": 99}, {"version": "4d4662f3af929fce5cac9eac0193c3b9e0b7026516049a398463d091ea38c053", "impliedFormat": 99}, {"version": "d7697f915c61a7f7ee03922e9f4e2dd3ef8122a3bcdafc1d7824f2c664b67ad0", "impliedFormat": 99}, {"version": "f4b527c18afc2e6361bd8ed07ede2d49a1ed42e54f04907df15d6e9636ac506f", "impliedFormat": 99}, {"version": "047b42b5db6da573ed865d8a6e1de787af8dd9b74655e726e22cd085546d5c55", "impliedFormat": 99}, {"version": "1e08d5b9f209382acef44f69b8d31457510e9d7d90fa6846d42d656ef1408c99", "impliedFormat": 99}, {"version": "346b52716101745778442850848e17bbd85debfa16f0e0ecc5ebf42b39b0b49c", "impliedFormat": 99}, {"version": "b90f14bca14cdbdd60dc83c451aca97e8df63c8eb8a158a9ed84de4bfb4cad76", "impliedFormat": 99}, {"version": "bcdfa0b735dff249e6cafe7096d17d338c3942704c20b0cacf78c1d78a5a427f", "impliedFormat": 99}, {"version": "daf3cb7fbb067540163df0a3421e791ebde6bd2e699aad4cdb13366871cb7196", "impliedFormat": 99}, {"version": "98ba4768c426848773fb4a39203aac92e6baa545d93510665cdf207454d0811c", "impliedFormat": 99}, {"version": "f65116ea54fd65813a0d9695249ceaa716487c932247e4aede3e2e3ad3d07316", "impliedFormat": 99}, {"version": "99484c7a277c488a16c49ac1affe465e4fbb5e4d57b8c2190092c5d7b4fe6fca", "impliedFormat": 99}, {"version": "459576a2bc7f798ca767ded6a79cc639a26cb797e5b0c417d0f05eb46f595019", "impliedFormat": 99}, {"version": "0f1ea4f6570d745ee2dfa784baa306ae15c35ff7742566ac5ccc1a893af9a1ba", "impliedFormat": 99}, {"version": "06e727ca4d41b4f549f875d7999d940a392058b1b579846441351ff011a63a31", "impliedFormat": 99}, {"version": "d7e8d8a15b4fdd368720cb7a1ad3e740e2f25b9a5ac24c26839921b8d0b7134b", "impliedFormat": 99}, {"version": "d94acd15b4a3517523756dfeabcb7b4fb8ee853bba680d892ccfd3df4c81edc1", "impliedFormat": 99}, {"version": "0f65f9b61383ffcfa1a409da90c35741cd81ece1a2dc6f2ebd094d81599bc5f6", "impliedFormat": 99}, {"version": "9abd03a84d5473e66b038270dbeae266129ab97261d348a5fbd32ec876161a85", "impliedFormat": 99}, {"version": "884f8073c4687a2058be4f15a8f3d8ad613864a4f2d637bf8523fa52b32cf93f", "impliedFormat": 99}, {"version": "6470df3bb3b93da4bc775c68b135b54e8be82866b1446baaffebf50526fc52a0", "impliedFormat": 99}, {"version": "e3ac1db377991a0bea76cfcfd60959f9ba94878cf99d141222c8f11470f540ff", "impliedFormat": 99}, {"version": "b6024c6222886b95cb29ab236155a98f8e5dc41151233781815e81a83debf67b", "impliedFormat": 99}, {"version": "94dab3752006a2cd2726462342f1775ef18ff4986404d016d317fe79a9d0a14c", "impliedFormat": 99}, {"version": "727b3a462015bbed74b520861445761ebaecf94e09d95bbf59dfcf22afaccae9", "impliedFormat": 99}, {"version": "2c0300921d8d04b21353c94a8f50a2b6c902feccd1303b6f136bedbb2cec5ed1", "impliedFormat": 99}, {"version": "d496217c7f38f218fc162e8f3e6ed611343aa65615f730f82c494dee6c892bc0", "impliedFormat": 99}, {"version": "282ed4ab5b5c4759d5c917c51a5b2f03ca1df4072275b6bccb936cf60078e973", "impliedFormat": 99}, {"version": "2c96813e14e7edcd8e846f009b24fb1bd842b90e2dcd85481136e52588de7982", "impliedFormat": 99}, {"version": "aa70da8072bb8b6e8fae35c7d394d543be8e5c946dad666225a3475010fd2bf0", "impliedFormat": 99}, {"version": "d2c35cb9836cae1899ae9e7e114410dc128bcff4a79cc26318db285699e0223a", "impliedFormat": 99}, {"version": "f89fbb50fd3736e09b418a2e66b98ff9a04820259856afe54bc67977e1acd05b", "impliedFormat": 99}, {"version": "4c76aceec7002f299d9a57ec8e6623f3573bea208b1ea51cc5ea03bf140adad4", "impliedFormat": 99}, {"version": "a0f217b01453d43058cea514325ac8bd3ac3a184265314429eec8059c62824b6", "impliedFormat": 99}, {"version": "c73e552e79763809a52f967e48b7a96a0c164c672ef99de1fa8a7e9e02e3b177", "impliedFormat": 99}, {"version": "31a4b6d0c23346d5fb30b52bd3a8f83113fc928ee6474338d5571361943d58ea", "impliedFormat": 99}, {"version": "aecd83ca7059d21a33fb7ed01dfa06a36c545698dbe0017073dba45532a8487d", "impliedFormat": 99}, {"version": "7fb874c17f3c769961d1b07b6bb0ef07b3ca3d49da344726d8b69608997ef190", "impliedFormat": 99}, {"version": "979e969f86456425e505f6054f5d299f848223d70770a5283fa7c405020b47e1", "impliedFormat": 99}, {"version": "7235f928c14f752f6ea3d6d034693c5d46154cce8757a053d9cd6856be2ab344", "impliedFormat": 99}, {"version": "acd7f9268858029bcec5eba752515b9351d4435b21f1956461242c706dcc0cf9", "impliedFormat": 99}, {"version": "53e2856f8644978742fae88b3c7f570ab509dc4d13288b3912a4446993fa3bc7", "impliedFormat": 99}, {"version": "ea2b6112bfd326f1075896bf76c9108dfd08ccbae2482ba31f68ca43f0b59ca5", "impliedFormat": 99}, {"version": "3f9368aa15d0cc227a3af7af3e3df431dadf0f7cd9897fcc54507f7eb68761cc", "impliedFormat": 99}, {"version": "0f2d4be859066fc3ea8d04b583cd0774e1f9dce7f60b9890bcc0a10efb9fac33", "impliedFormat": 99}, {"version": "ac09b9131c553c189311d9e94d3853b7942d0097925304fe043220a893701ce9", "impliedFormat": 99}, {"version": "f1b34ea3d64f73fc79ce1f312589134db27aa78ef9e156a8f14f89f768e800ac", "impliedFormat": 99}, {"version": "873da6c837a1ee62b5f9b286845be06dc887290a75c553bed7f431107d25a3b6", "impliedFormat": 99}, {"version": "b2abee3c001c024d4e552c4a3319bf3fcc94a1f48bb0d21f5d300d9b4920bde9", "impliedFormat": 99}, {"version": "f9740d044306830442cac761b593538117f46c5ea57a8dc6d61f0bee12e971b6", "impliedFormat": 99}, {"version": "7cf786964e26f0e2c3a904f93f6e31609e2636723df8c1ce248d39b55055c89f", "impliedFormat": 99}, {"version": "41c6aff52e4289763ea30f0849b712437aaeb420c8448aeb8047ee2eca4549f4", "impliedFormat": 99}, {"version": "f5db101f7d90f614627bcab5f8d06d9ccd144a1735b475637940c54097786b67", "impliedFormat": 99}, {"version": "8c575a8e1b6032e576577f28d74066f73aefa7a35d741d0015be36956bbc30aa", "impliedFormat": 99}, {"version": "1989cb4fb2174c56b15f8b10d18ecb0c053e7b39f94582581d69767d7bfb9b32", "impliedFormat": 99}, {"version": "7d90add559ac0a060d621c722127b9a5880a6ab4c15d512a91c57a7b14a073ca", "impliedFormat": 99}, {"version": "47921880701610e8d8a5930d0c9ea03ee9c13773e6665f4ffc8378d5f8c8c168", "impliedFormat": 99}, {"version": "41cbf6c58f2f4e1e5ee95a829b3f193f83952385fa303062f648040a314f939b", "impliedFormat": 99}, {"version": "bb11cd0d046d21d4ae4a28fc4b0eb5d9336a728f9bd489807a6a313142903bc1", "impliedFormat": 99}, {"version": "a96d6463ab2a5a4cf31b01946f1b0929dc3f8be9f28c7c43da29a9e6b7649db1", "impliedFormat": 99}, {"version": "ec43d6b21fd1ed5a1afeb779ceba99e80fe010458bb0a67d9ef301426b1929e5", "impliedFormat": 99}, {"version": "105bb5317c5212d56f82fd9730322b87f4ad8aea2927ef7684341afad050f49b", "impliedFormat": 99}, {"version": "79ffce57ab318282b29bceb505812c490957124a3a96c7d280a342488b0859bf", "impliedFormat": 99}, {"version": "06fd0e1204b7daf4164533fff32ab4e2c1723763c98794f51df417c21e0767f3", "impliedFormat": 99}, {"version": "c4b46086b44bb8816d4a995654c00f64b3601eb50a163f2bba4dfe48ae6c6b91", "impliedFormat": 99}, {"version": "32e670209322bd3692e8fc884c63002f6bd565e83f62f1fd23c46729aa335d1b", "impliedFormat": 99}, {"version": "97717d35deb9f6a6127f3abff60c9af080ab0ccba60aa06a5a3486a374747573", "impliedFormat": 99}, {"version": "4d70c89489fdef067b0819f22eec5fd0323a8b488d93075cb7953bbfc636e03e", "impliedFormat": 99}, {"version": "233dc7f3ea55d2375b32c5c19034babec8e1496dc73784f9b091629a5287f2fe", "impliedFormat": 99}, {"version": "e3fbf3f3e99083f8fc21bbde7677c3b1cad0c730fe231599a69911aa66487d01", "impliedFormat": 99}, {"version": "59110c7d72a09bacde4a80f4ba95d9990b352911f0e4ea09bf766804f8d3e44b", "impliedFormat": 99}, {"version": "3d827d1dd689311e57a98e476b3451445d39e573f4855ac265b7ec1747075c4f", "impliedFormat": 99}, {"version": "e0669b0e7c953962035bb39e7fdfd5cc8fc3d9a666a8b167b78417355609be01", "impliedFormat": 99}, {"version": "8495eef8be427c71a2d574e3ead06c537a9a6d437dd669e6786dab3df009f125", "impliedFormat": 99}, {"version": "15741df16deef60b197560d3cfe45e6c1eff69fa7b85a861e3d8aa8a26683b83", "impliedFormat": 99}, {"version": "c1fc3a728bc95e5ae7dbbb3c650247e77bdeccd7c246f76ca917aadc94a8fba7", "impliedFormat": 99}, {"version": "bb77b52bead9b75d7173bec685e5e2136f6c3f226cedae736db63a44f69db679", "impliedFormat": 99}, {"version": "b3f7783d4977af919bdb8db798fe185908083c6f4bd3b07460967c8e093f7312", "impliedFormat": 99}, {"version": "5a6bae49831f960e7f0bc66f49b2c40077b136d9573871f865507fde09580436", "impliedFormat": 99}, {"version": "c9d03e6b230acfabb058a0b0391312dfb0e7001bb5955836333a14c7f9347f3e", "impliedFormat": 99}, {"version": "e6295124f95b686a16233c1031d04cd971f9685e3416631f463bde75a5c86ce7", "impliedFormat": 99}, {"version": "00c38bd1fe89fed8d4e8502db4f896aef7415b097ac061c2d65f2b539b6df6a7", "impliedFormat": 99}, {"version": "94a2d7c15538d8e83415299f17fd00ab88c594b6a0a40be1e26c99febbab45f6", "impliedFormat": 99}, {"version": "20bbd68ac2d2e7cdf9f60816ba9b378e13c07f0fdafccf9ae5833c876c6f51bc", "impliedFormat": 99}, {"version": "df109d2490b693bd75105efaae08738ab84102bfdb2eee2372e9e3f369ec5fc2", "impliedFormat": 99}, {"version": "0fabc5da6eb8454effc526d74f28b0abbe726eab0ed1296aa618b611da7d9462", "impliedFormat": 99}, {"version": "d411ba0bcd6a51485be855a01cb95f79649fa90039b4f235ba8481dc68edae3e", "impliedFormat": 99}, {"version": "b1991f24f264ab5e0d4de1a95b8483830ba659016dfe4b9e58b4076974c1966a", "impliedFormat": 99}, {"version": "b8ba23b2e323342f2710619f6c1abf6731da764092cdca12f09b983ebf236d8a", "impliedFormat": 99}, {"version": "6e688e8aeba98c268b195f80355a8d163d87ac135ad03c708ceda608e6e269b2", "impliedFormat": 99}, {"version": "802a6978c1b38822934ce43a3505e13b555584848c50bc5db9deb2e896c0940e", "impliedFormat": 99}, {"version": "f502c7d829f5774109007ec2262c23efc941dd1ce42acc140f293a7c5ccfd25b", "impliedFormat": 99}, {"version": "af3444bd00030bae3bef81569f8703ecddc2e569cb6b728ec045f0d73d47572b", "impliedFormat": 99}, {"version": "53102281f8a153bb051e0223a8dc51ff9c4cf92da127d91e3f60e74b4e8f41ca", "impliedFormat": 99}, {"version": "e402e111fadcd36fa26ea1ad74f3defd6ef478f6d278a69c547e664b57770392", "impliedFormat": 99}, {"version": "bf8f4b3b372e92a4e4942ce7f872b2b1e1bd1d3f8698af21627db2dee0dda813", "impliedFormat": 99}, {"version": "be36b21097cdd05607c62ce7bf47f30967b9fa6f11b9da86dabdb298e9cd8324", "impliedFormat": 99}, {"version": "d6325d809c8396ecc90202ebfd2427e052a77d98cfd4e308f656346baf84106b", "impliedFormat": 99}, {"version": "dad5c38d723d08fc0134279b90fac87441ee99b71b0d30814b86954e0111d504", "impliedFormat": 99}, {"version": "dd7510a9a4d30db5ac6418ef1d5381202c6b42c550efeb5fb24dd663eac3f6a2", "impliedFormat": 99}, {"version": "cef653b7f2115c8e2a9b6558bf9a083dbcc37ce8fb6bae0e48cde3b92fdaacb2", "impliedFormat": 99}, {"version": "bb544ec93eab70a6c769cd69c0912742da7c2a8bed7d570e79b8af046a9ca556", "impliedFormat": 99}, {"version": "532bd533a1921eedb9b39fa3559594ab783233867021a7a911db00be5d42fe7a", "impliedFormat": 99}, {"version": "ad48586787d5e217f4fcc229e3c3d8de8aa12979fdf1f186134e3684d56577ac", "impliedFormat": 99}, {"version": "229d6bca5145c86846793cb3166c83abb256cfdb5c425f25ada8eee49c993e54", "impliedFormat": 99}, {"version": "292856f47dad178fe1cb3401554428b3b0157369a8fa52792587fd2bd06fcbec", "impliedFormat": 99}, {"version": "c7d9ac6cbda9b080656b859f3a05e1b5efa14f82aa7e0c7821b4ba1e129d6240", "impliedFormat": 99}, {"version": "23f30bf4295e61d128d785ccb811ad49b90d290e63a5f609597ab410e7896d54", "impliedFormat": 99}, {"version": "b8562e5aefa86c069ec1c61dff56ef0492e9fbd731cbcdd4d7fce28a8644e9f6", "impliedFormat": 99}, {"version": "dc6f347fac486f402df8878d94fbd01a3939a3b7c69635ae4b8e0fcf27f9359e", "impliedFormat": 99}, {"version": "dd6c7d6abb025e7494d02fa9f118af4a5ab0217e03ae54dd836f1160cb7a9201", "impliedFormat": 99}, {"version": "440c9aba92c41b63d718656bd3758f8f98619dbe827448e47601faa51e7a42fa", "impliedFormat": 99}, {"version": "d9cf429fa9667112f53e9bb67bb7b32eeb3697f524d01b9781b65247f1733da4", "impliedFormat": 99}, {"version": "6b8a1a0ee3ab56f43f641206b95e53bfa8a53e6af056415bf7bbf58568cefc83", "impliedFormat": 99}, {"version": "701e25008d343bdd67e02c0ccdce4c2ab41d56645bff646b5dc25e4145e77a3a", "impliedFormat": 99}, {"version": "7a891af63bf06f2be51ed3a67fa974a324d7b917f7b1d10f323ed508a6662354", "impliedFormat": 99}, {"version": "efa0e3dff0199f00eaeb36925776e62419538f7263ec77a56d5612ac5abe9ee2", "impliedFormat": 99}, {"version": "ae6114539394eed7b6305a6d788cb6d2fd94e256d7582f5111a1972ee5a1c455", "impliedFormat": 99}, {"version": "ce460a3532421aeaf7db7b48c10d1e6f0cdac6eed27a6424ebd89d0f6f2865fb", "impliedFormat": 99}, {"version": "3563a343e025cb849b94da85e8455dd89064dee213bc97bbed559f83d74c98de", "impliedFormat": 99}, {"version": "0c5b2200afef6faf0a929b94b3e06b31c64d447ca755d0770dc4ce466fde2895", "impliedFormat": 99}, {"version": "e67fbc9a974d14cab74cb47b4bed04205886bf534c7e2f17ecb8f7789d297b1c", "impliedFormat": 99}, {"version": "82d76af0a89cd5eb4338771a2a5b27f3cbc689b22be0b840de75be4cfc61f864", "impliedFormat": 99}, {"version": "24e856aec3b5c4228ffed866dcd8e7e692aa86eccaecc4fa8205fadd9737d1af", "impliedFormat": 99}, {"version": "fe395a24df9ffd344cb825575d4b35c1cf69275208c0f99517c715bd7d08ff79", "impliedFormat": 99}, {"version": "39e8edcbd5ac35c6cfdf2b1a794a9693a461a54efb2a475ab7fc08ab13504e26", "impliedFormat": 99}, {"version": "12012b6c28d09a6f1d86b2a30213a92a9e92ad9ee573f94c92a8b237b6422bb7", "impliedFormat": 99}, {"version": "8ee28204ddb2be7d6dfb68891493f654cbf10f5e1667bd33bd62920d9eb9e164", "impliedFormat": 99}, {"version": "b09669391dd3312b8a52242af7823a3c44b50c7dcdc216db8da88b679af46574", "impliedFormat": 99}, {"version": "b71e7f69e72d51d44ad171e6e93aedc2c33c339dab5fa2656e7b1ee5ba19b2ad", "impliedFormat": 99}, {"version": "763ee96bd4c739b679a8301b479458ea4fd8166892b2292efe237f2f023f44ca", "impliedFormat": 99}, {"version": "9c61e1d1777ef5ec76a62eb9c66ebc0c1ee5bf1d1037767208693cc3fe61bf9a", "impliedFormat": 99}, {"version": "a715a2786c285a9e27ea2bbaa2ed249d3017e7139782f5ebb8eeedb777b26926", "impliedFormat": 99}, {"version": "2dffb65044b6a28dcba73284ac6c274985b03a6ce4a3b33967d783df18f8b48c", "impliedFormat": 1}, {"version": "f7e187abe606adf3c1e319e080d4301ba98cb9927fd851eded5bcac226b35fd1", "impliedFormat": 1}, {"version": "335084b62e38b8882a84580945a03f5c887255ac9ba999af5df8b50275f3d94f", "impliedFormat": 1}, {"version": "5d874fb879ab8601c02549817dceb2d0a30729cb7e161625dd6f819bbff1ec0b", "impliedFormat": 1}, {"version": "ace68d700c2960e2d013598730888cde6d8825c54065c9f5077aaf3b2e55e3ad", "impliedFormat": 1}, {"version": "247389ec5593d19a2784587be69ea6349e784578070db0b30ba717bec269db38", "impliedFormat": 1}, {"version": "4d7d964609a07368d076ce943b07106c5ebee8138c307d3273ba1cf3a0c3c751", "impliedFormat": 99}, {"version": "0e48c1354203ba2ca366b62a0f22fec9e10c251d9d6420c6d435da1d079e6126", "impliedFormat": 99}, {"version": "0662a451f0584bb3026340c3661c3a89774182976cd373eca502a1d3b5c7b580", "impliedFormat": 99}, {"version": "c02203ae7f03fd2dd9c0da1a08a886734c54aae25fdf8543b1125589f20f0b52", "impliedFormat": 99}, {"version": "409d9b2dffd896e5589be900b59d81149fd48dd811a6fca9311407e03b331e80", "impliedFormat": 1}, {"version": "87c5f1f8ab2e5b52d333f41b66e50f35cb838fa12d839d58478a18e795d401a9", "impliedFormat": 1}, {"version": "21bc4db82aff687d0a4e58858d51ff544677cbc3b6789934bbd4c9abe7bd04aa", "impliedFormat": 1}, {"version": "1dd4deeb0e37d39f07354a91c65e3b040ff408960e1ceed31446343419f9a07b", "impliedFormat": 1}, {"version": "3456acb6ff0d0a202eec1307f2e8b2d1cbba68dace120c47b7e38d7343da19f2", "impliedFormat": 1}, {"version": "7a429fa77d22d12f8febc7ebbb00fa45c75c60b47ce840f92f03b05e9d16648d", "impliedFormat": 1}, {"version": "8df9c6daab36789fcc880e7cdddc453aa72d7d40d0a765f82e97d5a7f66af204", "impliedFormat": 1}, {"version": "020bf445147e2d24f1bcd04212d11f4180efa08b5be86fdefe62bd2023f270b8", "impliedFormat": 1}, {"version": "5483233566b27fecdef8a3f40420d60db822ffbdb0cf20073ac8fd0157fd2290", "impliedFormat": 1}, {"version": "b42bc4e718dbeba955b71adc452e5023b8dda17aa57bb9050ec8c542a8e7e626", "impliedFormat": 99}, {"version": "2091e884437c2fac7ef5b4c37a55a1d0291f3d9e774ca484054adf9088a49788", "impliedFormat": 1}, {"version": "c2762b064c3f241efdcbfce2a3fb4fe926b9c705cbea1da8f2ee92a90bc44e27", "impliedFormat": 1}, {"version": "6b33b56ce86bed582039802da1de9ff7f9c60946b710fb5a7a00ee8a089dc1a2", "impliedFormat": 1}, {"version": "b4fbfaa34aacd768965b0135a0c4e7dbaa055a8a4d6ffe7bedf1786d3dc614de", "impliedFormat": 1}, {"version": "be3daf180476b92514b9003e9bd1583a2a71ad80c9342f627ca325b863ca55d4", "impliedFormat": 1}, {"version": "8ab9b0dd5ad04b64911bbf9ae853690d047c1e12651940bd08da5b6c8fae8b04", "impliedFormat": 1}, {"version": "6fcb9ff90e597db84de7e94537a661dca09dc3c384e1414496d76d31f91232a3", "impliedFormat": 1}, {"version": "ad68aac2dffb24c0330e5bcfe57aa0f2e829650c8dfe63d7329d58af7277990e", "impliedFormat": 1}, {"version": "df0627eabd39ed947e03aedef8c677eb9ad91b733f8d6c7cdc48fc012a41ed8a", "impliedFormat": 1}, {"version": "2164ae0de9e076bf50b097cc192d6600a7b3eb07a0e1cd3281f7f5d19d4f4638", "impliedFormat": 1}, {"version": "e9759993d816a63028cb9a42120223941b0835c6b27aa8af69cc650a18c1bf91", "impliedFormat": 1}, {"version": "f964f0ebc9cad8ce4873f24e82241b8eb609d304cbc1662a739443b24ef11c9e", "impliedFormat": 1}, {"version": "f0f65a61b70d5ddb3d7f07a6e3f9d73a5da863172c815a3559c8bbb5c18bcc23", "impliedFormat": 1}, {"version": "639c15ef2ce567ec3a62d9c51a43b65f1a8eabfdc88dc5ed57f1f23cc213189f", "impliedFormat": 1}, {"version": "b6d80e669780b6591b159637ad0e8cf678cf6929fa0643be7d16aff7ca499bd6", "impliedFormat": 1}, {"version": "d4e6925460a27b532a99e38bb0e579ed74b5f6422d70a210aeca9da358526f89", "impliedFormat": 1}, {"version": "8a9d6ffa232e5599cebac02c653c01afa9480875139bab7d70654d1a557c7582", "impliedFormat": 99}, {"version": "9ee450d9e0fbae0c5d862b03ae90d3690b725b4bd084c5daec5206aefa27c3f1", "impliedFormat": 99}, {"version": "e2e459aac2973963ed39ec89eaba3f31ede317a089085bf551cc3a3e8d205bb4", "impliedFormat": 99}, {"version": "bd3a31455afb2f7b1e291394d42434383b6078c848a9a3da80c46b3fa1da17d5", "impliedFormat": 99}, {"version": "51053ea0f7669f2fe8fc894dcea5f28a811b4fefdbaa12c7a33ed6b39f23190b", "impliedFormat": 99}, {"version": "5f1caf6596b088bd67d5c166a1b6b3cd487c95e795d41b928898553daf90db8d", "impliedFormat": 99}, {"version": "eaeaddb037a447787e3ee09f7141d694231f2ac7378939f1a4f8b450e2f8f21f", "impliedFormat": 99}, {"version": "7c76a8f04c519d13690b57d28a1efe81541d00f090a9e35dca43cde055fed31b", "impliedFormat": 99}, {"version": "17c976add56f90dd5aad81236898bad57901d6bdac0bd16f3941514d42c6fcc7", "impliedFormat": 99}, {"version": "0d793c82f81d7c076f8f137fa0d3e7e9b6a705b9f12e39a35c715097c55520c9", "impliedFormat": 99}, {"version": "7c6fd782f657caea1bfc97a0ad6485b3ad6e46037505d18f21b4839483a66a1c", "impliedFormat": 99}, {"version": "4281390dad9412423b5cc3afccf677278d262a8952991e1dfaa032055c6b13fb", "impliedFormat": 99}, {"version": "02565e437972f3c420157d88ae89e8f3e033c2962e010483321c54792bce620a", "impliedFormat": 99}, {"version": "1623082417056ce69446be4cf7d83f812640f9e9c5f1be99d6bc0fad0df081ab", "impliedFormat": 99}, {"version": "0c1f67774332e01286cdd5e57386028dd3255576c8676723c10bd002948c1077", "impliedFormat": 99}, {"version": "232c6c58a21eb801d382fb79af792c0ec4b2226a4c9e4cf64a52246538488468", "impliedFormat": 99}, {"version": "196ce15505ddb7df64fa2b9525ec99ec348d66b021e76130220a9ac37840a04a", "impliedFormat": 99}, {"version": "899a2d983c33f9c00808bf53720d3d74a4c04a06305049c5da8c9e694c0c0c74", "impliedFormat": 99}, {"version": "942719a6fafe1205a3c07cecc1ea0c5d888ff5701a7fbbd75d2917070b2b7114", "impliedFormat": 99}, {"version": "7ad9c5c8ca6f45cf8cc029f1e789177360ef8a1ac2d2e05e3157f943e70f1fa3", "impliedFormat": 99}, {"version": "e9204156d21f5dd62fa4676de6299768b8826bb02708a6e96043989288c782c7", "impliedFormat": 99}, {"version": "b892c877d4b18faad42fd174f057154101518281f961a402281b21225bf86e2f", "impliedFormat": 99}, {"version": "755e75ad8e93039274b454954c1c9bb74a58ac9cef9ff37f18c6f1e866842e2e", "impliedFormat": 99}, {"version": "53e7a7fa0388634e99cf1e1be2c9760c7c656c0358c520f7ec4302bd1c5e2c65", "impliedFormat": 99}, {"version": "f81b440b0a50aa0e34f33160e2b8346127dbf01380631f4fc20e1d37f407bef9", "impliedFormat": 99}, {"version": "0791871b50f78d061f72d2a285c9bfac78dba0e08f0445373ad10850c26a6401", "impliedFormat": 99}, {"version": "d45d1d173b8db71a469df3c97a680ed979d91df737aa4462964d1770d3f5da1b", "impliedFormat": 99}, {"version": "e616ad1ce297bf53c4606ffdd162a38b30648a5ab8c54c469451288c1537f92e", "impliedFormat": 99}, {"version": "8b456d248bb6bc211daf1aae5dcb14194084df458872680161596600f29acb8d", "impliedFormat": 99}, {"version": "1a0baa8f0e35f7006707a9515fe9a633773d01216c3753cea81cf5c1f9549cbd", "impliedFormat": 99}, {"version": "7fa79c7135ff5a0214597bf99b21d695f434e403d2932a3acad582b6cd3fffef", "impliedFormat": 99}, {"version": "fb6f6c173c151260d7a007e36aa39256dd0f5a429e0223ec1c4af5b67cc50633", "impliedFormat": 99}, {"version": "eebfa1b87f6a8f272ff6e9e7c6c0f5922482c04420cde435ec8962bc6b959406", "impliedFormat": 99}, {"version": "ab16001e8a01821a0156cf6257951282b20a627ee812a64f95af03f039560420", "impliedFormat": 99}, {"version": "f77b14c72bd27c8eea6fffc7212846b35d80d0db90422e48cd8400aafb019699", "impliedFormat": 99}, {"version": "53c00919cc1a2ce6301b2a10422694ab6f9b70a46444ba415e26c6f1c3767b33", "impliedFormat": 99}, {"version": "5a11ae96bfae3fb5a044f0f39e8a042015fb9a2d0b9addc0a00f50bd8c2cc697", "impliedFormat": 99}, {"version": "59259f74c18b507edb829e52dd326842368eaef51255685b789385cd3468938f", "impliedFormat": 99}, {"version": "30015e41e877d8349b41c381e38c9f28244990d3185e245db72f78dfba3bbb41", "impliedFormat": 99}, {"version": "52e70acadb4a0f20b191a3582a6b0c16dd7e47489703baf2e7437063f6b4295a", "impliedFormat": 99}, {"version": "15b7ac867a17a97c9ce9c763b4ccf4d56f813f48ea8730f19d7e9b59b0ed6402", "impliedFormat": 99}, {"version": "fb4a64655583aafcb7754f174d396b9895c4198242671b60116eecca387f058d", "impliedFormat": 99}, {"version": "23dae33db692c3d1e399d5f19a127ae79324fee2047564f02c372e02dbca272d", "impliedFormat": 99}, {"version": "4c8da58ebee817a2bac64f2e45fc629dc1c53454525477340d379b79319fff29", "impliedFormat": 99}, {"version": "50e6a35405aea9033f9fded180627f04acf95f62b5a17abc12c7401e487f643f", "impliedFormat": 99}, {"version": "c1a3ca43ec723364c687d352502bec1b4ffece71fc109fbbbb7d5fca0bef48f1", "impliedFormat": 99}, {"version": "e88f169d46b117f67f428eca17e09b9e3832d934b265c16ac723c9bf7d580378", "impliedFormat": 99}, {"version": "c138a966cc2e5e48f6f3a1def9736043bb94a25e2a25e4b14aed43bff6926734", "impliedFormat": 99}, {"version": "b9f9097d9563c78f18b8fb3aa0639a5508f9983d9a1b8ce790cbabcb2067374b", "impliedFormat": 99}, {"version": "925ad2351a435a3d88e1493065726bdaf03016b9e36fe1660278d3280a146daf", "impliedFormat": 99}, {"version": "100e076338a86bc8990cbe20eb7771f594b60ecc3bfc28b87eb9f4ab5148c116", "impliedFormat": 99}, {"version": "d2edbba429d4952d3cf5962dbfbe754aa9f7abcfcbdda800191f37e07ec3181b", "impliedFormat": 99}, {"version": "8107fdc5308223459d7558b0a9fa9582fa2c662bd68d498c43dd9ab764856bc7", "impliedFormat": 99}, {"version": "a35a8a48ad5d4aad45a79f6743f2308bdaea287c857c06402c98f9c3522a7420", "impliedFormat": 99}, {"version": "e4aa88040fd946f04fe412197e1004fb760968ac3bd90d1a20bfb8b048f80ce0", "impliedFormat": 99}, {"version": "f16df903c7a06f3edd65f6292fef3698d31445eaca70f11020201f8295c069b5", "impliedFormat": 99}, {"version": "d889a5532ecd42d61637e65fac81ea545289b5366f33be030e3505a5056ee48a", "impliedFormat": 99}, {"version": "6d8762dd63ee9f93277e47bf727276d6b8bdd1f44eb149cfa55923d65b9e36bc", "impliedFormat": 99}, {"version": "bf7eebda1ab67091ac899798c1f0b002b46f3c52e20cccb1e7f345121fc7c6c2", "impliedFormat": 99}, {"version": "9a3983d073297027d04edec69b54287c1fbbd13bbe767576fdab4ce379edc1df", "impliedFormat": 99}, {"version": "8f42567aa98c36a58b8efb414a62c6ad458510a9de1217eee363fbf96dfd0222", "impliedFormat": 99}, {"version": "8593dde7e7ffe705b00abf961c875baef32261d5a08102bc3890034ae381c135", "impliedFormat": 99}, {"version": "53cf4e012067ce875983083131c028e5900ce481bc3d0f51128225681e59341b", "impliedFormat": 99}, {"version": "6090fc47646aa054bb73eb0c660809dc73fb5b8447a8d59e6c1053d994bf006e", "impliedFormat": 99}, {"version": "b6a9bf548a5f0fe46a6d6e81e695d367f5d02ce1674c3bc61fe0c987f7b2944f", "impliedFormat": 99}, {"version": "d77fa89fff74a40f5182369cc667c9dcc370af7a86874f00d4486f15bdf2a282", "impliedFormat": 99}, {"version": "0c10513a95961a9447a1919ba22a09297b1194908a465be72e3b86ab6c2094cc", "impliedFormat": 99}, {"version": "acfce7df88ff405d37dc0166dca87298df88d91561113724fdcb7ad5e114a6ba", "impliedFormat": 99}, {"version": "2fb0e1fc9762f55d9dbd2d61bbc990b90212e3891a0a5ce51129ed45e83f33ee", "impliedFormat": 99}, {"version": "7be15512c38fdbed827641166c788b276bcfa67eda3a752469863dbc7de09634", "impliedFormat": 99}, {"version": "cbba36c244682bbfaa3e078e1fb9a696227d227d1d6fc0c9b90f0a381a91f435", "impliedFormat": 99}, {"version": "ec893d1310e425750d4d36eb09185d6e63d37a8860309158244ea84adb3a41b8", "impliedFormat": 99}, {"version": "0d350b4b9b4fea30b1dbac257c0fc6ff01e53c56563f9f4691458d88de5e6f71", "impliedFormat": 99}, {"version": "4642959656940773e3a15db30ed35e262d13d16864c79ded8f46fb2a94ed4c72", "impliedFormat": 99}, {"version": "a2341c64daa3762ce6aefdefc92e4e0e9bf5b39458be47d732979fb64021fb4f", "impliedFormat": 99}, {"version": "5640ea5f7dfd6871ab4684a4e731d48a54102fd42ea7de143626496e57071704", "impliedFormat": 99}, {"version": "7f6170c966bbd9c55fd3e6bcc324b35f5ca27d70e509972f4b6b1c62b96c08ff", "impliedFormat": 99}, {"version": "62cb7efe6e2beecb46e0530858383f27e59d302eb0a6161f66e4d6a98ae30ff5", "impliedFormat": 99}, {"version": "a67ae9840f867db93aca8ec9300c0c927116d2543ecc0d5af8b7ab706cdda5ad", "impliedFormat": 99}, {"version": "658b8dbb0eef3dcfbcaf37e90b69b1686ba45716d3b9fb6e14bb6f6f9ef52154", "impliedFormat": 99}, {"version": "1e62ffb0b2bc05b7b04a354710596e60ac005cab6e12face413855c409239e9b", "impliedFormat": 99}, {"version": "c92349bad69a4e56ac867121cda04887a79789adb418b4ee78948a477f0c4586", "impliedFormat": 99}, {"version": "d49420a87cc4608acbd4e8ce774920f593891047d91c6b153f0da3df3349b9be", "impliedFormat": 99}, {"version": "44376b040b0712ffe875ad014bb8c9f84d7648487cdf36e8bbe8f4888f860a03", "impliedFormat": 99}, {"version": "4c704b137991192a3d2f9e23a3ded54bdb44f53ea5884c611c48637064e8c6cb", "impliedFormat": 99}, {"version": "917af11888db0ac87046f9b31f8ccb081d2da9ba650d6aab9636a018f2d86259", "impliedFormat": 99}, {"version": "d6c196e038cb164428f2f92feb0191de8a95d60aad8eb65bc703d3499d7ff888", "impliedFormat": 99}, {"version": "b27723af585d0cf2e5f6a253b2989d084ba5c7ffe24130ab33d3c01f60f8f7c8", "impliedFormat": 99}, {"version": "37f271a1de9b674667cffbd616832f4127c0a364d502b2b33e3e9c6b16fde1b8", "impliedFormat": 99}, {"version": "0c796f53945fee54a07b295dbd1f1303c7a73cdd2c629e66fbfa5e29df16de9e", "impliedFormat": 99}, {"version": "2b3045052668b317d06947a6ab1187755b2ad4885dd6640b6a8fe174e139ec5e", "impliedFormat": 99}, {"version": "44ee21f3f866b5517804aadc860c89da792cca2d3ad7431d5742c147be7deb82", "impliedFormat": 99}, {"version": "57bc6a334f498834fe779ea68e92a06c569e3b6757b608a092119589c34b7242", "impliedFormat": 99}, {"version": "ccc8793b3493c8cf50af8e181da08e4e7ff327535724dfde8bf56249a385954f", "impliedFormat": 99}, {"version": "c48b220c9a10db0df2d791b93d332575bb57033797da241c124f87c2171159ea", "impliedFormat": 99}, {"version": "d1509856fe7e38720ef11b8e449d4ada04879e5ecfd2d09b41c2e4a07b3d8dd1", "impliedFormat": 99}, {"version": "3883734e7cba8ceb7a314ca68c97ac3f69031a2fde7830e5b2e2339f10520497", "impliedFormat": 99}, {"version": "54396051cf9f736287426d1f3c9ec0f8afad30a4d3e607f65ffd6205ec90bdce", "impliedFormat": 99}, {"version": "4c5ed0d7c2b8dc59f2bcc2141a9479bc1ae8309d271145329b8074337507575d", "impliedFormat": 99}, {"version": "2bdc0310704fe6b970799ee5214540c2d2ff57e029b4775db3687fbe9325a1e4", "impliedFormat": 99}, {"version": "d9c92e20ad3c537e99a035c20021a79c66670da1c4946e1b66468ca0159e7afd", "impliedFormat": 99}, {"version": "b62f1c33a042e7eb17ac850e53eb9ee1e7a7adbfa4aacf0d54ea9c692b64fc07", "impliedFormat": 99}, {"version": "c5f8b0b4351f0883983eb2a2aaa98556cc56ed30547f447ea705dbfbe751c979", "impliedFormat": 99}, {"version": "6a643b9e7a1a477674578ba8e7eed20b106adbef86dabe0faf7c2ba73dc5b263", "impliedFormat": 99}, {"version": "6e434425d09e4a222f64090febcbbfbb8fb19b39cec68a36263a8e3231dab7ad", "impliedFormat": 99}, {"version": "58afdddfd9bc4529afe96203e2001dcc150d6f46603b2930e14843a2adc0bef3", "impliedFormat": 99}, {"version": "faa121086350e966ec3c19a86b64748221146b47b946745c6b6402d7ecf449d4", "impliedFormat": 99}, {"version": "a9286d1583b12fd76bf08bcd1d8dad0c5e3c0618367fe3fe49326386fee528bd", "impliedFormat": 99}, {"version": "141c5152b14aa1044b7411b83a6a9707f63e24298bfc566561a22d61b02177a4", "impliedFormat": 99}, {"version": "dce464247d9d69227307f085606844dc1a6badc1e10d6f8e06f3a72d471e7766", "impliedFormat": 99}, {"version": "26333aa1e58f4c7c6acb6cdb1490ba000c857f7e8a21608019ca9323ad97365e", "impliedFormat": 99}, {"version": "b36269da8b9c370075ad842a17f7d284bae04bc07d743aa25cc396d2bbd922cd", "impliedFormat": 99}, {"version": "1e5afd6a1d7f160c2da8ed1d298efcd5086b5a1bdb10e6d56f3ed9d70840aa5d", "impliedFormat": 99}, {"version": "2e7c3024fa224f85f7c7044eded4dba89bf39c6189c20224fa41207462831e06", "impliedFormat": 99}, {"version": "4ca05a8dfe3b861cf6dc4e763519778fc98b40655e71ddee5e8546390cf42b21", "impliedFormat": 99}, {"version": "f96c214198c797da18198b7c660627faf40303ba4d1ac291ac431046ec018853", "impliedFormat": 99}, {"version": "fa20380686e1f6c7429e3194dea61e9d68b7af55fa5fc6da5f1da8fc2b885c3d", "impliedFormat": 99}, {"version": "d3a480946bced3c94e6b8ab3617330e59bf35c3273a96448d6e81ba354f6c20e", "impliedFormat": 99}, {"version": "ff72b0d58aa1f69f3c7fa6e5a806aa588b5024d8bd81cb8314b6df32759cafdd", "impliedFormat": 99}, {"version": "feccbe0137990c333898ac789870caf62bddf7b7f825cca3f5aac4388d867695", "impliedFormat": 99}, {"version": "5d0b0e10dd5f4857dcf4703a4c86d92fe3e1d82a68ffc6739d777fc2ff6d6902", "impliedFormat": 99}, {"version": "d002e1dad5ff22c6d7b9b4e8b09302b99fe6089f907e4e00310b1eea88d24a01", "impliedFormat": 99}, {"version": "0497b91aa0292f7cafe54202e69cb467242426a414623aac0febc931c92b10f2", "impliedFormat": 99}, {"version": "faf1f29f98e2a8db3737827234c5de88d2bf1546471c05b136578190ed647eb9", "impliedFormat": 99}, {"version": "80634ab7f8f65c7b4663e807f8d961c683eaea3b0e58818524c847abb657b795", "impliedFormat": 99}, {"version": "85e852e090c97b25243fb6c986cad3d2b48d0bb83cd1c369f6ff1cf9743ab490", "impliedFormat": 99}, {"version": "12e856f6193309e09fbab3ce89f70e622c19b52cbeaad07b14d47ef19063e4dc", "impliedFormat": 99}, {"version": "d3f4fda002f6200565ef1a5f6bcad4e28e150c209e95716e101d6c689ae11503", "impliedFormat": 99}, {"version": "497a791143290119136bfcde6cd402e3b7d211df944188d1a4a511b8df5a9b13", "impliedFormat": 99}, {"version": "1cb9dab41d415a2a401d52c6bede4ad5aa14a732b2914c01c16cc8b0fc69cf88", "impliedFormat": 99}, {"version": "617108f6e6514fbfa7bf226cf99c33c8872a28517f5b7e855c657d4132afeb3d", "impliedFormat": 99}, {"version": "194823a242a97327f6ac0af92f3d37fc078d4773149724fbb5176093eb7b0617", "impliedFormat": 99}, {"version": "085f9e9b8f27c4833a6cf9228b1ae26d383bf7eb4e0677b5321029564336deff", "impliedFormat": 99}, {"version": "34b81ae7140be9b70a7dfded8acebc06d62c5508617b196739e578595949724d", "impliedFormat": 99}, {"version": "c7631702b00fbbac3682deeeaeaac4bfc0694bec74dda8db4afae1098310e18c", "impliedFormat": 99}, {"version": "b0c04f92ff4c9da466ba563170892afe043ecd0f088deb3d3dc482a747d75bf0", "impliedFormat": 99}, {"version": "c4d6664fa99f28b210a65e5feccc41723bf77d89e5f00afdbdaf25726a9ea4c3", "impliedFormat": 99}, {"version": "f4940ce6889056747592fc93a331d7e33db8889d48e401397cfa15fa27ac4000", "impliedFormat": 99}, {"version": "2e3ae7d41b13b4ebfdf76eb20d4282b72b4eafb9b75b0f850177d03e92f59d7b", "impliedFormat": 99}, {"version": "e37392287850bebf777be5e4b573ef447b3437bf46f85969f9d9b4b37b7a8629", "impliedFormat": 99}, {"version": "68771841743fe93f5732c94a93447cfc2ebce7de956330fcb704e82725f218be", "impliedFormat": 99}, {"version": "6e58d2b1619cb5b2312a57fb1a0071f693ac0c7547f12d4e38c2b49629f71b9f", "impliedFormat": 99}, {"version": "8363077b4b4520e9cfff74d0ae1d034b84f7429d35265e9e77daedeb428297f2", "impliedFormat": 99}, {"version": "541cfa49f8c37ea962d96f4e591487524af58bfbf4faf45e904a4e1b25b7a7aa", "impliedFormat": 99}, {"version": "ebb09c62607092b0aa7dbc658b186ee8cc39621de7f3ccf8acbd829f2418d976", "impliedFormat": 99}, {"version": "f797dc6c71867b6da17755cfdbd06ef5ed5062e1b6fd354a07929a56546d4f4d", "impliedFormat": 99}, {"version": "686bd9db685be2e1f812cf82d476c7702986ad177374dad64337635af24a0b9f", "impliedFormat": 99}, {"version": "cc8520ff04dae6933f1eec93629b76197fb4a40a3a00da87c44e709cfa4af1ba", "impliedFormat": 99}, {"version": "55880163bc61bc2478772370acce81a947301156cdce0d8459015f0e5a3f3f9c", "impliedFormat": 99}, {"version": "d7591af9e3eee9e3406129e0dacb69eb2ac02f8d7ceb62767a6489cb280ca997", "impliedFormat": 99}, {"version": "522356a026eb12397c71931ff85ce86065980138e2c8bce3fefc05559153eb80", "impliedFormat": 99}, {"version": "1b998abad2ae5be415392d268ba04d9331e1b63d4e19fa97f97fe71ba6751665", "impliedFormat": 99}, {"version": "81af071877c96ddb63dcf4827ecdd2da83ee458377d3a0cb18e404df4b5f6aa0", "impliedFormat": 99}, {"version": "d087a17b172f43ff030d5a3ede4624c750b7ca59289e8af36bc49adb27c187af", "impliedFormat": 99}, {"version": "e1cc224d0c75c8166ae984f68bfcdcd5d0e9c203fe7b8899c197e6012089694c", "impliedFormat": 99}, {"version": "1025296be4b9c0cbc74466aab29dcd813eb78b57c4bef49a336a1b862d24cab0", "impliedFormat": 99}, {"version": "18c8cf7b6d86f7250a7b723a066f3e3bf44fd39d2cb135eaffe2746e9e29cc01", "impliedFormat": 99}, {"version": "c77cd0bddb5bec3652ff2e5dd412854a6c57eaa5b65cbf0b6a47aae37341eca9", "impliedFormat": 99}, {"version": "e4a2ca50c6ded65a6829639f098560c60f5a11bc27f6d6d22c548fe3ec80894d", "impliedFormat": 99}, {"version": "e989badc045124ca9516f28f49f670b8aeee1fb2150f6aefd87bb9df3175b052", "impliedFormat": 99}, {"version": "d274cf19b989b9deff1304e4e874bc742816fca7aae3998c7feec0a1224079c7", "impliedFormat": 99}, {"version": "0aefb67a9c212a540e2dedb089c4bbe274d32e5a179864d11c4eea7dc3644666", "impliedFormat": 99}, {"version": "2767af8f266375ebd57c74932f35ce7231e16179d3066e87bcb67da9b2365245", "impliedFormat": 99}, {"version": "34a1c0d17046ac6b326ed8fbe6e5a0b94aeef9e50119e78461b3f0e0c3a4618a", "impliedFormat": 99}, {"version": "6fd58a158e4a9c661d506c053e10c7321edaa42b930e73b7a6d34eb81f2a71e8", "impliedFormat": 99}, {"version": "60e18895fc4bff9e2f6fb58b74fcf83191386553e8ab0acc54660d65564e996c", "impliedFormat": 99}, {"version": "41d624e8c6522001554fdddef30fed443b4c250ec8ddbb553bbe89e7f7daf2f4", "impliedFormat": 99}, {"version": "b3034ec5a961ab98a41bc59c781bf950bb710834f1f99bf4b07bfbba77e2f04a", "impliedFormat": 99}, {"version": "2115776fcd8001f094066e24d80b7473bbc2443a5488684f9f3a94a3842daadb", "impliedFormat": 99}, {"version": "55e49ce04550294b3a40dcd9146d5611cfcd4fa317eb2dcb2c19dd28dea09f58", "impliedFormat": 99}, {"version": "96149ea111d0a0017b95606821a16d4a1cf2470f1460549ba65ec63bf9224b5d", "impliedFormat": 99}, {"version": "5b290d80e30d0858b30aab7ccff4dbfa68195f7a38f732a59cfe341764932910", "impliedFormat": 99}, {"version": "a85ee477d4e97c2bfae6716b0faaaacef6b4f3de64e0b449c0347322e92a594e", "impliedFormat": 99}, {"version": "8c11d3a3eac4c18abf364d20dde653c8b4d3c3ad85bb55da285209140dae256c", "impliedFormat": 99}, {"version": "262fcc12bd0cb2fe7ce2115093ae2b083cf425329b7966d8857af78e1e33814d", "impliedFormat": 99}, {"version": "24f4daf278786772d9cee29876e85f5f6712c65b741b997a900b1d942c8f217e", "impliedFormat": 99}, {"version": "a2be1e277d805c54f038fee25fd291b5fdd76990be855454bd48e336b315fb8b", "impliedFormat": 99}, {"version": "dce9350553d244fa5ad6cff4e9aea3664d918113ddff74ef84210b0481b79f74", "impliedFormat": 99}, {"version": "8802c923b63c304b8e014600ff58fb9542323e842701aba9e69df60c7c979df5", "impliedFormat": 99}, {"version": "b5a14e52ffa8efd7e31e7856bbf36a7bce32446283a9b51e0a819b04a94f2ce4", "impliedFormat": 99}, {"version": "9cc999adecb60f81915c635cc91acdb0b79904370653acc283b97656b5b2cfa8", "impliedFormat": 99}, {"version": "80249dc33a16d10faf6ec20ea50d4c72b0d92e55070bba0327de428e1d0979e7", "impliedFormat": 99}, {"version": "7367f5f54504a630ff69d0445d4aecf9f8c22286f375842a9a4324de1b35066f", "impliedFormat": 99}, {"version": "0b86afbb8d60fd89e3033c89d6410844d6cb6a11d87e85a3ef6f75f4f1bae8a8", "impliedFormat": 99}, {"version": "9cfb95029f27b79f6c849bbb7d36a4318d8acf1c7b7d3618936c219ad5cddab7", "impliedFormat": 99}, {"version": "2a4181e00cfe58bdce671461642f96301f1f8921d0f05bd1cc7750bbf25dd54a", "impliedFormat": 99}, {"version": "24e33e2ece5223951e52df17904dcc52a4022be3eb639ab388e673903608eb37", "impliedFormat": 99}, {"version": "506eaf48e9f57567649da05e18ddd5e43e4ad46d0227127d67f07152e4415f29", "impliedFormat": 99}, {"version": "9e5247c2cdf36b8c44d22caa499decd252577b8b5f718b498f7a8b813d81a210", "impliedFormat": 99}, {"version": "69abcf790968f38d1e58bccff7691aa2553d14daada9f96dcc5fe2b1f43762c3", "impliedFormat": 99}, {"version": "5e88a51477d77e8ec02675edf32e7d1fccdc2af60972d530c3e961bd15730788", "impliedFormat": 99}, {"version": "0620fa1ded997cd0cdc1340e9b34d3fe5e84f46ba109b4a69176df548e76081c", "impliedFormat": 99}, {"version": "8508ed314834f8865469a0628cc8d6c31bf5ea2905f8a87f336a2168e66f91f4", "impliedFormat": 99}, {"version": "9757602b417a9364a599c07507e8c9a4e567f78829eeb03a7c64b79ffb16caf9", "impliedFormat": 99}, {"version": "e0bfc7204238bd5b19f0b9f3cd8aa9e31979835772102d2f4fa0e4728140bdbf", "impliedFormat": 99}, {"version": "070ff67371e23b620cbf776e08881a3d1ff6cdf06c1cf6a753fb89b870c6f310", "impliedFormat": 99}, {"version": "d2e8a7070ff0c6815be4ccca5071fe90d7923702e6348fa83275b452768f701a", "impliedFormat": 99}, {"version": "63c057f6b98e622b13aa24a973bbdf0fef58d44e142a1c67753e981185465603", "impliedFormat": 99}, {"version": "2b857bdc485905b1be1cee2e47f60fc50e4113f4f7c2c7301cdc0f14c013278e", "impliedFormat": 99}, {"version": "4abccbf2fc4841cf06c0ff49f6178d8f190f2645acda5d365e61a48877b8b03e", "impliedFormat": 99}, {"version": "b4ababf5c8f64e398617d5f683ad6c8694f19f589485580623a927121cfab64b", "impliedFormat": 99}, {"version": "f856d3559afde2a5e3f0e4e877d0397fe673eea71ac3683abb7c6cef429c192d", "impliedFormat": 99}, {"version": "8148fe494a3556aec26a46b0deba7a85d78883b285e408ebf69ff1cfd1531c00", "impliedFormat": 99}, {"version": "0942f7d40c91c30a5936d896de2194238ad65a45e7540bab7f7f588b70242bb8", "impliedFormat": 99}, {"version": "b808dbc3d555d643bd6410da582c2d7512b39dc8331acef7d4752fff0f390b5f", "impliedFormat": 99}, {"version": "65971cd38702bdce2440a7322eccccf978a37e481b44e22dd0b34aee30e0b6dd", "impliedFormat": 99}, {"version": "c6f038949f364df4f690cebfe93324f54d53c9c50aec6c8e5508b7f6a6ea4df7", "impliedFormat": 99}, {"version": "58a0bdd8fa7be3a362ce850e4af11c7a4f82abcbfad36201463f7b28ebf53e7e", "impliedFormat": 99}, {"version": "cc9f07af7679c686e5e68c3933a4430af6ea651ed0c1cfcf0db7c60576d05ccc", "impliedFormat": 99}, {"version": "d45698ab81cc9a9722ec492e7442de1136be3c2a5c830b7c700c3cae020bbf70", "impliedFormat": 99}, {"version": "18441c1a35fed75775881c3b918c3ea4a630f02e43c8179225a268055907b140", "impliedFormat": 99}, {"version": "bbe0ac66e24ba0c5d30dfc8f0579e3c660f8e1f3b8f234c7cbdd9fd2db9ed22f", "impliedFormat": 99}, {"version": "63e65622cd147ea99f39f8833c65d7c2b7a0595c86ce71e92e04b07d1f38d3ad", "impliedFormat": 99}, {"version": "6a840e9604c761dd515f8c76ea08c648beed01129b75133e0d54e24372802302", "impliedFormat": 99}, {"version": "7b853ab7e6a660ca2dfdc36eff9d3cb5215b3e10acbe65a09ed6d9be52c38d9b", "impliedFormat": 99}, {"version": "cb1f24cd504d21fe92ea004fab2b3e496248b4230c3133c239fbc37413a872b7", "impliedFormat": 99}, {"version": "d7ec8da78b951af56a738ab0586815263a433ef3517c4e3ea6aad5dfd65c4a04", "impliedFormat": 99}, {"version": "6adb1517628439ae88aeb0419f4fa89eacda98f89791fcd05fa92ad2cdc389af", "impliedFormat": 99}, {"version": "87e256c8149c5487ef2c47297770c4e0e622271ac1c8902dc0b31795062a1410", "impliedFormat": 99}, {"version": "99c98d7abbf313f8978c0df4fae66f5caf05b1e7075a2a3f0e8cd28c5abb56d2", "impliedFormat": 99}, {"version": "3d7c052002e317d7ff01dbe4c6cf82aa20b6ef751101139c38c547636d872ffe", "impliedFormat": 99}, {"version": "353fd6acf4bc2232c850bcf24fa6512a85517623f84dabe4dc4a22fcd0a69f00", "impliedFormat": 99}, {"version": "f9c4bdf33b97ce2f7c4fa422c32ce85f8f4cafa4421e02172279ee5ebd097804", "impliedFormat": 99}, {"version": "1f098514ce3fb820e89bde510a34b939f281581a7c1e9d39527ec90cec46f7c8", "impliedFormat": 99}, {"version": "54b21f4fe217619f1b1dc43b92f86b741c55400b5f35bfd42f8ea51b2f6248a1", "impliedFormat": 99}, {"version": "48d9c8e386b3ba47dd187ee4b118c49d658cdac580879984b1dc364cf5a994ca", "impliedFormat": 99}, {"version": "b69cecaec600733bb42800ac1f4be532036f3e8c88e681f692b4654475275261", "impliedFormat": 99}, {"version": "bb8e4982de3a8add33577b084a2a0a3c3e9ebf5a1ec17ddfe6677130ec19b97d", "impliedFormat": 99}, {"version": "5a8aa1adc0a8d6cf8a106fd8cc422e28ca130292d452b75d17678d24ab31626b", "impliedFormat": 99}, {"version": "f4d331bd8e86deaaeedc9d69d872696f9d263bcb8b8980212181171a70bf2b03", "impliedFormat": 99}, {"version": "c4717c87eecbb4f01c31838d859b0ac5487c1538767bba9b77a76232fa3f942e", "impliedFormat": 99}, {"version": "90a8959154cd1c2605ac324459da3c9a02317b26e456bb838bd4f294135e2935", "impliedFormat": 99}, {"version": "5a68e0660309b9afb858087f281a88775d4c21f0c953c5ec477a49bb92baa6ec", "impliedFormat": 99}, {"version": "38e6bb4a7fc25d355def36664faf0ecfed49948b86492b3996f54b4fd9e6531e", "impliedFormat": 99}, {"version": "a8826523bac19611e6266fe72adcc0a4b1ebc509531688608be17f55cba5bb19", "impliedFormat": 99}, {"version": "4dc964991e81d75b24363d787fefbae1ee6289d5d9cc9d29c9cec756ffed282b", "impliedFormat": 99}, {"version": "e42a756747bc0dbc1b182fe3e129bfa90e8fb388eee2b15e97547e02c377c5ef", "impliedFormat": 99}, {"version": "8b5b2e11343212230768bc59c8be400d4523849953a21f47812e60c0c88184b3", "impliedFormat": 99}, {"version": "d96b4e9f736167c37d33c40d1caae8b26806cdd435c1d71a3a3c747365c4163c", "impliedFormat": 99}, {"version": "363b0e97b95b3bcc1c27eb587ae16dfa60a6d1369994b6da849c3f10f263fd04", "impliedFormat": 99}, {"version": "6c7278e2386b1993c5d9dfa7381c617dc2d206653b324559f7ef0595a024a3da", "impliedFormat": 99}, {"version": "f5d731a9084db49b8ffd42bc60aecb28f90966e489261d7ec5f00c853efc3865", "impliedFormat": 99}, {"version": "4dcc76850d97256f83a7d45b40327725db3aa7ee02dee3b1e860ca81ce591694", "impliedFormat": 99}, {"version": "70fa22a23b35e04482f13ab7f697a057506503e21ced87d933359e3224c92ed5", "impliedFormat": 99}, {"version": "709622bea0f7188c66bcee996bd4f24221c69d67e1d04797a11ebdd1311096cd", "impliedFormat": 99}, {"version": "e8ad189c7d2932a01feadccefca9c873bee40d202fb53f708f1e7b1efce4ffef", "impliedFormat": 99}, {"version": "ed3dbe543bbf46c4365e3eb5faa3fa87f0fe0c3db4b2476b8f430838432e2b8c", "impliedFormat": 99}, {"version": "1ad2f20d17cad8ed17df10daf3f9050161fd42a86d5b7afd0a1dacac216e9c14", "impliedFormat": 99}, {"version": "4e6502d4dc180cdff48d77f6ee04007167bef42f7b5488dbadedb0ddb1e9cdf1", "impliedFormat": 99}, {"version": "e41e03387b7c74aae146473ff507c26b07699cfcd953f79dd174bfd624bcb5d0", "impliedFormat": 99}, {"version": "ff671a3c1efcc1a96ca6f418c7a9616ae4a4c6110ece811fc1ec8013a3a24e6b", "impliedFormat": 99}, {"version": "a105278208759f167642ea5b37b78661edf4b0350824ad2f961a329e5976b9b6", "impliedFormat": 99}, {"version": "6f9a389203f44e1c344e5e5d8c0ddad05f0f2e033d0657297894cd8e6ca4747f", "impliedFormat": 99}, {"version": "636ddb4225f892b1033182ae24af259fe30d5209a2b9e69d7374c3268818b9d3", "impliedFormat": 99}, {"version": "c00c3b2b915c5cd789a78f86c98c211c78646872ed84ddc478994e97c6560a0a", "impliedFormat": 99}, {"version": "592640ac835589f476f9cefbffdfeef79dc327bb9b25c0a3f92549fcd8e8c514", "impliedFormat": 99}, {"version": "24033c6280d58689e7cdb5af09e2766c6b44a3747dbb0d844f155bd0621024f0", "impliedFormat": 99}, {"version": "1914db9d25d18ff046611a41a8129ad01c829d5f9565f16660c7d09c66f776c6", "impliedFormat": 99}, {"version": "054c4bef46bc70b9fbb18481f501bac861cd54af683fe5942e5c7e7d3b0c1fb5", "impliedFormat": 99}, {"version": "d6ce9fe8c2849756dae3c9e11de07966bb58b6638a462098a3a1b23d78b56ef0", "impliedFormat": 99}, {"version": "0f149ffde075123eb05b9aefdd405d5dc1acd729f94b3dedaf9f48d9fbbe2348", "impliedFormat": 99}, {"version": "193a5fc1bfbc703c3772e05dfffb1c821ef30bb2d787f906fc26c38718bb35bb", "impliedFormat": 99}, {"version": "dfdc408e78629b12771eca9a58edbeeb2f4783e79841368a069b8eb65ce447ce", "impliedFormat": 99}, {"version": "513601842e2f161c0e7c3bc35c433f793f338b5d7d0465423d071486f43b65e4", "impliedFormat": 99}, {"version": "5270479971ab757c197fa22d4eb07bf7bfc886440a76da240e095d5ffb2e95bc", "impliedFormat": 99}, {"version": "8f5d63fde9f0ace19cfcec1a2bc4bc0efec47b89465216817204448dc6dfd5a2", "impliedFormat": 99}, {"version": "65323bbeb0b10634c92484812f6a0020d3ca38a888c2a536962b425cb77d8e77", "impliedFormat": 1}, {"version": "767183261649b963ccc7daa3d2ae38cc604ce60fc3a453a15a8afa9a4daba71f", "impliedFormat": 1}, {"version": "5fb2b92475a3963e7b4ee8152cc6c3ae066081364b4abaeea695a5001db32e63", "impliedFormat": 1}, {"version": "890d6c959fe26e8bd017bbb9b25623c227368fa1983a8966055c960b14de1452", "impliedFormat": 1}, {"version": "4b5ed80412f64641dc5caf5af1c98d8083315bcf5f4d9bceea7b6aac4a1b865b", "impliedFormat": 1}, {"version": "81957f051f71d2f4b0b20fbe8bfc40cbaa4d9a441ee3af3ec82646a96076429d", "impliedFormat": 1}, {"version": "e4630dcc04c04cfed62e267a2233cae1367a7366d5cadcf0d2c0d367fd43e8d4", "impliedFormat": 1}, {"version": "f7f13164c6c9b9e638ac98ffd06041a334cb20564d24d37185e29408d00cea8f", "impliedFormat": 1}, {"version": "eec0d8defb7ed885473e742b9298a2f253f2113688787c2495b4f8228bc22590", "impliedFormat": 1}, {"version": "de2cddc05d2aff0460f1bb27f796e9134b049e4fab33716b4d658628e0976105", "impliedFormat": 1}, {"version": "4bd3e56fca57ce532152c64036a2153d61f2c1acfc27b4d679b1f4829988b9f4", "impliedFormat": 1}, {"version": "7640a64392d0920c04d091373eb8ca038d6e80cc5b202bddcb0ea0937f90def4", "impliedFormat": 1}, {"version": "ec817057681d50c1c0d2a3c805aee50e6df7c51c60484fdf590c81b9a5001931", "impliedFormat": 1}, {"version": "bf6c2b7d7ef94e5d5add264d87aa2321e2e1d875d74e2ff1a5870b3fd0fa4506", "impliedFormat": 99}, {"version": "da85d4bf5436447eea22ed6404226fa97f44ae375559ac97b5d3d5d86c1d5b72", "impliedFormat": 99}, {"version": "e86e6db08b9106c95115542563d5a49d20447cf08cd2994dbd86c1896c49dc08", "impliedFormat": 99}, {"version": "c3bbaa7348f9e5ca7e7c67c18aa0db9cfbfb1485ab4c13b73e8e0a15766b99de", "impliedFormat": 99}, {"version": "338d21e6e39eac5d7df7fbad9179a489c4689471775cedc24a4eacd2b4acfc97", "impliedFormat": 1}, {"version": "71c894f7dbb289f6b9907e4d70f0ccaa746be732a7d65354e6bcd23405fcc1e6", "impliedFormat": 1}, {"version": "0cb45071af866142b4198636d458bd6d2f564b7d79896907a75b01d66c135625", "impliedFormat": 1}, {"version": "e151f7178771544d572824da291a8e2c45325c0cc2dbfe513de06c9d3cf771fc", "impliedFormat": 1}, {"version": "16d707a765a9a3114e9911c1a57634fb3c90d678539c2d6d793c30cc87e759f3", "impliedFormat": 1}, {"version": "4ce2e4991a21c8e6a98905d0dc3a9efaf75e8e8812a2b930f77ed8aa4435784d", "impliedFormat": 1}, {"version": "4b86cb06a21c36b5ff47731a046e0109cb41d540e17215b8f95829e30da1bb94", "impliedFormat": 1}, {"version": "7cc83c9b21c59ab3b08196adbeb13d999e16c56a5bbf89864d6e01cc1a6e6204", "impliedFormat": 1}, {"version": "102334bccff335c3ef1c556fabac2c2f12bf93ce1a5cd8ce826ed188707496ed", "impliedFormat": 1}, {"version": "c9144f4f50f868501918f526697deb558eb9d82bcad179b3807609246ba6b32b", "impliedFormat": 1}, {"version": "8bb219fc6b96eb8fee00d73aa6e570b01885a01be42f2b85d93a1fa102f52ccd", "impliedFormat": 1}, {"version": "fcc36716f4a5bb4ac1babbd30a3c55483def152357c0d17c570ecc406ef8f159", "impliedFormat": 1}, {"version": "66c695ccbaa50b938c0e058b28b3a004fc8954e7e0f7f01177bae4bb8e92cc0f", "impliedFormat": 1}, {"version": "6e01462f84beeb73382f987fae1bc554f0ed6d9f70056106f417a9f6088bdbc5", "impliedFormat": 1}, {"version": "1b46f9a444f79e8aaa88e9c7ccff9f131ab101015b8933ea3a8fc7cc2021adc9", "impliedFormat": 1}, {"version": "7749ee7c2eb72db8f09271082b925580321c546d8b2aef68960f3f4bf483d454", "impliedFormat": 1}, {"version": "3d77e968a4a37fe3857daf2227ccaa7efb978830a6873de10d6a887daabda9cb", "impliedFormat": 1}, {"version": "0ee14e6d06ffdcc74c5fc496224c15e6275bda1c413ffc86b0ad19d1452898a6", "impliedFormat": 1}, {"version": "b10364cad5f3ba55bb99c69d21eb4a0df657c7a36027a2618f8739ed69142570", "impliedFormat": 1}, {"version": "c7c4c05e6788ee40a4f1e374ab1355d3a8dcd1c947afadc8ac1dfdd0bb0ea41b", "impliedFormat": 1}, {"version": "0a5e955193cb8aea98e00bf54042651f8c8b9b00c87337ff3c0ce8960345b5ba", "impliedFormat": 1}, {"version": "5ad71db5434af4e0d796a387bb7f4b7c1837199b866723921e5bd67fb01c2f0f", "impliedFormat": 1}, {"version": "059c53f6ef59639c574a2dbf541e2013ecd432d1223ef1dce10c1caae312dc03", "impliedFormat": 1}, {"version": "0a176b1b69c74bcc70bb1e3220fb14f5377e50b3fca16546a78c8ac87d84501e", "impliedFormat": 1}, {"version": "da11218c9b5629fbd61c9d3d6a2091d60a2d7c78d90609ef8f401bcf1541feca", "impliedFormat": 1}, {"version": "938492e3d181452f0cd89248fdb15e8126ac2aab687948d8d488d255d7a4fea6", "impliedFormat": 1}, {"version": "b1a02c272b834972bef5cb8d9c79acb0352966ed5ae3a37482cec39da5e51276", "impliedFormat": 1}, {"version": "25197fdcec1f0b168131c901881f9689b950c546a8d5d3620a9028765e9c91d8", "impliedFormat": 1}, {"version": "c2a5d0ee3f7dd09d0741ba10eb9d07ccc714ee5f7fad3e550fe8ad99eedda1a5", "impliedFormat": 1}, {"version": "81af227428e65ccfec74d0e439a810fcc2f33f3fa0e74730d486edf14ad2e367", "impliedFormat": 1}, {"version": "2e6b2ac20f09b0351d256155e9b8d8854434ed9a01ba7e55a87a5d13e4365f63", "impliedFormat": 1}, {"version": "3b0b108ad2bfedd6aba6c50b5b6aa969a75644935e40a749ecc2d28de9d9e788", "impliedFormat": 1}, {"version": "221e3b82ae572a418be0a8e112681c64aae84166f2c25f4fd39297d0a6958b92", "impliedFormat": 1}, {"version": "8a5fea1b0a68c64d9d830e878ea4e81efac6be802b4af1aa29cdfaad9be210f0", "impliedFormat": 1}, {"version": "367fd06f031fee62713fa846885d31c8cfa8101b7e3ab129f1d89d9d5e719124", "impliedFormat": 1}, {"version": "7163a9b5ad66c4e388aaeb18acf502e7c5afdbc52cb163bac5faf5d140abedfe", "impliedFormat": 1}, {"version": "a9347756f992e52cd1ad3a5a7f35f3176e05795f44f4299f2809f5458699981a", "impliedFormat": 1}, {"version": "eae25a53e17e999981a08fc48c987a1d1da75dfc1dd3728557451959b2a34ee2", "impliedFormat": 99}, {"version": "dd6585c64a7e2247adc774fe92a3c5bebac28af2c1bc06bbdafeb58a2813d725", "impliedFormat": 1}, {"version": "e0feff26b376e6eda473fea2273a6e96c5b380276a9ad9d3730cb607a0bcf1ce", "impliedFormat": 1}, {"version": "4a286cb32756749c240e70cdb3e751b676fd0305f9d35928e3d3976e0d3c39b1", "impliedFormat": 1}, {"version": "5b9716db2e3ca48d084e8baff9e2db5b2824ac7f7413e001dc33976e9f8e9636", "impliedFormat": 1}, {"version": "ceacff8e81d4c413c8cdba7ef6eb5d35a2a20a7b0bc5b316114bbdce888b58da", "impliedFormat": 99}, {"version": "dc62e0d530ec9d6b960e09c39f3eb0e1f0384511facc30f07e441b0abef2c5c0", "impliedFormat": 1}, {"version": "9da9c5a6b9c0020c1e8f2d087168d2ea5d43ad70fec8d8b31be7db2e2296ef55", "impliedFormat": 1}, {"version": "690bc2bd40e8d87f033168d99e4cde82607b8e0a181163350e7de07ccc98f5b1", "impliedFormat": 1}, {"version": "4619bbac2522271def9ec6d67b1b421a8fe4b85a90bc2f92ddd8f4b7a08f728e", "impliedFormat": 1}, {"version": "9019d34b102c683cf2810e38477cd5e8964e46a15870abcd27c108c31d90970d", "impliedFormat": 1}, {"version": "dd0b8ff0d6d5922e247969e6b3df41cae2d7294d000b056f9f93eda3e5bc31f9", "impliedFormat": 1}, {"version": "b53e04ce667e2497d2e1e5826eb739840b6d83e73abeba7d267416990cf7c900", "impliedFormat": 99}, {"version": "466d30b0f75773a2677ad69bc7d94facb224e061e0276c18b22a50d922e7a6be", "impliedFormat": 1}, {"version": "858520cadc012c1c8ff47ddc61686f50f4ee52c9b87a7c10b8fb84b60ababc32", "impliedFormat": 1}, {"version": "09e286c715f875d3772a8c196677934495eb7cc0b0222ddbf6756f4f3c57830d", "impliedFormat": 1}, {"version": "8ae39ac101bde0f657604d9128862e2522d683301ec37ba5dff17754eee52c08", "impliedFormat": 1}, {"version": "29b553ef6920613307fa4edbd656a105bf159c7db2438fd84fe624a4ef6fc491", "impliedFormat": 1}, {"version": "a69b64cc44b49bdadaa0de322b4b347b16fcb9c7fc08029a0372a082cb0f4467", "impliedFormat": 1}, {"version": "7596bc71c0939bf0b534c1ead88b0c13c6ce7a8ffed9e47fd176036b3a464062", "impliedFormat": 1}, {"version": "51cafc266445e20b92529192d8eb0ff3385ac1bc44fe125e84561563f338ec80", "impliedFormat": 1}, {"version": "86a9434282d3ac8a6438ad0d6bec7f9e6463106edb2dc63c26a9dc63a6050d24", "impliedFormat": 1}, {"version": "c16cffd6aa4a2c0701bd16332f4dfe6517a17f770f00218867d1fd4b13617fe2", "impliedFormat": 1}, {"version": "ff1e570657ad6fb9247c2d7160d8c318796b88ab5db739336515fb04547a2d20", "impliedFormat": 1}, {"version": "2ef29f5b7766615f2dc6b2fad24f5ce9e64204f6bdc035f3c9f90ade189196b5", "impliedFormat": 1}, {"version": "ff4a940841cc11f423a911011edef12b47541e48c02cd5be4e8aa0addb0cf3f7", "impliedFormat": 1}, {"version": "2ce39f6923be247a53eb5ea78ee1b5df3be8086253b8dd70be2584f5d8c2537a", "impliedFormat": 1}, {"version": "bac47ef1b5d6cbf8c3e80f672e8f9ecf1cbab10da5fd25b7f228702306fceff8", "impliedFormat": 1}, {"version": "3ef21503ad78f542c2efbd785f22a8c77e3798a2462be8a25a806937d4d85a3a", "impliedFormat": 1}, {"version": "bd1ff4e0676496bf4f98f4f3ee31765bb49339aafa8b076952ec27cb041db0c7", "impliedFormat": 1}, {"version": "5b89a6e06ccb15548326fac4c3ccb65892d8b10cf52fccb2867d0eb9a0b27bfd", "impliedFormat": 1}, {"version": "2aba54f9c5acaf97b2f54e15dd52b88a26069c04e40118c5c1b4e1c7d0b13704", "impliedFormat": 1}, {"version": "22b47c263603277f4caae17f9b5aa564f600a9b770f05920e68bee09394e2178", "impliedFormat": 1}, {"version": "bdb92c931b192ef315b53cd48aa02e4398c251a8ea8800492cf0f43cb038ba28", "impliedFormat": 1}, {"version": "eb37622408d5a60a38a9141acc5ce584f031df61fa67eeba98d495704fa14ddd", "impliedFormat": 1}, {"version": "d787f15bf7abaa3a0d38c657e4281b13f86cc38b8845094a6977d583a9347ea2", "impliedFormat": 1}, {"version": "8cb8894f63c1636f90fb7730fe50e421cdf56c779d0ba298010f0be89022cd39", "impliedFormat": 1}, {"version": "749fb78249cdfc1fbb9ef8cef948a13f85f9942ca5489f1468736922500d78e1", "impliedFormat": 1}, {"version": "30fd5d3577a7e58f873b83049dfbd2f173c350851c17b1e9a4b0878020626b97", "impliedFormat": 1}, {"version": "66231c5bc015e15786504a220d622ddc6aac651b2a49f9cbf3fb945e27e733cd", "impliedFormat": 1}, {"version": "c23cd69e2b2cada942f0bd916ecb7904b98dc3fe10cdfb0db39d3dcf0a69556e", "impliedFormat": 1}, {"version": "5426089e9fcec830597afd777d68bfe372de694dea4a8e7e68e3ca28acc8a6db", "impliedFormat": 1}, {"version": "8e302e6fa5c43ca2384fe54b39fbdf0c320224a6919d71da5efc423366551314", "impliedFormat": 1}, {"version": "fdc1bebcfdb5da0d3db8b11a94e68e0f40aff9f126ba06512c74e83cbab03a03", "impliedFormat": 1}, {"version": "9139c1f3d72a1419734da74c4cbed997d073dafdb8fba63f9088a6fce6f23c99", "impliedFormat": 1}, {"version": "79314b827217deb6d8518be67e201505f4da047bfd8fee11457f997403e0e7e9", "impliedFormat": 1}, {"version": "5e788a039b7435497ef94c30ceff9f92ae097522e53ee75652407f1fba79579d", "impliedFormat": 1}, {"version": "8782f99016b5b587eeb2e57c913a0a9470200941afda788224ce960fae47eeb4", "impliedFormat": 1}, {"version": "c471dc722410fa62a4ff2c7f033cc15814087f5b445b5e9fbda596cd4c228a2e", "impliedFormat": 1}, {"version": "0548857ee66b6fad6f26fdfaa76ee25334fa62454997c3a954726c166deb6a5a", "impliedFormat": 1}, {"version": "a1ffd087cb5a5f76ff56226148d0acf8d223a9474eaf9d97dbd45fa6a19c1e58", "impliedFormat": 1}, {"version": "cc5f3ec646bf93a7f13e27a9bb72f42b2a094a551a015296361cfe7f0d4350d2", "impliedFormat": 1}, {"version": "f9e8a5ef3b0cbc104b6e66b936e5e76119630186ede7d3bef2cf53df506ca5a6", "impliedFormat": 1}, {"version": "3644cfe268c1fe7de7b18619b385f8fdae10531ebd0ea4193ca6ab8bc8175e72", "impliedFormat": 1}, {"version": "a05cfa018e37d5f3a5f39773145e5e77d18f32819ba3e115cd49b468f3ac139e", "impliedFormat": 1}, {"version": "e2ecb11f739a7f3556659fee61d144d3ca1d715436ceb727f5701cd12461a65b", "impliedFormat": 1}, {"version": "6ec1463df8c2070371669bdaee719272607903467a19f9883348166b50af8d54", "impliedFormat": 1}, {"version": "cc08bd4e50ec465e694826816b4797e6f6a4a5211e98bb76bb05342439c7ce38", "impliedFormat": 1}, {"version": "96cfa668e8ad2f88bf255184086129046467ff400f678de888c2cddf82b999ec", "impliedFormat": 1}, {"version": "8d27a16268750bef7f8f2816fdcb28a9500fb9e6ba5a1e5981a053d35b416c3d", "impliedFormat": 1}, {"version": "d90ff671df07b5dc26709a9ff6688a96fbf467e6835bee3ad8e96af26871d42c", "impliedFormat": 1}, {"version": "7a0555e1186c549e113b9603b37994dbdb9b0aea18c1ebaccbade9fba289d260", "impliedFormat": 1}, {"version": "ad1eab49ed8d2c7027c7d5b8333217688ef1bf628c6b68ca7674329c262433c5", "impliedFormat": 1}, {"version": "c8d412a9b07756667bf4779a960226b71418a858cb6801188992f4e9ed023839", "impliedFormat": 1}, {"version": "7801e1a8f4396ec3a8eb0fae480baf1fe9ea036a5d68868337a7bcc50bf769e4", "impliedFormat": 1}, {"version": "9dfbe649c60c743bf0cbf473639551cf743a1acdead36e3d66a8e3feee648879", "impliedFormat": 1}, {"version": "c214b33fb74b0ea35c672b1923e51ab30a1e3e8f876a09e94148a35f3cd2f5db", "impliedFormat": 1}, {"version": "e3846aa20e866fce307a39d7efc4e90eef08ea0884b956738458fe724684e591", "impliedFormat": 1}, {"version": "c19feddfc23f04fd9cda6b24568894eb79852a26b3f9733cc0472b91bfc1c0a1", "impliedFormat": 1}, {"version": "9ac8b88f902bd4c2212ae16b11d26421e50669f0a0643586083281176f9d9132", "impliedFormat": 1}, {"version": "5180e5bae39bbb8baf8aeba9100814e4f4d017d41638a4e609ca5c3ce83993ea", "impliedFormat": 1}, {"version": "b69e0431f9b7f6e6c5f0754e8a3dad3f263684ed4c7406d4be7649eeb7d9af27", "impliedFormat": 1}, {"version": "a10e2f2466f0ed484ef74a385bfb5e63f2b202d51dbf1bb4c51c294a70ba92ca", "impliedFormat": 1}, {"version": "5347737b57f1c1cce11c140228c4e4068eca4c2435b1e4beb4d46e60c5d5e55e", "impliedFormat": 1}, {"version": "631b3d9fcc0fd5e08affcdb01b76f5d34e1f1c607031d03a6d621cf2aa63b2e8", "impliedFormat": 1}, {"version": "ef7ee4e86977bf10f68dc2e1a3378bbebb4e97dc476bac72ca9315cc7e89e3e2", "impliedFormat": 1}, {"version": "3a21d83e527b6d812d75c719134026ffc18efe0f01c76e6441b29d77add09e26", "impliedFormat": 1}, {"version": "91406250d53804ad5f3a42af40a5e17f1ea3e54c493076f6f931e77efa6db566", "impliedFormat": 1}, {"version": "1fb51788ac6acb1e6cba5cf7e99b03d07ca8b4120550defd561b331dfa8e816d", "impliedFormat": 1}, {"version": "3cc15f1ebcd824e7752f390dab07e92b15e02514f2c9ceb1737ee42d4e3164e3", "impliedFormat": 1}, {"version": "830c34482ca4bce8c4fa2f14cff1197fce2017471752441e95b25112827ceef3", "impliedFormat": 1}, {"version": "f00b89d69f241f3e74269c2de5d3cd564fea760fd4d2a403820ed5b077819724", "impliedFormat": 1}, {"version": "d2e41732e6551589732bb50507b48762982fbe68fcb739f7a4fdacf7a2eb6bb1", "impliedFormat": 1}, {"version": "601d2c65eeacc9af0d026ffae767c36e64ca3a2e3c0169aae6ed52b95e29860a", "impliedFormat": 1}, {"version": "ccfc90c02782570e4b49adf011601291b7392d7f9b25cf8d7a0c9be1761f62d4", "impliedFormat": 1}, {"version": "20463dff6b7f9ab3573ceb503f0674d34c3571328bec2152db193e732a29bb7a", "impliedFormat": 1}, {"version": "528e1e94b95de11acf4545f8b930b460e18ef044579a24a8b1b2d40c068fa89e", "impliedFormat": 1}, {"version": "fc8a3cf4a55f7d1ae3f2efdda84bbeaeea605a92e535ac52b99deed6366917d5", "impliedFormat": 99}, {"version": "4d0d2708fe857d7a1a936da40fb357b2f67f22b0e0c4994211ee6a6ccbd48a33", "impliedFormat": 1}, {"version": "21a572262a50e7b603382800b727abae5b7d52ccd71ae163f8dc4cac379f7274", "impliedFormat": 1}, {"version": "e674342d40884888334a6cf55ac4276abd77f36f51687f56a47d5910fd9ea033", "impliedFormat": 1}, {"version": "ac04b4535689f4fd637d97c9811d5fafe4d2209d497c0eae539c3e99d81978fc", "impliedFormat": 1}, {"version": "c3a31b99b4de2d53784cf340ee9b36907f2b859dcb34dd75c08425248e9e3525", "impliedFormat": 1}, {"version": "f03893fc4406737e85fd952654fd0a81c6a787b4537427b80570fea3a6e4e8b6", "impliedFormat": 1}, {"version": "518ee71252a0acf9fce679a78f13630ab81d24a9b4ee0b780e418a4859cc5e9f", "impliedFormat": 1}, {"version": "3946840c77ebba396a071303e6e4993eaa15f341af507a04b8b305558410f41e", "impliedFormat": 1}, {"version": "2fba8367edfbc4db7237afc46fd04f11a5cc68a5ff60a374f8f478fcc65aa940", "impliedFormat": 1}, {"version": "8d6e54930ac061493fa08de0f2fd7af5a1292de5e468400c4df116fd104585a2", "impliedFormat": 1}, {"version": "38c6778d12f0d327d11057ef49c9b66e80afb98e540274c9d10e5c126345c91d", "impliedFormat": 1}, {"version": "2ac9c98f2e92d80b404e6c1a4a3d6b73e9dc7a265c76921c00bbcc74d6aa6a19", "impliedFormat": 1}, {"version": "8464225b861e79722bf523bb5f9f650b5c4d92a0b0ede063cc0f3cf7a8ddd14a", "impliedFormat": 1}, {"version": "266fb71b46300d4651ff34b6f088ac26730097d9b30d346b632128a2c481a380", "impliedFormat": 1}, {"version": "e747335bc7db47d79474deaa7a7285bf1688359763351705379d49efcddc6d75", "impliedFormat": 1}, {"version": "20f99f0f0fdf0c71d336110b7f28f11f86e632cf4cf0145a76b37926ffaa5e67", "impliedFormat": 1}, {"version": "148e0a838139933abaeee7afc116198e20b5a3091c5e63f9d6460744f9ad61a0", "impliedFormat": 1}, {"version": "72c0d33dd598971c1caa9638e46d561489e9db6f0c215ced7431d1d2630e26d3", "impliedFormat": 1}, {"version": "611f0ccef4b1eebe00271c7e303d79309d94141b6d937c9c27b627a6c5b9837f", "impliedFormat": 1}, {"version": "e2d98375b375d8baa7402848dca7c6cd764da6abf65ecfaa05450a81a488157f", "impliedFormat": 1}, {"version": "b6254476d1ab4ce8525ae5f0f7e31a74d43f79eecd1503c4de3c861ee3040927", "impliedFormat": 1}, {"version": "65f702c9b0643dc0d37be10d70da8f8bbd6a50c65c83f989f48674afb3703d06", "impliedFormat": 1}, {"version": "5734aa7e99741993aa742bf779c109ced2d70952401efe91a56f87ed7c212d1b", "impliedFormat": 1}, {"version": "96f46fdc3e6b3f94cd2e68eca6fd069453f96c3dea92a23e9fcf4e4e5ba6ecdb", "impliedFormat": 1}, {"version": "bde86caf9810f742affde41641c953a5448855f03635bf3677edf863107d2beb", "impliedFormat": 1}, {"version": "6df9dfe35560157af609b111a548dc48381c249043f68bcdf9cf7709851ac693", "impliedFormat": 1}, {"version": "9ba8d6c8359e51801a4722ce0cbf24f259115114a339524bb1fdb533e9d179da", "impliedFormat": 1}, {"version": "8b1f2a75b36d4a5b52771e1bfd94706b1ec9cd03b0825d4b3c7bcf45e5759eab", "impliedFormat": 1}, {"version": "97d50788c0ec99494913915997ab16e03fb25db0d11f7d1d7395275fa0255b66", "impliedFormat": 1}, {"version": "aea313472885609bd9f7cd0efdc6bc17112f8734699b743e7fbd873d272ca147", "impliedFormat": 1}, {"version": "116f362c8b60668e7a99f19a46108ceac87b970e98678a83ae5b2a18382db181", "impliedFormat": 1}, {"version": "b4fbfaa34aacd768965b0135a0c4e7dbaa055a8a4d6ffe7bedf1786d3dc614de", "impliedFormat": 1}, {"version": "87b9b8fd9faf5298d4054bfa6bf6a159571afa41dfdbd3a23ea2a3d0fab723bd", "impliedFormat": 1}, {"version": "cde5f66590c3a1af8b32b89444c7e975de93a3f4b7fc878087abf4187c7949fc", "impliedFormat": 1}, {"version": "31ad2c3e09a73713d4c52f325e0fa0cf920ea3ea6bccb1fc4b271d9313183883", "impliedFormat": 1}, {"version": "5906db268438b1a7a124f8690a92031288a8e42e6aea0f525158031b324427d7", "impliedFormat": 1}, "79eae2ba643bf1619efee5ee3b894331a6cf784006957b4252c369fbf6b3cd0a", "6382c3db336809a6f3c2835765f9ce726c257829818b4f264f20ca4cadb3b168", "9dc7e7ccb54fe4fec2c7310e1c71eb912cdf738aa260c0a6b4d7192fcc38daf1", "d49a4e24050442648be351b4d72f37802939693dcef564a6e2716c56e3c6da06", "0ba76a9ba51e6a0e9ebd11e5725ca5bed8dc6838aa8a541ae1ee0e2878455be3", "4d67e387bdfba0d1e6b3cebad267e13bd634533ee630a8cad99abd8f3d285aad", "a8523a29b52db94f56df53c642a7c9434b77490eb2254f440024e1c129a54fe8", "d39e52d837eb01f8b287a1cf9a5925a3f72dc1bc8cc75e06f7403fc7fa97f995", "e6e7eefd72a034b617f0343a6cf7d45e2f34fcb14fcd3a20a3cafb44d2538d1f", "b793013d324315ea327762116b2924c84bdedf4e53beb253dea1e507ec94ef32", "a2599e2bfb6f1cb159f49edef869c1f74f71b29aef6b1e310c2456d0ebff452e", "f084d8df89708810378c6b183c81f29a0938f8541172cd331dedfdcca60cd130", "aa556faf02591124e6b72af398a1060fdc89a186407b80fb489f9ddb00609333", "d225d5bd9a2f3692634e186e3a63f9dd3318862269cdbf465bf881698f29980b", "a72a94d82af4922f96b62ea18c1f6cee94b054d097a2970a6637c742e9a3dffe", "7fc536c07a4c19397e5c73fefa3e5a47b008153dfcf3c1ffab75dbfd5805fcaf", "47f5320d7d67a69246a82f8bc215118a6595a4fac98a7146a6b87272ee31a91b", "b55005f86ccff5977c4ec5e41ebc9586041f3e3d0073afe6edc78d8bb8964188", "34ed8e54eebd14e3964fd7a0f974338d7f8b5e2c5cd1ea4ebb1b8e838f9fca03", "cb70298edcea7891b40f5106ba49b4a5bce451c96bf0e43ed077d3d7a53ee5f4", "0fe7269506a3edd3711cc2a4a91c6cf1737b462b492de0567a84bf3f0d1543f6", "6d9aea5bbbfdad61eb01cbd63ea29ce36cc5b005f03305e9c3a6cc06ec129a87", "c42f189da1f6f19ed435d55af60cc35aac3d7509f95d564426248d591e426a21", "32a11c30e19c832f5b58c31e6b5dfe0d0522e628707f053d6fdcd5ec334a2dfd", "a3a3a89e5f2c0bd8e8dd691a2a5a067638fe8d587a6aa2124def0314a55de9e5", "1cd0ee6c4f9de008a0a635068797e01c1d8da480a21cb9f38fa117351a1c6a58", "238bb3505379a3c88e8b23d0132756170b1974b188a39ec785b44deab75d3334", "bacec23ccb06415a0ffbb961144d60bad045939c70f503bd37a014459f80260e", "b981cdc91afff2ff1b2581379c49d53ef8f61e1f3a2f2f58aa91d051e0e66d6c", "26667532e97fa203d27c3e79fbeb746b336ec722d24f142512fcf7b47c970c18", "9f2bf0da863cd98d928778b69ed6e8c65bf68103555c1518efb40bba227e812e", "d1903b110b123f259f6e2f0da92d24504a5b3723040f4742bdab205e2759a679", "aff372f848404815240e4517aa82190c48b3cb0237ea4c0be4052a1fb79bd609", "760c23dc2193fb977e9cb817057ea53ce9305b34be6c29597fcc8a186133a991", "27a4c6ea4f9428935eb28cf09e2e9eaeee51b9fb0c25b0875396f23fba965dfe", "eda7c0524e8775275e81709990b6db3a170c89e63935f52c16d55b30d2aae7dd", "dc2efc3a66e4a7249089ae04376cfe865adf82a108def3d1e8414309cb59040c", "c0ee9388653339164be37b7cd7467c604fb8fb733e0a95d94687e8113bc944c8", "9f6103cdfc0328cde79a421320b4346738d7fee5ce3e162beb7bfdb617bafd1a", "36ca4e669a76165b1ea43729582d3c7d367dd50a2e0122e7bdf866fe99e257f2", "4b323b009690b4eaa2d6f2176b71df1927e6a1e8b304281e68a9f345073f4d38", "356097b178d2e566b241e4107198ea278e4bf9653cec99028333e938adb56461", "579256c32d44fb02723fff3826f80c1a56ef3bc92e0d8c694ebd3448b961d33b", "4fb4c2fd493bd5f99c80a53015cb7a538b9bb49562b87baf4f590d5a98520e69", "64a311e44be66d43d417a3fc892227887ec6241e13b583e91cdfc9c382a389d3", "3071268943607a413c42d803f04e66b667d2734ef08f5106f3631a2ef4dd684e", "3bb227915ee47a799d79060173a4bf6ff15d7baf4d05392d30408c5d61f3fac5", {"version": "a0acca63c9e39580f32a10945df231815f0fe554c074da96ba6564010ffbd2d8", "impliedFormat": 1}, "dd1b18f6ca25fe173d660c8e19b6b77808b8cbd9e3b1ef7090da894c287c1cfd", "75d9d8bfd0d1f91cbfbfe26ab229971b6bd07872257f9dc49f53edc364b47f0b", "344e6d4dd06e3b47931f6c3d187b4c1a80e523715ddbaec08c4ddbcf88597df7", "2ce4a38ca38314aa57e2cd6909b3e6155cd2491fb1bfe81b5efaa63d1b272a3a", {"version": "eae0f0bd272650a83a592c6000b7733520eb5aa42efcc8ab62d47dc1acb5ee78", "impliedFormat": 99}, {"version": "0f321818befa1f90aa797afdc64c6cf1652c133eca86d5dd6c99548a8bdaf51e", "impliedFormat": 99}, {"version": "481c19996de65c72ebf9d7e8f9952298072d4c30db6475cd4231df8e2f2d09b1", "impliedFormat": 99}, {"version": "406be199d4f2b0c74810de31b45fecb333d0c04f6275d6e9578067cced0f3b8c", "impliedFormat": 99}, {"version": "2401f5d61e82a35b49f8e89fe5e826682d82273714d86454b5d8ff74838efa7a", "impliedFormat": 99}, {"version": "87ba3ab05e8e23618cd376562d0680ddd0c00a29569ddddb053b9862ef73e159", "impliedFormat": 99}, {"version": "2b4276dde46aa2faf0dd86119999c76b81e6488cd6b0d0fcf9fb985769cd11c0", "impliedFormat": 99}, {"version": "88247402edb737af32da5c7f69ff80e66e831262065b7f0feb32ea8293260d22", "impliedFormat": 99}, {"version": "5ecea63968444d55f7c3cf677cbec9525db9229953b34f06be0386a24b0fffd2", "impliedFormat": 99}, {"version": "b50ee4bde16b52ecb08e2407dca49a5649b38e046e353485335aa024f6efb8ef", "impliedFormat": 99}, {"version": "a3d603c46b55d51493799241b8a456169d36301cc926ff72c75f5480e7eb25bf", "impliedFormat": 99}, {"version": "324869b470cb6aa2bc54e8fb057b90d972f90d24c7059c027869b2587efe01aa", "impliedFormat": 99}, {"version": "eedf3960076a5b33a84cd28476e035983b7c71a9a8728f904d8e17e824259a8e", "impliedFormat": 99}, {"version": "d7058b71aae678b2a276ecbeb7a9f0fdf4d57ccf0831f572686ba43be26b8ef7", "impliedFormat": 99}, {"version": "ed57d309b3d74719526912a9952a1ff72ca38fe0243c51701a49976c771cbb6c", "impliedFormat": 99}, {"version": "9e0b04a9586f6f7bcf2cd160a21630643957553fc49197e8e10d8cca2d163610", "impliedFormat": 99}, {"version": "2df4f080ac546741f1963d7b8a9cc74f739fbdedf8912c0bad34edeb99b64db6", "impliedFormat": 99}, {"version": "4b62ccc8a561ee6f6124dec319721c064456d5888a66a31a5f2691d33aa93a5f", "impliedFormat": 99}, {"version": "430fa8183f4a42a776af25dac202a5e254598ff5b46aa3016165570ea174b09e", "impliedFormat": 99}, {"version": "7cd3e62c5a8cc665104736a6b6d8b360d97ebc9926e2ed98ac23dca8232e210b", "impliedFormat": 99}, {"version": "ff434ea45f1fc18278b1fc25d3269ec58ce110e602ebafba629980543c3d6999", "impliedFormat": 99}, {"version": "d39e6644c8b9854b16e6810f6fc96c2bf044e2fd200da65a17e557c1bac51bc4", "impliedFormat": 99}, {"version": "cd6f4c96cb17765ebc8f0cc96637235385876f1141fa749fc145f29e0932fc2b", "impliedFormat": 99}, {"version": "45ea8224ec8fc3787615fc548677d6bf6d7cec4251f864a6c09fc86dbdb2cd5d", "impliedFormat": 99}, {"version": "3347361f2bf9befc42c807101f43f4d7ea4960294fb8d92a5dbf761d0ca38d71", "impliedFormat": 99}, {"version": "0bbc9eb3b65e320a97c4a1cc8ee5069b86048c4b3dd12ac974c7a1a6d8b6fb36", "impliedFormat": 99}, {"version": "68dc445224378e9b650c322f5753b371cccbeca078e5293cbc54374051d62734", "impliedFormat": 99}, {"version": "93340b1999275b433662eedd4b1195b22f2df3a8eb7e9d1321e5a06c5576417c", "impliedFormat": 99}, {"version": "cbcdb55ee4aafef7154e004b8bf3131550d92e1c2e905b037b87c427a9aa2a0f", "impliedFormat": 99}, {"version": "37fcf5a0823c2344a947d4c0e50cc63316156f1e6bc0f0c6749e099642d286b1", "impliedFormat": 99}, {"version": "2d2f9018356acf6234cd08669a94b67de89f4df559c65bf52c8c7e3d54eea16b", "impliedFormat": 99}, {"version": "1b50e65f1fbcf48850f91b0bc6ff8c61e6fa2e2e64dd2134a087c40fcfa84e28", "impliedFormat": 99}, {"version": "3736846e55c2a2291b0e4b8b0cb875d329b0b190367323f55a5ab58ee9c8406c", "impliedFormat": 99}, {"version": "f86c6ba182a8b3e2042a61b7e4740413ddca1b68ed72d95758355d53dac232d4", "impliedFormat": 99}, {"version": "33aab7e0f4bf0f7c016e98fb8ea1a05b367fedb2785025c7fa628d91f93818cc", "impliedFormat": 99}, {"version": "20cb0921e0f2580cb2878b4379eedab15a7013197a1126a3df34ea7838999039", "impliedFormat": 99}, {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "impliedFormat": 1}, {"version": "f816538db9388ac17bd354cf38d52da6c01d9a83f0589b3ff579af80cff0c8c6", "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "impliedFormat": 1}, {"version": "566007f48fa4cc7d29e4cb5cce9c315ccd52b72300d2d45ab0c639889e42d455", "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "impliedFormat": 1}, {"version": "096681898d7131c1183f164ccfec478d99a9efa3744a1b6617116bc6713ed7be", "impliedFormat": 1}, {"version": "2c9626288e967ebb03ec2bc27ea504f6f829b1686f65b86fd5074d53e0160d70", "impliedFormat": 1}, {"version": "4de35fb3800a92324c59c1d2ed28a4dc1284d507d27ef2eed680c2f9ebb32cd2", "impliedFormat": 1}, {"version": "4c3cccf01f76ca4292746b6dfebd6df4382eb7a05315724116feacecf952f106", "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "impliedFormat": 1}, {"version": "99953f3f1f9deae755b97ed3f43ce2bee2ae1324c21c1e5fa9285c0fe7b5077f", "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "impliedFormat": 1}, {"version": "504f509e23f2ab3a8018533925c034a340fbce4af9e77a1f71a8ddffbe0c19fa", "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "impliedFormat": 1}, {"version": "7f31b6e6d0c03a34e462fdaaf2f7ab6daf85bed51fcaa61ee794aaa1c9b890ac", "impliedFormat": 1}, {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "impliedFormat": 1}, {"version": "e72503cd93f937a8ef6731495d8ae662eb18ea4fbbb19b195dacc1123695f3a3", "impliedFormat": 1}, {"version": "7b1cf8c63616e8d09b6a36b13f849fb6aff36aa4d520a5e3393137cf00e5e15c", "impliedFormat": 1}, "13bf74f82a7e04b2acd31c081bc41e0f3ad228d69845c4d2ddac02860c38bb46", "9e02ef89dc3e4516e38b9a08ac093043a9d5af2b7efa87f24f2b621678ce287b", "e3b25e5f2787c139b1cc3ecaec90525d5bd34461c5c70e228a2fe09cd9306039", "ad342180657297d0625593cbe3044d283f0ae0c6dd2938fc44f48c70fc61f557", "c2b0b01fb26df87f861e1db8459d33e4fe41a287665d2bed5825e6a6375ec815", "db48a5ed938773179a8551fd382a320185ba46f9443f206693a5f4d0e9413728", "5beecc0181283bb42cc7134efa22e8383258072f8a5e446318d10a0033844f31", "0a1be8d071be09f0af8cb06c826668b565d01ff29a3cd0029c8f83ed21c3e87c", {"version": "8d2ae3253cd7dc86aa72fc402fc326d3cb9bebf35b35d5f066dac96bcebda483", "impliedFormat": 99}, {"version": "8959e66e910640d77ba2acc979c3ddcb2eb7f567d39ae96527b7751a6fdbb274", "impliedFormat": 99}, {"version": "367f3f82b900fd94db7f73952a0710f472ef97941c4e754fe9c9201899d886fd", "impliedFormat": 99}, {"version": "6bfe4ba1992264f5a1644d67965983f297f876b121062cace8482faca8eadda8", "impliedFormat": 99}, "23f349dfaeb2a1bdcdc64a2f6451f2fe8047b680a3856c7cf36253813b89a25c", {"version": "c8e857b6224783e90301f09988fb3c237fe24f4ebf04778d0cbe8147a26fffe7", "impliedFormat": 1}, {"version": "df33f22efcbdd885a1ea377b014e0c1dfbe2e42d184d85b26ea38db8ee7834c4", "impliedFormat": 1}, {"version": "f400febd2140549f95c47b2b9a45841c495dfeb51cc1639950fa307cd06a7213", "impliedFormat": 1}, {"version": "7048016c91c6203433420b9e16db56eec9c3f5d5a1301398e9907ac1fed63b58", "impliedFormat": 1}, {"version": "a4234645829a455706bf2d7b85642ee3c96bfe1cfddc9918e25bac9ce2062465", "impliedFormat": 1}, {"version": "9ff2d17592dec933b2b9e423fab8b8bc20feed486f16d35c75edd77c061de6e3", "impliedFormat": 1}, {"version": "fe9fc5b80b53a1982fe8fc0f14a002941b471213717536987d0cf4093a0c90a0", "impliedFormat": 1}, {"version": "4921f21de15ba1e7d1d5c83cf17466d30d4371bc9acf0c2c98015ebc646702ef", "impliedFormat": 1}, {"version": "f728f13a2965aacfb75807a27837509c2ab20a4bb7b0c9242e9b5ca2e5576d22", "impliedFormat": 1}, {"version": "c340ac804b0c549d62956f78a877dda3b150e79954be0673e1fc55f4a415f118", "impliedFormat": 1}, {"version": "2bfe95f5f0ea1a7928d7495c4f3df92cdc7b24872f50b4584e90350255181839", "impliedFormat": 1}, {"version": "9dfe677f6d3a486eebe1101b4cf6d4ec1c4f9ee24cc5b5391f27b1a519c926f7", "impliedFormat": 1}, {"version": "2766c9a60df883b515c418a938f3c8fd932241c89aba12aedf418e02a73017ce", "impliedFormat": 1}, {"version": "394967bc5f7707312a95cd7da0e5b30b736b7ab2f25817a8fea2d73b9398d102", "impliedFormat": 1}, {"version": "014a4afcc1674f40c7d77ca215e68bb3b0a254c2c925bcaa9932b6fb8f1ccd4e", "impliedFormat": 1}, {"version": "f816538db9388ac17bd354cf38d52da6c01d9a83f0589b3ff579af80cff0c8c6", "impliedFormat": 1}, {"version": "d2e0c04dce50f51b98ee32fd461dfa6e416a4b703c3d6d7e7fb7e68eca57a8de", "impliedFormat": 1}, {"version": "a8995e0a2eae0cdcd287dca4cf468ea640a270967ed32678d6fbf89e9f56d76d", "impliedFormat": 1}, {"version": "b151ad192b8e11b5ca8234d589abd2ae9c3fc229cdbe2651e9599f104fe5aa6b", "impliedFormat": 1}, {"version": "c37f352ab276b3cd4117f29e4cc70ed8ac911f3d63758ca45202a1a052fa9d00", "impliedFormat": 1}, {"version": "c97ffd10ec4e8d2ae3da391ca8a7ff71b745594588acc5d5bdef9c6da3e221bc", "impliedFormat": 1}, {"version": "74c373c562b48a0bde3ee68ac563403883b81cabe15c5ada4642a559cbd5d04e", "impliedFormat": 1}, {"version": "d42fe36f52e0ae09274753ed0fdedb32c42c2ad6ad247c81e6bd9982d1762004", "impliedFormat": 1}, {"version": "87f162804c7a5615d3ea9bdb2c828cd1d1f8378d5e2a9c3be1bd45c12f1fc1a5", "impliedFormat": 1}, {"version": "ccb92f285e2f3a3462262945fa59506aebe6ec569e9fec223d45d41c7c6cd447", "impliedFormat": 1}, {"version": "04e45000cf1381e6a84196aa01ca811ab192ca0a09debacc9e75dcfc6777bae1", "impliedFormat": 1}, {"version": "566007f48fa4cc7d29e4cb5cce9c315ccd52b72300d2d45ab0c639889e42d455", "impliedFormat": 1}, {"version": "4c2f8fb8a8f4afce6e05b9c554c012eb50147084933d78f7d218108740afd803", "impliedFormat": 1}, {"version": "6f72b3ebad0276cfcc7291fd2aefd1fbbd229487ec1acbbad03e798e8760e02e", "impliedFormat": 1}, {"version": "096681898d7131c1183f164ccfec478d99a9efa3744a1b6617116bc6713ed7be", "impliedFormat": 1}, {"version": "2c9626288e967ebb03ec2bc27ea504f6f829b1686f65b86fd5074d53e0160d70", "impliedFormat": 1}, {"version": "4de35fb3800a92324c59c1d2ed28a4dc1284d507d27ef2eed680c2f9ebb32cd2", "impliedFormat": 1}, {"version": "4c3cccf01f76ca4292746b6dfebd6df4382eb7a05315724116feacecf952f106", "impliedFormat": 1}, {"version": "492d1d21f79a8fa084e9dfd8fab89247301a49f1a0c12765b99c30a0ad8629ff", "impliedFormat": 1}, {"version": "69872cabf40dd4399939184cd7c5e47da62a9df811d3f56d193a437817a85b21", "impliedFormat": 1}, {"version": "19d00382e69115eeb1214d9b865030b61ec14f1bd5e91fb6e2b75acf5a6bef80", "impliedFormat": 1}, {"version": "3c3045d2661ef44458559f6bd48ebb47ccdfcbc513d859dc60c5e18e0544ac87", "impliedFormat": 1}, {"version": "e1de43a7fb0dda59dd9ed398fa306abdcb99da16b54edd3c7dc5e1a45d7e91df", "impliedFormat": 1}, {"version": "8301449ecbf03d5f893c298863fb66d97f1becb31f157276bdba7c708174a5be", "impliedFormat": 1}, {"version": "a556bdee2de2416a026022aeb260b5d684da34e322b5a95c7503143e51787b4f", "impliedFormat": 1}, {"version": "e8bc04f55c1b3da172412955b2785de54f2e1f2c9cb8949c0748ff143525310e", "impliedFormat": 1}, {"version": "683ad3639d8a96cfc782d672c44797d13c735ca9792d6c57e2fa5ada89e18e0c", "impliedFormat": 1}, {"version": "25b171a82c55909032e85448d89f8409e045a24a2b0458080bf304845b29b6ba", "impliedFormat": 1}, {"version": "ce25751e5374e1f13100276ecf2e2e8aac4d4c7229f762b3dc206639640954b8", "impliedFormat": 1}, {"version": "2f0a5a8ef5c6f5866d3caf04151422d05e64765ee250a7e9defc62908cfe73af", "impliedFormat": 1}, {"version": "79726fbe0854724f5bc3f16d4e40c0b320bbaa7a6296d1d782d70909f3b3a2eb", "impliedFormat": 1}, {"version": "6d391889910947acbe7d110271463ef74e7f65ae372d355756b1a6b0a987168d", "impliedFormat": 1}, {"version": "b3dadc705ad865a3acd5b40561ac0dcbce38fa28872ecb903eb586bd64cfa8b6", "impliedFormat": 1}, {"version": "8181adc6c7145eb6b2596249f3a2e1ff2fa7ebc604e73fe583f98c4b40916d6a", "impliedFormat": 1}, {"version": "dc84bb520982504eb30b09b870b32be8eccff2cd9beb963efd6a78971ae104b6", "impliedFormat": 1}, {"version": "bafdca74b47f54e116a9f2d589d39f1c677c777198b96a677a2d2f628b43c8f5", "impliedFormat": 1}, {"version": "9ccc168fc7cb696b5f60f216c72881db1f6c2d8e39eadd6c68130711f8eddd19", "impliedFormat": 1}, {"version": "6187a2dae6a9d910f272bfae324625437343f43a6ff48a28a5c5dd5e9cfc2d5f", "impliedFormat": 1}, {"version": "f063f87a44b1e92948bd5ef6db5b8cadef75218126e75ff02df83196e2b43c4b", "impliedFormat": 1}, {"version": "333df4996910e46b00aa9b7c8be938f6c5c99bfbf3a306596e56af9fff485acb", "impliedFormat": 1}, {"version": "deaf2e9bfb510a40e9413d5e940f96bf5a98a144b4e09a0e512efe12bfe10e9b", "impliedFormat": 1}, {"version": "de2395fb1d7aa90b75e52395ca02441e3a5ec66aa4283fb9ced22e05c8591159", "impliedFormat": 1}, {"version": "64be79c9e846ee074b3a6fb3becdbb7ac2b0386e1e1c680e43984ec8e2c2bbb9", "impliedFormat": 1}, {"version": "9c09e723f7747efc123e19f0ced5f3e144bbc3f40a6e1644a8c23437c4e3527f", "impliedFormat": 1}, {"version": "36fc129c8e3ad288656ea0e9ba0112728c7ec9507c75c6a3bce6d66f821a31d5", "impliedFormat": 1}, {"version": "3771470dde36546305e0431b0f107e2175d94e11f09b116611156f134364127e", "impliedFormat": 1}, {"version": "18c6715ca6b4304a314ff9adb864bd9266fc73813efd33d2992a7c6a8c6e7f73", "impliedFormat": 1}, {"version": "90cde8ac2173d2008c51996e52db2113e7a277718689f59cd3507f934ced2ac2", "impliedFormat": 1}, {"version": "69d01aac664fe15d1f3135885cd9652cca6d7d3591787124ae88c6264140f4b1", "impliedFormat": 1}, {"version": "55ab3dd3c8452b12f9097653247c83d49530b7ea5fe2cb9ef887434e366aee8c", "impliedFormat": 1}, {"version": "abd2ce77050bfd6da9017f3e4d7661e11f5dc1c5323b780587829c49fcac0d26", "impliedFormat": 1}, {"version": "d9dfcbbd2f1229ce6216cb36c23d106487a66f44d72e68fd9b6cb21186b360cd", "impliedFormat": 1}, {"version": "244abd05ca8a96a813bf46ddb76c46675427dd3a13434d06d55e477021a876ef", "impliedFormat": 1}, {"version": "5298f6656d93b1e49cf9c7828306b8aefc0aa39ac56c0a1226f1d4fba50a2019", "impliedFormat": 1}, {"version": "93268ed85b0177943983c9e62986795dcb4db5226732883e43c6008a24078d7f", "impliedFormat": 1}, {"version": "843fa59ad0b6b285865b336b2cbc71cdc471e0076a43d773d580cb8ba2d7030d", "impliedFormat": 1}, {"version": "aa2d452401748a5b296bf6c362b9788418b0ab09ee35f87a89ba6b3daa929872", "impliedFormat": 1}, {"version": "a4ef3c3f6f0aadacac6b21320d0d5d77236360e755183802e307afd38f1cbcc9", "impliedFormat": 1}, {"version": "853b1daed2861381ddda861a0450ce031c280d04caec035cc7433872643871c6", "impliedFormat": 1}, {"version": "1058ed9becf0c63ba0a5f56caaafbfd0bf79edf2159c2f2f2fe39a423ae548ae", "impliedFormat": 1}, {"version": "8b6eab9a4a523909ee1c698a10d332c544aa1fb363f482fe60f79c4d59ca2662", "impliedFormat": 1}, {"version": "f2b2c244b10a8e87192b8730ed5b413623bf9ea59f2bf7322545da5ae6eae54b", "impliedFormat": 1}, {"version": "92bbeada67d476b858679032b2c7b260b65dbccc42a27d0084953767d1a8cf46", "impliedFormat": 1}, {"version": "545afad55926e207ac8bdd9b44bb68f0bbffc5314e1f3889d4a9ad020ea10445", "impliedFormat": 1}, {"version": "4c8ef63125ed4d1eef8154ec9da0b6b7ca9effdf4fa5a53ab74a9d73c9754ff5", "impliedFormat": 1}, {"version": "e76a7e0b4f2f08e2bef00eacc036515b176020ab6b0313380dd7a5bd557a17f0", "impliedFormat": 1}, {"version": "fabd983e4148e2dce2a817c8c5cdbbc9cf7540445c2126a88f4bf9c3e29562b2", "impliedFormat": 1}, {"version": "a80c5c5bab0eb6cc1b3276ac276e5b618ead5de62ec8b0e419ea5259af0a9355", "impliedFormat": 1}, {"version": "d8cf5ded7dd2d5ce6c4e77f4e72e3e1d74bb953940a93d3291fb79158e1afc6e", "impliedFormat": 1}, {"version": "bdb10c13a7ababaae91932d0957ef01cd8a789979cd0b606a2106d198848b16c", "impliedFormat": 1}, {"version": "0fd3f9fed4dd35b1b07c18b4c3f612b7542c91835ad8a26e0e83d905709543dc", "impliedFormat": 1}, {"version": "441b5f5ac4619df9dbf436ecdb9f0bbaacf8696e6fdb2f81c6f5b1db76f5a1c0", "impliedFormat": 1}, {"version": "5d2284728400ee7b4fd1acd69e48d649d4056916cc70950a0000e5d70a32a750", "impliedFormat": 1}, {"version": "27ef186120f9e7ee90686aa7ad5163eb5c7f4cdeb19bb87850c4a5fe4b8e05e8", "impliedFormat": 1}, {"version": "4f1f9e056e0c9d23031367b4c7e7eedffb3e1ed58e64befc90749ca4dd9363ee", "impliedFormat": 1}, {"version": "2b0ccf76bcf10f61612135f951a74327ea0a2d5a80f397b767e0e0b08cdac265", "impliedFormat": 1}, {"version": "4e42e643f05a7fa69581a1a697a1cf967d9b2657dd9dd66e59d90500ec053ba0", "impliedFormat": 1}, {"version": "0ea8485dc0bb7d2a258a93b16305e17fb5be9f877a9df88de7023a9821c537ab", "impliedFormat": 1}, {"version": "5c221ba5333b775cef94d4a30076cc30730cceba649e9d30c5a7224a698c8825", "impliedFormat": 1}, {"version": "cb61ba4d5b5e39ecafe74ad7d88dc8e67defcffe15fb9216addee0fa06d5df38", "impliedFormat": 1}, {"version": "d83e8f0c10477fb4a7729a51aaad853cee81e0e332581dd2244da09e5526b5ff", "impliedFormat": 1}, {"version": "c8933a5b693306696e78315dca1fa57f6f5493fed44cd90aa2d4a4d354dd6516", "impliedFormat": 1}, {"version": "af8e2bf3df20cd2e6b8d744dd83499e174609d0c88864af3f30cd43671e719f5", "impliedFormat": 1}, {"version": "4186fd8b51535399c7ad1edc08f9c4ebb2a9e8e327b131cc1f950c5dfbb0c358", "impliedFormat": 1}, {"version": "b92965f503f55830702062f9e0832fabfbded49ff28728686a6fd84aa32f454d", "impliedFormat": 1}, {"version": "172dbc7933ff46ba3b2efe8b5c7828fd4f0d45c08755df8200213b6055d57f2e", "impliedFormat": 1}, {"version": "89e2ec7ed42725f89fa537c38f20144782bec6c5710e467a46a647647c8255cf", "impliedFormat": 1}, {"version": "5165882999957fa041e423a4fb64627dcb310bf50183af70a6ee8e10a584b0c3", "impliedFormat": 1}, {"version": "390997d64e1e5721fa807aa9e05c97086f58627170d9a7ed84b127126a3e5202", "impliedFormat": 1}, {"version": "00cf8ed9b47860a5f8cc0a65d7a41f85a7026f68162057728abc9249943a8629", "impliedFormat": 1}, {"version": "fc8b086c99f6d721eae8125a96833e0ba1762d00b80aad1d55c7a8b59d007466", "impliedFormat": 1}, {"version": "ff72c74ccdc5570c4a75a93e605a5586596444d96048d52c72f322da183c556d", "impliedFormat": 1}, {"version": "b8755448066177191edcd0b7e19e7fe44d69ed6dc97b16a420b7aa9070e2b850", "impliedFormat": 1}, {"version": "822a0c843f492ad2dc815080f24d4ddac4817a9df0de8cd35830e88fbbafbbe4", "impliedFormat": 1}, {"version": "467865324b9f66a1b8f68d9350c5aa0e749eec499e4863fe017b16ea8bcaccdf", "impliedFormat": 1}, {"version": "863bd77d5546877e19594759a901cc7b75da8d27336d4351e54413ec12032d09", "impliedFormat": 1}, {"version": "a17a62c94da321c0bf2315c35033e313daf1298a75aa43a01a4daf6937980c01", "impliedFormat": 1}, {"version": "851271a09d3c2db3eab80d64beb468d775a9818df06a826ba58925c900231ccb", "impliedFormat": 1}, {"version": "da2c95cd1f0f9cc19f3dd599b4c8fb0930eccb78a5c73f683e7ea98262d2f55e", "impliedFormat": 1}, {"version": "e40d3ca85fb1362763067506784635aa28863640cf7cf9be9e8c1c521c0fbbd5", "impliedFormat": 1}, {"version": "77a2f84e19aca9d03efdf0c484aba8daad3fd23c70b72e63aca78fadf71b448d", "impliedFormat": 1}, {"version": "00c5b6248c69e66729e5c4acb239db849b1497d7eb111fed3eba979432461ebf", "impliedFormat": 1}, {"version": "8e13abf75e9394f3a4b1d0b3f99468e15f4c7e2115153d2a1ca3c0de035bad1c", "impliedFormat": 1}, {"version": "07097dab1c068118806fecb8544aba3cca30965d0864b1998af1bee326a9990c", "impliedFormat": 1}, {"version": "c490ca6eb9149c28e4f2def6acb1bc058d160edb40fd249cf2a70c206a8cfecc", "impliedFormat": 1}, {"version": "7c9aab9a76abba65aa6389e41707d57ea0288dac9a8b6359465dcb462d2cfaa1", "impliedFormat": 1}, {"version": "97fbe30fd1b61b26f807ae1c78b681b0999af71cd9604c08a1d45e44690ca0c2", "impliedFormat": 1}, {"version": "ef91bf45a3d149db0b9e4e612ed1400c35f6a3d2a09669d1441add612d5f16b8", "impliedFormat": 1}, {"version": "dacebdc0353168f259724bccfd273b892e883baf36cf3dee21cf4178f3ef9ea0", "impliedFormat": 1}, {"version": "5416fb031a72377c3c17faa2041428a5f19f9d46a70b645dda6e3293fd0ca8ce", "impliedFormat": 1}, {"version": "95611472fd03e0992070caa3a5387133e76a079719994d237947f6bcf67f9bca", "impliedFormat": 1}, {"version": "6141d19bfa7698f362e84460856ace80a1eac3eab1956b188427988f4cd8e750", "impliedFormat": 1}, {"version": "1acded787e1fc09fd56c004d3ba5b719916c06b61976338a92a2f04ec05cba5c", "impliedFormat": 1}, {"version": "8fb0d41cd90f47b9148e4a474fb03484d9af1735871321a2f57f456e40a7e319", "impliedFormat": 1}, {"version": "a25cd4cf54bcdd109dd46274e2369fc1cad6d74350b5642441d2b9eef515c3bf", "impliedFormat": 1}, {"version": "af4b9f16e50a0ae803745150e4c091e86ab95f3dac649286af28505258f7a189", "impliedFormat": 1}, {"version": "3d209a6c3c53366b3bcb72dcf04a7ceda57362cae6ac47dbb783321934a0c5ad", "impliedFormat": 1}, {"version": "4766770027d93a5ad1d4cc880cce405b4c6f67c64303ab34b347d6428eb783f2", "impliedFormat": 1}, {"version": "43d2bec085f0fab54d7b9dfa3f5c5ce65e30da6a19d82ed37d1d41867682f86e", "impliedFormat": 1}, {"version": "e5efb9781a0ef18d60cbb8afa261489efd260d87642c095cacba0b09b2684fcf", "impliedFormat": 1}, {"version": "775ca7538a2f9bc674ebe5f3cb8aa8fa346ef4c1faec4c5b13b4784a744854dc", "impliedFormat": 1}, {"version": "c0037c7c6fb8031f7047a1ccdb381762862b48429e9ab07bac8fc35fc5b5dd14", "impliedFormat": 1}, {"version": "af4db63c6e4d55df1ad7f3dabdde31bc30555debf1cd6b79ea65a36c52bf199c", "impliedFormat": 1}, {"version": "d291ffc234a58061b8192f74422f2e51fb87f6d10e82c30a555bccf9641b3e38", "impliedFormat": 1}, {"version": "6d683695e9765b29165bb0823f88755211d48949f0b95a9a4236802afddf41e1", "impliedFormat": 1}, {"version": "8fcd568ba937d867544cd8e726f35a515690ad041387fdebc93d820c8720e08c", "impliedFormat": 1}, {"version": "81a0ff507ece65e130c1dd870ba79b8337c1fd345db7b154a2749282c994d2d5", "impliedFormat": 1}, {"version": "64e2ffc72047548fa3c04095abb9dab48e2eaac169161fd2ed3564dea0c67e57", "impliedFormat": 1}, {"version": "b525d2fc6b694512a877219ebba25d5fa244f99253a5bbe6c6421f8d71b1c806", "impliedFormat": 1}, {"version": "d695f0d65f5fba0e275cf7801399575c272b86e7bf8e70133f8fc03517305b1d", "impliedFormat": 1}, {"version": "0836f15e5e7dcad64fd50d49a39267da34371d1c2b803b38dffcfabcd2ff604e", "impliedFormat": 1}, {"version": "56eff313f885482d44e4aa7cefdd55f7d0d92a91c1ddf9cd73c533abc36f4dff", "impliedFormat": 1}, {"version": "022ff6b725f6ab95b1c4d229893b3047002a9c1fab6798c8fe63797ec1e63dc5", "impliedFormat": 1}, {"version": "5e64d04301aa6ae6bf0f3435d07804889342873ab2875a16c827db9e6543002d", "impliedFormat": 1}, {"version": "0b8c3effe0c65129d493be140da1a83eb61a1e83481d441dd2bc359a926b453e", "impliedFormat": 1}, {"version": "0816c977ef73d99cbb134427a83f91ca6f7fe00eb7544118320d613a85da6879", "impliedFormat": 1}, {"version": "068db2994f5926e888462b0852ada2c24f2cb50028f034f475407957ca51c6cd", "impliedFormat": 1}, {"version": "59106b469557319ad26f40f054861be3fd2cf09911c3b66df280b9340a1d9caf", "impliedFormat": 1}, {"version": "69e8e2dc21b0636f671485867555439facd68ee9e234fc9190c3b42e7f1a74e9", "impliedFormat": 1}, {"version": "5fb0c0cae187f6554769cd4ff36575ddbc43078a4fdf9b17a5c0c25dfa9a9f2b", "impliedFormat": 1}, {"version": "918d99a7aa4b7f5edf2cdcb33c163837a892f43b9e22c10634d61d0a28fc09a2", "impliedFormat": 1}, {"version": "097b0d1e237bfcc97411fcae19a484a717fd4055a48e98ade5cc28b26afd21f6", "impliedFormat": 1}, {"version": "5fb0eef64cb75951f7ae2dc6a704aa0567a25a39a616a5dd10ba7cfbfcf73b78", "impliedFormat": 1}, {"version": "0a649cbc59a47f224d0494a6d5167a803ed049f995ade8423c7cb62bb6a38b64", "impliedFormat": 1}, {"version": "68e25d1a79523b18fae630ca57100ce2dff6c5023376a2f57e9d0d07e1b9b8ef", "impliedFormat": 1}, {"version": "1a505f408bc7d484553b7701f712dc52e1174648baff7d6c9c1f38b5cb83b772", "impliedFormat": 1}, {"version": "b19badf31df455f10cf44fda9f6a0e0b42d6e970ac122b66c5da5d683fa270d4", "impliedFormat": 1}, {"version": "71b6fe5c85eb877c3e3ed2f142b95a69f97905c34f11fc6d9052a4317e7f6bae", "impliedFormat": 1}, {"version": "bd55536c0f989f59af6ca66cbc8121485f978f4e07c3df1688623c5f898058c6", "impliedFormat": 1}, {"version": "dcb868c613ccd06b1a3ff56ee235e5987820c0c8bbd77fedc9af4dcfdd4c54bf", "impliedFormat": 1}, {"version": "f3d1b3cd130e3cd67fe8e06256deb5d678243c6976ea498c81a48e542efb7529", "impliedFormat": 1}, {"version": "772b881836efbdceb7ae8d3ae038f14ec83444397d8429b866312dcd78714dde", "impliedFormat": 1}, {"version": "314d516eb3bf1eda07e898935edcbd1e74739493c8ad444e82181f8a020eef2c", "impliedFormat": 1}, {"version": "8cfced8e57c64563f91e90a76a6df2d8f934c90a425327a9ed5393bc88c27d97", "impliedFormat": 1}, {"version": "67bd754a8775c81794c9fc84b1a1e9fca44a402fa7d93fcdad4ba2d37737d929", "impliedFormat": 1}, {"version": "5128e32c57068eb09d5189eb68681ca7d0e5e4b0cdedecbef9c67689f0970876", "impliedFormat": 1}, {"version": "7fcdedd29146e5a2a6c86eda652f8485a1eeda1b8646825bbf729023f6ea6013", "impliedFormat": 1}, {"version": "86b9b361ce8ea1d9f04e15bbe49e5ac72e5f97d8cfa8592930d32f267729a201", "impliedFormat": 1}, {"version": "671f5e3a931c2737f8dfa43b34c4a320eca27fc6584ecef890ddd7374cee5cb7", "impliedFormat": 1}, {"version": "ff213315eebd3ff05e01b383f704d79d8139aad5cb0d6a13c082f2e29625adbc", "impliedFormat": 1}, {"version": "83ed351a10ef17b7811d3c06fc2775e36b6911278326d55da8d1eef8ff2f29df", "impliedFormat": 1}, {"version": "2f5f146f1d6c04cf89ae0e9b4cf2b064b2ce4319ba6a5bf18ab8fb29db1cfd1a", "impliedFormat": 1}, {"version": "7fc2b96a8465725bf774bd490c383edd5ee3dfe0d38c13551d082cae2de4041e", "impliedFormat": 1}, {"version": "9eaeb6696e4218cb5bded9ee27c3e95589ad4af1fd4b97ccdca43eadd62c94d5", "impliedFormat": 1}, {"version": "fd580a99cb9bb84288da00eea67dce300bdef06d4da2a727c0fc466d2922dca2", "impliedFormat": 1}, {"version": "b82809d4468b6ba4d72437adaab7ca273547c59974e954c48f655a4b1bdca429", "impliedFormat": 1}, {"version": "c6455d4ed4f7337bcb885c61372c7d9b03991995ed73e29023bad502d1336f0a", "impliedFormat": 1}, {"version": "b5e6f0491b5a2002eb9b1146165cf915ee58e0fddf7f2adb5f2aa4bc44b4fb83", "impliedFormat": 1}, {"version": "f534aef095a62fb82f57768fc52995d3e58d95e0a1671b0256a4704802aee818", "impliedFormat": 1}, {"version": "cdc6f1d471882782cdac7442dbdad65aede5f749c09799a84918bd916eb6d6db", "impliedFormat": 1}, {"version": "2475197472c609662f09660e3964a86aa355cea0e671653656800690bb508b7c", "impliedFormat": 1}, {"version": "b4067760d0447747d82b6848b640168d656d0b916c3add2ec94c3c4dea92fc9f", "impliedFormat": 1}, {"version": "c6c591a17f9c0c2821baf15f775f5c7d6dd4a0786365ee9c182d7a97e38ad96a", "impliedFormat": 1}, {"version": "ede44ddf9d274a859e9f1f34333d5f0e8cf2167c3265f81d5280d37b872b4552", "impliedFormat": 1}, {"version": "6317aba53c9152998bb1f8bd593f55730084d05c00c774ff72a3aa4d687a6dbb", "impliedFormat": 1}, {"version": "26f1bd15980b19d925be98afde3918a6a181435b87e9b7c70d15726ecbfff0e5", "impliedFormat": 1}, {"version": "57af4faf6847adff5048f82929b9a7d44619d482f571534539ae96a59bb29d3a", "impliedFormat": 1}, {"version": "874770f851ac64a93aaddfb86a2f901f158711911fee14a98a67fe32533ee48b", "impliedFormat": 1}, {"version": "3d933e519ad9cc8cf811124f50d0bc14223cdea9f17adf155f11d190ceb2a6c8", "impliedFormat": 1}, {"version": "d5dfce61a7bf994d2cb711af824efa4de9afa5854d34e6725b9c69d925b6b2dc", "impliedFormat": 1}, {"version": "f77d1e10417bf43f8fa5d18916935f342d4d443e371206ede7239faaf9abbbb8", "impliedFormat": 1}, {"version": "c94e0b8815b72ba924c6b8aa666b25903d949a7ab0d38ed84e4bf65da3d06a3b", "impliedFormat": 1}, {"version": "15db84e660fdcd8468f23973ab83c31d7fd28bdddb30b0aed16cfa051aafe900", "impliedFormat": 1}, {"version": "7c01cbfe181c0e10044831b899de6c2eec4fba32de1f1cca12742d2333c1345b", "impliedFormat": 1}, {"version": "62cb1636513ef26d3ea83fb5d2369cf8569d04aa30d8fd7f5327d0e10841635d", "impliedFormat": 1}, {"version": "8282a076b07dc3dc6b2265377627ab3860cb6a1bcbae85a5a4006dec4c9f0066", "impliedFormat": 1}, {"version": "b273c241dd08c6276fd35be413c64508ae50f847fa052bf7781799b51da8e9e9", "impliedFormat": 1}, {"version": "3bc0bbef6d7fb63002fe80167db350b9677cfce5872c0cc7ecec42ba8248ded6", "impliedFormat": 1}, {"version": "4880c6a85442934b81f3b1a92cb6b43df36f8c1b56b6822eb8cbc8c10c438462", "impliedFormat": 1}, {"version": "1bfdd8c1710a3d1654746ca17f512f4a162968a28e1be1a3a1fdd2a8e5bf385f", "impliedFormat": 1}, {"version": "5405aedafdf272dde53b89036199aaed20d81ddc5ec4bea0cb1ab40232fff3fe", "impliedFormat": 1}, {"version": "db2ee45168db78cc83a4368546e0959318374d7256cbd5fa5692a430d5830a59", "impliedFormat": 1}, {"version": "49993b0eaa14d6db6c334ef0e8b1440c06fee2a21ffd4dea64178880bd3d45a2", "impliedFormat": 1}, {"version": "fb9d9dc0a51cb4014d0e5d5f230ec06ffc4eb6caae6eecfe82ea672b7f3c6967", "impliedFormat": 1}, {"version": "84f44079a0793547d3a629feb8f37d8ef6d07cb5bb5fdeefd887f89e9be871f6", "impliedFormat": 1}, {"version": "295c5ec088a1bfc286e8dbdc9807958588979988cd7a74ad32be774a6f6ea512", "impliedFormat": 1}, {"version": "f15129c62ed04410ac0a3326ae6fa5ef7229bbb1b0cbfa252b5c558505a38253", "impliedFormat": 1}, {"version": "4bf500d9a554d43cb9133d60f1b3f58ca98b0f794486d1377f3effc551b40faf", "impliedFormat": 1}, {"version": "536f6a9208c89eb8f0a5eeda629175b0fa62ccd22e387af7f35297fa2af6897d", "impliedFormat": 1}, {"version": "8c95fe5a655ea1c78f0335f8da58e70d98e72fe915987c3b61c6df49d6e276d1", "impliedFormat": 1}, {"version": "4bd434d3055d1b4588f9d7522d44c43611341de7227db9718a700703c608e822", "impliedFormat": 1}, {"version": "935507b695f420fddff2d41ddc12ff3935931a3f26d6aa65afbb276bfdf51cb4", "impliedFormat": 1}, {"version": "e851c14c9dbe365592f5084c76d4b801e2f80302f82cebbe7c2b86095b3ae08a", "impliedFormat": 1}, {"version": "b5c90d931d285d9d1c4cb92d71f2719e28caaa9ca062072d0bb3b69300b436c2", "impliedFormat": 1}, {"version": "40b3e953e9ea51a86a1e5b60a2355eeb780f2f8ce895ece252910d3e0a033a16", "impliedFormat": 1}, {"version": "0264b432aace8398f174e819a0fc4dc196d5aed49ae65aae071fc2ec8e6dc029", "impliedFormat": 1}, {"version": "3b29bb23855a1924264c3a30b5c73b00c52a57c2ffb5f91c48c9572e71048f19", "impliedFormat": 1}, {"version": "8b9b2e76db07d8926bcc432c9bdfb38af390568951b39fe122d8251b954f9ed2", "impliedFormat": 1}, {"version": "96e85c6fa102741a25418ab2c8f740c994e27ea86fd6518a17ec01a84b64dd5c", "impliedFormat": 1}, {"version": "9525b28a4fa959c8d8c7d6815f842f78c67b40def9160afdced5c9daf14cd4a8", "impliedFormat": 1}, {"version": "0e59a6944a52f52138315b6658fb1d217fa017b7abec12006c491d51e07fb56d", "impliedFormat": 1}, {"version": "cfa8acfeb9d68702aa6249b7295ca73ea598e441f014cd4184b6e2a3ea9a275c", "impliedFormat": 1}, {"version": "21b0c616f61cd6699135a34a500f7df30022abf9358ba612f10668ea3c988e00", "impliedFormat": 1}, {"version": "9ad1d0b171f7bb9f484ad156e97f0d8e760a5fee13e342831669c7b2d1137a30", "impliedFormat": 1}, {"version": "7ccadd4ba126bb2c0564bfb85ddd7d084aa5f2880cc2d0149fbe183fd5ceb6d1", "impliedFormat": 1}, {"version": "ebbde5a8a356a1547ac6ecdfba7547036a5ada116011cb96634c32df1cf69084", "impliedFormat": 1}, {"version": "e703eded767e3a944ac1f7c58c201a0821da1d68c88d6ba94bb985a347c53e42", "impliedFormat": 1}, {"version": "99953f3f1f9deae755b97ed3f43ce2bee2ae1324c21c1e5fa9285c0fe7b5077f", "impliedFormat": 1}, {"version": "2afd452bfa6ebaacbead1ca5d8ab6eda3064d1ea7df60f2f8a2e8e69b40259e9", "impliedFormat": 1}, {"version": "dae0f3382477d65621b86a085bdb0caabf49e6980e9f50ee1506b7466c4d678d", "impliedFormat": 1}, {"version": "e5793b3f4cbd73c841790264db591d3abe9bd09128302a2901fedd2353ab24d5", "impliedFormat": 1}, {"version": "41ed74193a13f64a53705a83e243235920fd58d4b115b4a9f5d122362cda7662", "impliedFormat": 1}, {"version": "478e31b207faa7110b04f6a406240f26b06243eb2d2cff3234c3fc8dd075bf6c", "impliedFormat": 1}, {"version": "ea917cdbfb87d11cd2f8b03e357b22b1114d0ba39ce4ce52b1a4f0dc10c6c20a", "impliedFormat": 1}, {"version": "3ef0c5634d9aabee346f9ba056c1c5d977f2e811f6d13c082614c9062cd4b624", "impliedFormat": 1}, {"version": "1ddb49c7f8fc4b9e4da2d5ddca91b4e2763fe7d17aa79940bd60406f3e2739bd", "impliedFormat": 1}, {"version": "d5b01eab562dc40986a5ceb908519dc7f02a7ded2bcb74318317a75714dbc54c", "impliedFormat": 1}, {"version": "b19ef44e991aa150a19a9f84be1fd1c4d86496241300fd904216762246700623", "impliedFormat": 1}, {"version": "87df6cf2565a88dae3ec50e403e9ef6b434ad3e34d922fe11924299018b38e58", "impliedFormat": 1}, {"version": "9d999d30b52fb0b916f7a64c468f6d5c7a994e0c1ef74d363562e9bda3cb8b99", "impliedFormat": 1}, {"version": "9b1b05f88ded21046391276ff60d2d987bf160d77b40399e07b7bdbfe2e38b31", "impliedFormat": 1}, {"version": "628177f7eb0314f0189e4d90f663233606b3936ce391c7f98da46105ae402c65", "impliedFormat": 1}, {"version": "3c80bf6873eb3b95cd590aab8eb1612f0f7cef6a30b3f49535844f7cecd99351", "impliedFormat": 1}, {"version": "da367ede4ebd5ff4cb1cf9e6bc8eb35848b23c57c22c53360e53dc772c7be8f9", "impliedFormat": 1}, {"version": "4337acbd8896efb7e7d8d6e0eca78607fc7c1a9ad2bb228240f13f97b3492f1f", "impliedFormat": 1}, {"version": "505c7800f8195961302dee715870b7212bdfb667e5e47de76447151dd35a40f1", "impliedFormat": 1}, {"version": "cf5a3eed6cd493d198b0c1eacf70486d8bd527fc411d57660caf2c93b5ea0fb6", "impliedFormat": 1}, {"version": "900e344adae3c65076c9ba4ee1a77c6db19fb0c7e54d7ce23c28ff8d272cba26", "impliedFormat": 1}, {"version": "bcc5186a38d1eecf60b2c4d1e3eb9abd8ab91cb492f384a9d2ed7bcda2abd0d5", "impliedFormat": 1}, {"version": "0ec1b41954fea9def7d9d87e0f3beea2ba3ec5b7beb769f308cfe32ad2968669", "impliedFormat": 1}, {"version": "51189c085256f11da13b22792f1d7c928f8a8e9d9b6c7b38e956e72a51ef8219", "impliedFormat": 1}, {"version": "504f509e23f2ab3a8018533925c034a340fbce4af9e77a1f71a8ddffbe0c19fa", "impliedFormat": 1}, {"version": "635c049483e13e1dc8bee72dde300c40d350046cff59b202d41a12ec8c733d27", "impliedFormat": 1}, {"version": "7fd8d5f70ea745e1a0338de7aaacd9bd6ff086ce6de75dcf91749c77d1e23831", "impliedFormat": 1}, {"version": "78d2a7795bfd2be490937e8b01968a0acca8a6bdf5933570bc013806049d4175", "impliedFormat": 1}, {"version": "db49833b6e9aa54b535076f40615349a7465005367a787b50ba7b92421e26442", "impliedFormat": 1}, {"version": "6a936fc917de40c44ca81331ee7d7a71dc30ae1895871e7be7b6ed85d96cc41f", "impliedFormat": 1}, {"version": "bdd2a764cf87c4ab1efd7084597d1ca4ba17f6b6496553095ecca5a14b5d4278", "impliedFormat": 1}, {"version": "ddef8e6676fd572ee3de174ad28df05c7b3803542d7318482b8f98779ff25612", "impliedFormat": 1}, {"version": "34eae3bc7f5bfb515d2ec163ccd4b63fdb73ad7f66564707686d84f42a8b7c35", "impliedFormat": 1}, {"version": "d240d106cf9bc3c0efdb323d807b944ce16ac5d837ecef5b75f1e66d606b2a72", "impliedFormat": 1}, {"version": "639d5a26be297431e0bcc9f71f969fd7d84319fc03b5e1c672ea10fb0094c616", "impliedFormat": 1}, {"version": "770c3e6367c2802c027c0b1f86928f288e11ad77ac2f454d7f682460eab30a0c", "impliedFormat": 1}, {"version": "c9dd2760e0419a059cf733c38ef5d44eeca3fc647f9c201d88656e5040f5a3a7", "impliedFormat": 1}, {"version": "16766b8f3d1bba66ac8167e6407be6c490d4462e802f67c140b1174869db5b67", "impliedFormat": 1}, {"version": "f9267391788ac81ca54dfae32c5d86e99a19abaee9b172b2f8d98a3c2b578a2f", "impliedFormat": 1}, {"version": "92441638c0fa88072ef9f7b296a30e806bac70219ce2736ef33c8941259d9b70", "impliedFormat": 1}, {"version": "8774efbaf39f9ea3a0ff5b1c662c224babee5abb3d754796278e30eb2e51ae3c", "impliedFormat": 1}, {"version": "e634b47a7d3f9468572a7c9af1fe2f52687ee1afb23ba5568205a7a4c55662ef", "impliedFormat": 1}, {"version": "1cbef47ee169c717a1ef7ea91b15582c61ac721fd5f5671de95c3df9f026db9a", "impliedFormat": 1}, {"version": "0db0ee49f803c9b901dfe06be9c8fb6a1c05f98664ca34c68e0da575eae76f2b", "impliedFormat": 1}, {"version": "4b745fcadf040899979b6b26e24aca6d2fa2bbe52a919d67f717bfe0339354a3", "impliedFormat": 1}, {"version": "bc57f3550b3fd3b7d31b9a278d0b491dd45d170e37c4046a3105fdea9ebe5f89", "impliedFormat": 1}, {"version": "b5f7093d62a228669dd56edd0bcb86a0cf0b46db4816a3967b4632503c21b93c", "impliedFormat": 1}, {"version": "4d70bbb1f35f345b2c2e1b5c9b8174d5397bba76ffef12656bca16ce9a1830d3", "impliedFormat": 1}, {"version": "a004fc80aa8f78dfb1d47b0e098fe646e759311c276b6b27404f5e356528f22d", "impliedFormat": 1}, {"version": "c8933d9afe6c5ee7ecbeec5aa01f6b37d3c2be2f7dd203ee75ee4850164007cb", "impliedFormat": 1}, {"version": "b1129b38f1eea70951ece3ccd1cc3e1d094379b64d3958ba8ce55b0ec0083434", "impliedFormat": 1}, {"version": "b2bb10f992cfd1cf831eb005311a80f7f28bc14cfac5883f17e75f758d1354e1", "impliedFormat": 1}, {"version": "58b621b924324874a67e92d7626809fd4b72fc079ce909f6da7097654026af00", "impliedFormat": 1}, {"version": "149288ae23bb3b31ffe5cfb7eea669fc6872e41901d60be932af2581601fc70f", "impliedFormat": 1}, {"version": "01a0fd262c8fdc6c91078255c4fe2f8602fd4fe4c753b2eae88537585b21dddf", "impliedFormat": 1}, {"version": "deb69e6754a61784daadc35b318544b0aa69048ebfb142073c62b7f46bb1d5d0", "impliedFormat": 1}, {"version": "60eef77c9b5cec20516907628f849845975a8137773ddb0bcb53fc2ea7d28870", "impliedFormat": 1}, {"version": "67bcdcbd8cece34ae28180c636908af1b118fa9603d0d4b7dea877156d4de519", "impliedFormat": 1}, {"version": "5a1c2cee26d1f8d9bb15b334f5b2df7de27a3944bff9ccf71d3b69c588612bda", "impliedFormat": 1}, {"version": "a04d60b205af1f28461f3d2f5a8222ec2d8af54d436bc53a0460756e07e4207d", "impliedFormat": 1}, {"version": "14c85d4debb2e0c8939f81b85cb9ab4543f70c8fe53be5fb5caf1192677c8ca4", "impliedFormat": 1}, {"version": "c507cdc9757c048620ff08a85b9cf6278598eb1738d729fdbfa1e387a35e639a", "impliedFormat": 1}, {"version": "4a4807c3096f49a463476742e3b5d23ccf0e087e43c017891c332ae5b8ad667d", "impliedFormat": 1}, {"version": "c611af558c5d19fa477f1b03ceac7b0ae28fe5ad4f8bc61e8ad64c46f97e86e2", "impliedFormat": 1}, {"version": "0cec41f583efa1f1033a4d546d926ee949756f19040bb65807c5a3ab6f3b8449", "impliedFormat": 1}, {"version": "73b1eda15491d4f3052d6fac202190e76d6453fce832034bd29901cb198448b9", "impliedFormat": 1}, {"version": "08c66989383183f3d7c43346617c8f466bef28f1e3eb4da829316d548cdbdf80", "impliedFormat": 1}, {"version": "1f283476bbeaa589fe644fe6ba9da223baf118ecd4756863deae7362b246aff3", "impliedFormat": 1}, {"version": "0a8f91ace4d1803eb2a50079c9e233fb262b0027d19aa250eb7ecbf6319e52d6", "impliedFormat": 1}, {"version": "65bab52912be03b374ab591d73ee40aff3a465ac20bc0f2024b4c80ac5ce8397", "impliedFormat": 1}, {"version": "6a647bf0620a4a7777527c688c62636a503e8b4d5e680037503066dd2af6d0dd", "impliedFormat": 1}, {"version": "f1466e4d708815280c849956a506e132b7dc243907b9c8e07d52862e32dfcd91", "impliedFormat": 1}, {"version": "cb4b99f8e47f57df841c95fcb1afc28488a2b5442e3524f6261e611b86105331", "impliedFormat": 1}, {"version": "7c5fc61fc40a9f3aa3a09fd867536ff94a93b16f4ae99f1fb748fae6e13ae8bc", "impliedFormat": 1}, {"version": "473d9ca5b242db0471d418336f410922eadd290679914f37ef21ee26dbeee2b4", "impliedFormat": 1}, {"version": "2ffeb6ad0b074d1cfa3dc9671dad062b08129d1e8a8988b727dd2ce9fd4298d8", "impliedFormat": 1}, {"version": "fa1d4332a68d84300895af592811f65f5f1d725ed0664f17d5c215a63408b6b4", "impliedFormat": 1}, {"version": "7a09768c36d8b7d8e44b6085031712559362b28a54f133b803bed19408676cdf", "impliedFormat": 1}, {"version": "f0b807278b2619fbe0acb9833bd285acabbf31da3592da949f4668a2e4bcbcf0", "impliedFormat": 1}, {"version": "bc6419ca69c35169941d9d0f7a15c483a82ac601c3448257f29a1123bc2399e1", "impliedFormat": 1}, {"version": "45f530610645ca6e25621ce8e7b3cf6c28cd5988871bc68b3772488bd8e45c25", "impliedFormat": 1}, {"version": "2d3e715ca71765b491ae8bd76257e8ccfe97201c605dadc4e6532bb62e4f6eee", "impliedFormat": 1}, {"version": "c519419c11e61347181ba3b77e8d560d8cc7614b6231cacefe206b41474792d4", "impliedFormat": 1}, {"version": "24823640771cf82865c3b1cb48a8a88119b69e56aef594171cc0570f35f60b8a", "impliedFormat": 1}, {"version": "30398045bda704d03d23e78a37095aa56e69ab2dd8bb7304b15df9e183b9800a", "impliedFormat": 1}, {"version": "9a816fe54ea736ecf02b6865c10157724cdb5ba3f57ead02d9216b2dd4bd0d5f", "impliedFormat": 1}, {"version": "a67582f2933f5b6faebba3484c99e78b529aa016369b768021726e400c93ddb8", "impliedFormat": 1}, {"version": "96cd7367cc076d36d9f10cbe34b91e94467caf9b64a7a0fe1c4f6c8287e0a1b5", "impliedFormat": 1}, {"version": "17c7be2c601e4b7e6292932997e491ff874418bef9ee6137e69ea6ef497e0e5d", "impliedFormat": 1}, {"version": "eb7ed3b69718cf40c1ab8ce9a0e917819e0ef0b7480ba2890cddbb94a1386b10", "impliedFormat": 1}, {"version": "7a7cec0720ee6d20e08fa9def697b149a94db1763bbec6e1ab5da8d7726ebddc", "impliedFormat": 1}, {"version": "c024677c477a9dd20e7aba894c2f3e6ef81c4076af932a7fc00c210543cd53bc", "impliedFormat": 1}, {"version": "7f31b6e6d0c03a34e462fdaaf2f7ab6daf85bed51fcaa61ee794aaa1c9b890ac", "impliedFormat": 1}, "b97101ab28c0c8e31117b04422c7440d7a35e6efb0e85bb5a704b15e348448e1", "374626198fa64f0992e7d5dc38d4586a9252cd84af26b89d64e908331c30531a", {"version": "b0d94232734d3a5645abbe92eae439d175109c87776c8c7226eca901bd92bf7f", "impliedFormat": 1}, {"version": "6d3896c27703f487989e187cac09082a2c0db44cfc6667856af2960597eb017a", "impliedFormat": 1}, {"version": "8e559bf8c5bac4a5d7df79deb18760f4fde9c0e5b8719deb362ff005b496ca66", "impliedFormat": 1}, {"version": "3fc652b87dd28a02a60edad4a68d1ffea4edd9538d1cfb586661c3ca51fde49d", "impliedFormat": 1}, "2ed19ab154472bbe55716f89a53c03efbf070cc51ab62da1da4c128b5553d24b", "e0de43943ff859f32dc1bb734a678dc007269f58ce74b0d221a5402233a0f15f", "62c1dea25a1e86e3fc0eef6e9cdb97a22752126875fc42f78cfedb168b01dcb2", "43044845d1539c04bbdd8cd695ab49bf7219553f96ce29098b8cc690477d8197", "47facecd19a41998e839000bc8f6a9aa3c980f7b29f4dcb7890b126c901d522d", "d6669c2e766380f7669f5ea269ee852ed6d94b08886960d6087fca6ff8a26da0", "9d40151377f7ac71353e44ff4f19b4d16907d6b9fe4e8cc9fd9cebb4e99f478d", "f35dc3a321696da2f0fc7e8798e8648db12717ec02e81c4d4c03db62943f5434", "095cb7f7e2a016f2be1bda543d0444d1f30ef81997efcf6a72aa3a1d2aa1dd60", {"version": "50585e6aecee4e903109eb423731d632b0ede60d6619dfce8f8c85e748743684", "impliedFormat": 99}, {"version": "ce22a5344d55865982a77d6388a952339bf12229487dc5520e3b4742f0c38e77", "impliedFormat": 99}, {"version": "2c70a1945560b056df69579b882fc0bfd17b3883ecad1d42def8f1045750ad87", "impliedFormat": 99}, {"version": "b7dbc555bb4b8bdedadbcafe44ffeb95bcddee0690df208aa12de90cb7d61ae0", "impliedFormat": 99}, {"version": "711848e5381230753956c04163fb48642566bdab45a4fa0b185ed2cb5547469d", "impliedFormat": 99}, {"version": "d2a32b1c9e3cfbceb0107710704602ea3003d2b27cd337fd22009dc838e02413", "impliedFormat": 99}, {"version": "24d1e5df3991bdbd57f9fb28ecd812d75111c0936ff1ebd5745780fbdf9476d5", "impliedFormat": 99}, {"version": "f8950e45e7ecd995228300925f97361e9eda95051838da237f2943c0ff6249d6", "impliedFormat": 99}, {"version": "111f32c5f5312e3d23ded8553803438ddb08a03d6ce4487c87988b58aa6928a3", "impliedFormat": 99}, {"version": "395f4afd053339c013d0fdbea2f395fc9b941493c37ad3e36fa3edde92d9e06c", "impliedFormat": 99}, {"version": "194d779446ee6695dfde84b1128a5f25651c368fb30441a26dc865b69d629b43", "impliedFormat": 99}, {"version": "2b0fac9ec2bef8cb832a82b6c827e827099913779f94b5124ebac051ce63c75e", "impliedFormat": 99}, {"version": "75fe380cfe6f7e4e9bfaf1e5296e40015cc8d1f24b741476a01d7ad2be03c912", "impliedFormat": 99}, {"version": "8a51b23adf34c05ecb161be43eb02e773e439eed0d35a9524aadb63776b0fc88", "impliedFormat": 99}, {"version": "ff0289a765e3941b98ddbbf52df87aaa69446a27ffea4efbcedd25b9db0b3257", "impliedFormat": 99}, {"version": "8b2ff2738bbbcec301caae6caf15b90e3bc69189b9539acf5bde0bbb3261e057", "impliedFormat": 99}, {"version": "af51cdc4aac8d3d3ef578d092edb86ff7a240a50ae4dd0b843667fb7a23363e6", "impliedFormat": 99}, {"version": "91fe39810e6370b7858faee456b54efdadd94d17a8326b1a083c3cd83317fc41", "impliedFormat": 99}, {"version": "ffc5a293c41d0a34041673337b47fae8d2efdf05da554d312d804ba8409fbd5e", "impliedFormat": 99}, {"version": "41d05f925a2e26c4fb6abd3ea69946f723331e1c2454749c452cf6ba2c5b4383", "impliedFormat": 99}, {"version": "de8f37e67941d4d946375cbcf81c1f160c47e27a0f320d403fe322fef0458e9e", "impliedFormat": 99}, {"version": "21c9dd0dd9301bdd86c3b56889971803ace4c4b263b4de7361db0abe5e3bfcc2", "impliedFormat": 99}, {"version": "0f33756fe6cfabac9a7554c9044b0a2e7eaace182048c36fe2dbb5f33818d0f1", "impliedFormat": 99}, {"version": "fd0816b2efe3cb8c2bb07b62f373ec32a12d17a9bd26d861398600574d1a533c", "impliedFormat": 99}, {"version": "5ed69293ea0a31f5a9ab5e3f2e0e0f4eeba9fa9320fbaad9be4a2fdfd6527718", "impliedFormat": 99}, {"version": "c9d433d2bd63f22107d3d5f70d255a9240cde0d25c7df5096685126930d560f6", "impliedFormat": 99}, {"version": "8cd9311fe355a70cff7add1ab8073fab757d903cc3ac36c7e89bea7da375f6bd", "impliedFormat": 99}, {"version": "405d7ab019ef6081661c574712a23461e84e3c8c9e55dbb706bf6d624ada6683", "impliedFormat": 99}, {"version": "09e9d3f5ccdb9b6074e4046860f9effc64d80247bbb4bd3e5a87dcb21b766983", "impliedFormat": 99}, {"version": "29f6c5c11ae67c7fa3821616a2dc1cbfad0a9db99d79f4690f844cb372775c4c", "impliedFormat": 99}, {"version": "6f0786ef52beecf487be30aebe2817a5659c1ddc5f378212b6e2261e2d2290a7", "impliedFormat": 99}, "f69a9cca1908a8b3a752bac23d75a5dcdaa708a7294737e66ed87e0477b0a131", "ba9aa8e7b0a070e3722ee34b33e8e2d9f119ccd16018857523ebbacac9c2e300", "a6aec962e095914ece33a2655746238b9ffb3df49fcbc215675a5e591db65280", "a71ab4bfc9ee39397eafc4650ab64d6a4fbfa0e850f5495f7641d222a94c1843", "d2a8b9ef2e6732a9f34dc76c773e9dc25abbce4aaf916c2adfd7b28d934438ee", "614fe8727862f4a573095ca38fd69ac039baa1c3dd8faaf03dc3dd9efa4405d4", "1f09fca0cea35fc924b96e6de7009b3266375502c950ff0092bf6d00212b279c", "51a43dce15069a89d28d94b0f3003538cab54644ccab7c715aa39859a6824f0c", "454b6437a16a5f87c7b7963b1878bca65db5991ddb22dae226672cc39be3bed8", {"version": "19705540f34a8e3d26843636d4b0c39ae680523f82535f2124c826a2f0a5b989", "impliedFormat": 1}, "2346c44f964ce506fe356f0578108e32ad7e6ac66273b54fea41a568ad9cecca", "521b0ff2171751bc39c356772be0cade767605c8173a9735e4840e39e1dca603", "65f07631efeff14ad73d928d7b55112ca26716b62ca0767a09e3043285316fc4", "48616fde15e04146e0f4a038b8c82a5251f5dd03cd84f8d4c29e46cce9c3aaac", "6c1730f469bd3db03f76e1ac7521e8a9456462ecaa1fcb021a718bfe0590606d", "ee5ed03918b3a2fff9b4d19b63460d14ff816c69c33844f96c99456567336263", "f5799c0df4ccd5f52be26522394472dfec0f4e0ed3a1d4965896a545be905b7e", "5e3f1259ebddccae645e3a1f5abb9f6ed5147e27c533f18c640dc3ecb113a710", "605c93e4128cfc0e5ec2bd4c23ce6f7b5794fa3ed2bbc83e5a02d7a2be6bf1b0", "59bc04ecae68c7f09c4ce80c25106a46974527d24f9fe9f91ab707be11d6f573", "37280388384b3de792ef831e8c59b7aeb5f0e2056552b2b64ff9eab8e609c908", "12ae85c58748fa35d05597b6bce87af5b9a02e9be6a9df35ea61154b8ae191f0", "a071daf5c40f7358df01b32cd1d5900fa3167a77d19d7a997f4c5b4ca5961a47", "289d0459e1ea892b36b30c12d741bf1c19b41039319cdc441be8d7b7ed3bfc27", "b653afe2f1eb3a4c67f98c45a8ad539516e4fbc2ea79f81d5512b38068f44d43", "a06db0c2e2c3d0bb4844e0edd6c5537beaa72069fffe655f25407c59af906a5f", "82dcb20470f2875253e407b3639afceaccfad42b1657cc0d7c06b86a7dee5648", "cf32ff5d7a318c84f3d4ff25f94c9e8081aaeec79a416535c0e4932239c8f3ea", "d06358a3c7fd7f23f35cb73f1671d030b118b07f10c8c89580430a997ae17d24", "18398e7b2c2915228d30ebd1028137ed1de66817c4eee31a305c9ac48cadc97f", "08fa468e28fadbfade57e95ad3d1114d89ca2caad643271e8553d39a3b633054", "c06f7b44f6e6c422a5caba14ff4d76309b99fc59db4c601bfa7c04e4cd5f565a", "ab3da11dc25fda0200e294a604851e07e341481a3082ee6c4d5aa2b19f4892c9", "9f21a652243f55593a03eeb5d80ef5013b980e573e225add1588b5dae36abeff", "03f7bb0a93951e7d00d6ecae3f9edf92e5c722e4c121c25b979cd6a95f999fac", "cb10eb597575c5f0cfaade910f86c1a93288fb5c08a13257d073415b0ce85c17", "3ed686ee319f7cd7b3ee769870bdd740865f976ef2ca699a24f9d09bb423b3c9", "93e07728a9d6c11d8a19cab1f479f3bba52319e74d3107b12ca0410a8fc6b5a5", "0af1d6e0faaea08674fe7b9b1394ff00eb0950eb0550d7a6818287bcbc93034c", "3eb3d3ccbff093bef57a15d655db98f0e01d622bd11c8bd55eee623ebed8dd2b", "351c55033f7a09c76c5d1753539e596c80adf83bd8f2d7c878688c418aa3ee0b", "3d4404f529f31db6215330925ff002e518160e65c3ced97cabad139877b3d9e6", "f88cea2ef52d804cf11ad53af40d70827e5ecb54d66fda5bf816e5da7683ecdd", "8281b6261e49204568ee39a555dfae7cdc5f48270a29f69f20bee86bbb6a7427", "3b72f8a157fc241e38e0ba2ca8915c717730db76d43c47d1899cc682db506d23", "71e447a60e9d335f61db334f32922ca843849c40eae240e1f75e2799f898eb27", "1a93e76af09aaa02f691c8aadea0b8a5a0a3b5d570b60b8b6a435fc8f41591d4", "18c005f963b93d7ccbf0108c2080502f6a4a8479996e181b4d6100398304d9ec", "abfe428b974867b7bcb6c8b387f91d61945c06fb2611890fbdb9cecf65877fc0", "6e09e6521f2ad92bf431fc3b16ab1eddac716f7fae9e6207342f95893d4e8924", "a6dfa318b51b5fd7967235b2c5c8e37602e872c4a47543711cab8d871368c1b6", "b6775192667a5a3967a1023550a013bbf543e885ac5a097179cc5f5868a6846c", "5b6802c209dbd09796346c56ecfe46a28e4a76a679f991323cb4ef546c438312", "c2f5d7031a6afe94c2552736ba0c610c112abfe9274170cd445758ff9ada48f4", "8a5bfb4e51321694051e5a8bc4a70d8722d89fb823dbc9dd24598f0b8f6deb32", "83d1f6a20343bb966257b999ec2e9adb900e7200743bb052d4e04a97287dd29d", "ca4cc1644de6d98f390815d41d5c2623a682b1879aa69c778341a466fe93a2a1", "81cbe5919afa67af102103688df75f96f8fd0cd779c7fa8f2dfea662315d7692", "74da53a0566dbc8a91182e07a12f3339e09f1c2794f847370f3476f629cf2dc2", "9a2aa7b7867d11c13b5145f59ad2b9e67d00f51be9ce417870bd7ce081c39283", "5a0b130c9fcaeea61c4f36d297adf6bcb269fdeca8ee4f4da330c2260f17c7c9", "911d287351f23311700f0a81459c7981cac0d7bab43658f3edfebc26261d22d4", "80b1eb0cca7268b13cb15f2c7060a1ba85134665694c47154b0ae2622f11ee5a", "70a0d20c56fc0e8eedcc9eb3346c840ad3920a03b7adac242ecd9df517875b60", "c102579d8b0669085a53bb25d9b0c8d6ae6cc4d936ae14c18094f4586b4887cd", "047ce9e2581700e10e9a016f35ff2ae4b52a905f7124457eac6f251cee68645e", "98d9f0cd0cd3a23c27f3a830e5e2e497e51bd267b6c500d8ac06fb0dca76f41a", "8153f72944a26af047dc498ad37180d141c0c7a5224a7f1b664d6dd417ac2e5b", {"version": "03b596fb8f308ead692151f5d82c5942945b0e52760c02e52ec6c75907519555", "impliedFormat": 1}, "c0d24990a3627cd1893a6c23efc8b11d30db4a8a4d113ededa908e88c0f450ef", "895873c9ae785c5c2a0cbb504a4febe86adb022da31b83ef5ea17e9fd5dee780", "fc888b450cea66051c9acd239f5de1ae5951da5b64f4701344f46e0dad278504", "91a1b027953aeabfe5b573d6b44d2f14ebba3c48c9ddb76bd3b621b7f5bbf2ae", {"version": "c0d2d022442c4cccb967548865e1cb1a024eceb46c65416bba9c67756c2cd861", "impliedFormat": 1}, "02b94a3e7a180b30765ba0043a9cc8d57e89cc0519e7ce6a2c8719e511250c5b"], "root": [92, 93, 104, [106, 109], 139, [141, 148], [176, 182], [440, 468], 552, 553, 555, 561, [685, 691], 693, [695, 712], [717, 732], [774, 777], [802, 804], 809, 811, [818, 822], [837, 858], [1586, 1632], [1634, 1637], [2317, 2324], 2329, 2655, 2656, [2661, 2669], [2701, 2709], [2711, 2768], [2770, 2773], 2775], "options": {"allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "composite": true, "esModuleInterop": true, "jsx": 4, "module": 99, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "skipLibCheck": true, "strict": true, "target": 7, "useDefineForClassFields": true}, "referencedMap": [[1645, 1], [1647, 2], [1648, 3], [1649, 4], [1644, 5], [1646, 5], [1640, 6], [1641, 6], [1642, 7], [1652, 8], [1653, 6], [1654, 6], [1655, 9], [1656, 6], [1657, 6], [1658, 6], [1659, 6], [1660, 6], [1651, 6], [1661, 10], [1662, 8], [1663, 11], [1664, 11], [1665, 6], [1666, 12], [1667, 6], [1668, 13], [1669, 6], [1670, 6], [1672, 6], [1643, 5], [1673, 14], [1671, 15], [1650, 16], [1638, 15], [1639, 17], [1999, 15], [2000, 15], [2001, 15], [2002, 15], [2004, 15], [2003, 15], [2005, 15], [2011, 15], [2006, 15], [2008, 15], [2007, 15], [2009, 15], [2010, 15], [2012, 15], [2013, 15], [2015, 15], [2014, 15], [2016, 15], [2017, 15], [2018, 15], [2019, 15], [2021, 15], [2020, 15], [2022, 15], [2023, 15], [2025, 15], [2024, 15], [2026, 15], [2027, 15], [2028, 15], [2029, 15], [2030, 15], [2031, 15], [2032, 15], [2033, 15], [2034, 15], [2035, 15], [2036, 15], [2037, 15], [2043, 15], [2038, 15], [2040, 15], [2039, 15], [2041, 15], [2042, 15], [2044, 15], [2045, 15], [2046, 15], [2047, 15], [2048, 15], [2049, 15], [2050, 15], [2051, 15], [2052, 15], [2053, 15], [2054, 15], [2055, 15], [2056, 15], [2057, 15], [2058, 15], [2059, 15], [2060, 15], [2061, 15], [2062, 15], [2063, 15], [2064, 15], [2065, 15], [2066, 15], [2067, 15], [2068, 15], [2071, 15], [2069, 15], [2070, 15], [2072, 15], [2074, 15], [2073, 15], [2075, 15], [2078, 15], [2076, 15], [2077, 15], [2079, 15], [2080, 15], [2081, 15], [2082, 15], [2083, 15], [2084, 15], [2085, 15], [2086, 15], [2087, 15], [2088, 15], [2089, 15], [2090, 15], [2092, 15], [2091, 15], [2093, 15], [2095, 15], [2094, 15], [2096, 15], [2098, 15], [2097, 15], [2099, 15], [2100, 15], [2101, 15], [2102, 15], [2103, 15], [2104, 15], [2105, 15], [2106, 15], [2107, 15], [2108, 15], [2109, 15], [2110, 15], [2111, 15], [2112, 15], [2113, 15], [2114, 15], [2116, 15], [2115, 15], [2117, 15], [2118, 15], [2119, 15], [2120, 15], [2121, 15], [2123, 15], [2122, 15], [2124, 15], [2125, 15], [2126, 15], [2127, 15], [2128, 15], [2129, 15], [2130, 15], [2132, 15], [2131, 15], [2133, 15], [2134, 15], [2135, 15], [2136, 15], [2137, 15], [2138, 15], [2139, 15], [2140, 15], [2141, 15], [2142, 15], [2143, 15], [2144, 15], [2145, 15], [2146, 15], [2147, 15], [2148, 15], [2149, 15], [2150, 15], [2151, 15], [2152, 15], [2153, 15], [2154, 15], [2159, 15], [2155, 15], [2156, 15], [2157, 15], [2158, 15], [2160, 15], [2161, 15], [2162, 15], [2164, 15], [2163, 15], [2165, 15], [2166, 15], [2167, 15], [2168, 15], [2170, 15], [2169, 15], [2171, 15], [2172, 15], [2173, 15], [2174, 15], [2175, 15], [2176, 15], [2177, 15], [2181, 15], [2178, 15], [2179, 15], [2180, 15], [2182, 15], [2183, 15], [2184, 15], [2186, 15], [2185, 15], [2187, 15], [2188, 15], [2189, 15], [2190, 15], [2191, 15], [2192, 15], [2193, 15], [2194, 15], [2195, 15], [2196, 15], [2197, 15], [2198, 15], [2200, 15], [2199, 15], [2201, 15], [2202, 15], [2204, 15], [2203, 15], [2205, 15], [2206, 15], [2207, 15], [2208, 15], [2209, 15], [2210, 15], [2212, 15], [2211, 15], [2213, 15], [2214, 15], [2215, 15], [2216, 15], [2219, 15], [2217, 15], [2218, 15], [2221, 15], [2220, 15], [2222, 15], [2223, 15], [2224, 15], [2225, 15], [2226, 15], [2227, 15], [2228, 15], [2229, 15], [2230, 15], [2231, 15], [2232, 15], [2233, 15], [2234, 15], [2235, 15], [2237, 15], [2236, 15], [2238, 15], [2239, 15], [2240, 15], [2242, 15], [2241, 15], [2243, 15], [2244, 15], [2246, 15], [2245, 15], [2247, 15], [2248, 15], [2249, 15], [2250, 15], [2251, 15], [2252, 15], [2253, 15], [2254, 15], [2255, 15], [2256, 15], [2257, 15], [2258, 15], [2259, 15], [2260, 15], [2261, 15], [2262, 15], [2263, 15], [2264, 15], [2265, 15], [2267, 15], [2266, 15], [2268, 15], [2269, 15], [2270, 15], [2271, 15], [2272, 15], [2274, 15], [2273, 15], [2275, 15], [2276, 15], [2277, 15], [2278, 15], [2279, 15], [2280, 15], [2281, 15], [2282, 15], [2283, 15], [2284, 15], [2285, 15], [2286, 15], [2287, 15], [2288, 15], [2289, 15], [2290, 15], [2291, 15], [2292, 15], [2293, 15], [2294, 15], [2295, 15], [2296, 15], [2297, 15], [2298, 15], [2301, 15], [2299, 15], [2300, 15], [2302, 15], [2303, 15], [2305, 15], [2304, 15], [2306, 15], [2307, 15], [2308, 15], [2309, 15], [2310, 15], [2312, 15], [2311, 15], [2313, 15], [2314, 15], [2315, 18], [1674, 15], [1675, 15], [1676, 15], [1677, 15], [1679, 15], [1678, 15], [1680, 15], [1686, 15], [1681, 15], [1683, 15], [1682, 15], [1684, 15], [1685, 15], [1687, 15], [1688, 15], [1691, 15], [1689, 15], [1690, 15], [1692, 15], [1693, 15], [1694, 15], [1695, 15], [1697, 15], [1696, 15], [1698, 15], [1699, 15], [1702, 15], [1700, 15], [1701, 15], [1703, 15], [1704, 15], [1705, 15], [1706, 15], [1707, 15], [1708, 15], [1709, 15], [1710, 15], [1711, 15], [1712, 15], [1713, 15], [1714, 15], [1715, 15], [1716, 15], [1717, 15], [1718, 15], [1724, 15], [1719, 15], [1721, 15], [1720, 15], [1722, 15], [1723, 15], [1725, 15], [1726, 15], [1727, 15], [1728, 15], [1729, 15], [1730, 15], [1731, 15], [1732, 15], [1733, 15], [1734, 15], [1735, 15], [1736, 15], [1737, 15], [1738, 15], [1739, 15], [1740, 15], [1741, 15], [1742, 15], [1743, 15], [1744, 15], [1745, 15], [1746, 15], [1747, 15], [1748, 15], [1749, 15], [1752, 15], [1750, 15], [1751, 15], [1753, 15], [1755, 15], [1754, 15], [1756, 15], [1759, 15], [1757, 15], [1758, 15], [1760, 15], [1761, 15], [1762, 15], [1763, 15], [1764, 15], [1765, 15], [1766, 15], [1767, 15], [1768, 15], [1769, 15], [1770, 15], [1771, 15], [1773, 15], [1772, 15], [1774, 15], [1776, 15], [1775, 15], [1777, 15], [1779, 15], [1778, 15], [1780, 15], [1781, 15], [1782, 15], [1783, 15], [1784, 15], [1785, 15], [1786, 15], [1787, 15], [1788, 15], [1789, 15], [1790, 15], [1791, 15], [1792, 15], [1793, 15], [1794, 15], [1795, 15], [1797, 15], [1796, 15], [1798, 15], [1799, 15], [1800, 15], [1801, 15], [1802, 15], [1804, 15], [1803, 15], [1805, 15], [1806, 15], [1807, 15], [1808, 15], [1809, 15], [1810, 15], [1811, 15], [1813, 15], [1812, 15], [1814, 15], [1815, 15], [1816, 15], [1817, 15], [1818, 15], [1819, 15], [1820, 15], [1821, 15], [1822, 15], [1823, 15], [1824, 15], [1825, 15], [1826, 15], [1827, 15], [1828, 15], [1829, 15], [1830, 15], [1831, 15], [1832, 15], [1833, 15], [1834, 15], [1835, 15], [1840, 15], [1836, 15], [1837, 15], [1838, 15], [1839, 15], [1841, 15], [1842, 15], [1843, 15], [1845, 15], [1844, 15], [1846, 15], [1847, 15], [1848, 15], [1849, 15], [1851, 15], [1850, 15], [1852, 15], [1853, 15], [1854, 15], [1855, 15], [1856, 15], [1857, 15], [1858, 15], [1862, 15], [1859, 15], [1860, 15], [1861, 15], [1863, 15], [1864, 15], [1865, 15], [1867, 15], [1866, 15], [1868, 15], [1869, 15], [1870, 15], [1871, 15], [1872, 15], [1873, 15], [1874, 15], [1875, 15], [1876, 15], [1877, 15], [1878, 15], [1879, 15], [1881, 15], [1880, 15], [1882, 15], [1883, 15], [1885, 15], [1884, 15], [1886, 15], [1887, 15], [1888, 15], [1889, 15], [1890, 15], [1891, 15], [1893, 15], [1892, 15], [1894, 15], [1895, 15], [1896, 15], [1897, 15], [1900, 15], [1898, 15], [1899, 15], [1902, 15], [1901, 15], [1903, 15], [1904, 15], [1905, 15], [1907, 15], [1906, 15], [1908, 15], [1909, 15], [1910, 15], [1911, 15], [1912, 15], [1913, 15], [1914, 15], [1915, 15], [1916, 15], [1917, 15], [1919, 15], [1918, 15], [1920, 15], [1921, 15], [1922, 15], [1924, 15], [1923, 15], [1925, 15], [1926, 15], [1928, 15], [1927, 15], [1929, 15], [1931, 15], [1930, 15], [1932, 15], [1933, 15], [1934, 15], [1935, 15], [1936, 15], [1937, 15], [1938, 15], [1939, 15], [1940, 15], [1941, 15], [1942, 15], [1943, 15], [1944, 15], [1945, 15], [1946, 15], [1947, 15], [1948, 15], [1950, 15], [1949, 15], [1951, 15], [1952, 15], [1953, 15], [1954, 15], [1955, 15], [1957, 15], [1956, 15], [1958, 15], [1959, 15], [1960, 15], [1961, 15], [1962, 15], [1963, 15], [1964, 15], [1965, 15], [1966, 15], [1967, 15], [1968, 15], [1969, 15], [1970, 15], [1971, 15], [1972, 15], [1973, 15], [1974, 15], [1975, 15], [1976, 15], [1977, 15], [1978, 15], [1979, 15], [1980, 15], [1981, 15], [1984, 15], [1982, 15], [1983, 15], [1985, 15], [1986, 15], [1988, 15], [1987, 15], [1989, 15], [1990, 15], [1991, 15], [1992, 15], [1993, 15], [1995, 15], [1994, 15], [1996, 15], [1997, 15], [1998, 19], [2330, 15], [2331, 15], [2332, 15], [2333, 15], [2335, 15], [2334, 15], [2336, 15], [2342, 15], [2337, 15], [2339, 15], [2338, 15], [2340, 15], [2341, 15], [2343, 15], [2344, 15], [2347, 15], [2345, 15], [2346, 15], [2348, 15], [2349, 15], [2350, 15], [2351, 15], [2353, 15], [2352, 15], [2354, 15], [2355, 15], [2358, 15], [2356, 15], [2357, 15], [2359, 15], [2360, 15], [2361, 15], [2362, 15], [2363, 15], [2364, 15], [2365, 15], [2366, 15], [2367, 15], [2368, 15], [2369, 15], [2370, 15], [2371, 15], [2372, 15], [2373, 15], [2374, 15], [2380, 15], [2375, 15], [2377, 15], [2376, 15], [2378, 15], [2379, 15], [2381, 15], [2382, 15], [2383, 15], [2384, 15], [2385, 15], [2386, 15], [2387, 15], [2388, 15], [2389, 15], [2390, 15], [2391, 15], [2392, 15], [2393, 15], [2394, 15], [2395, 15], [2396, 15], [2397, 15], [2398, 15], [2399, 15], [2400, 15], [2401, 15], [2402, 15], [2403, 15], [2404, 15], [2405, 15], [2408, 15], [2406, 15], [2407, 15], [2409, 15], [2411, 15], [2410, 15], [2412, 15], [2415, 15], [2413, 15], [2414, 15], [2416, 15], [2417, 15], [2418, 15], [2419, 15], [2420, 15], [2421, 15], [2422, 15], [2423, 15], [2424, 15], [2425, 15], [2426, 15], [2427, 15], [2429, 15], [2428, 15], [2430, 15], [2432, 15], [2431, 15], [2433, 15], [2435, 15], [2434, 15], [2436, 15], [2437, 15], [2438, 15], [2439, 15], [2440, 15], [2441, 15], [2442, 15], [2443, 15], [2444, 15], [2445, 15], [2446, 15], [2447, 15], [2448, 15], [2449, 15], [2450, 15], [2451, 15], [2453, 15], [2452, 15], [2454, 15], [2455, 15], [2456, 15], [2457, 15], [2458, 15], [2460, 15], [2459, 15], [2461, 15], [2462, 15], [2463, 15], [2464, 15], [2465, 15], [2466, 15], [2467, 15], [2469, 15], [2468, 15], [2470, 15], [2471, 15], [2472, 15], [2473, 15], [2474, 15], [2475, 15], [2476, 15], [2477, 15], [2478, 15], [2479, 15], [2480, 15], [2481, 15], [2482, 15], [2483, 15], [2484, 15], [2485, 15], [2486, 15], [2487, 15], [2488, 15], [2489, 15], [2490, 15], [2491, 15], [2496, 15], [2492, 15], [2493, 15], [2494, 15], [2495, 15], [2497, 15], [2498, 15], [2499, 15], [2501, 15], [2500, 15], [2502, 15], [2503, 15], [2504, 15], [2505, 15], [2507, 15], [2506, 15], [2508, 15], [2509, 15], [2510, 15], [2511, 15], [2512, 15], [2513, 15], [2514, 15], [2518, 15], [2515, 15], [2516, 15], [2517, 15], [2519, 15], [2520, 15], [2521, 15], [2523, 15], [2522, 15], [2524, 15], [2525, 15], [2526, 15], [2527, 15], [2528, 15], [2529, 15], [2530, 15], [2531, 15], [2532, 15], [2533, 15], [2534, 15], [2535, 15], [2537, 15], [2536, 15], [2538, 15], [2539, 15], [2541, 15], [2540, 15], [2542, 15], [2543, 15], [2544, 15], [2545, 15], [2546, 15], [2547, 15], [2549, 15], [2548, 15], [2550, 15], [2551, 15], [2552, 15], [2553, 15], [2556, 15], [2554, 15], [2555, 15], [2558, 15], [2557, 15], [2559, 15], [2560, 15], [2561, 15], [2563, 15], [2562, 15], [2564, 15], [2565, 15], [2566, 15], [2567, 15], [2568, 15], [2569, 15], [2570, 15], [2571, 15], [2572, 15], [2573, 15], [2575, 15], [2574, 15], [2576, 15], [2577, 15], [2578, 15], [2580, 15], [2579, 15], [2581, 15], [2582, 15], [2584, 15], [2583, 15], [2585, 15], [2587, 15], [2586, 15], [2588, 15], [2589, 15], [2590, 15], [2591, 15], [2592, 15], [2593, 15], [2594, 15], [2595, 15], [2596, 15], [2597, 15], [2598, 15], [2599, 15], [2600, 15], [2601, 15], [2602, 15], [2603, 15], [2604, 15], [2606, 15], [2605, 15], [2607, 15], [2608, 15], [2609, 15], [2610, 15], [2611, 15], [2613, 15], [2612, 15], [2614, 15], [2615, 15], [2616, 15], [2617, 15], [2618, 15], [2619, 15], [2620, 15], [2621, 15], [2622, 15], [2623, 15], [2624, 15], [2625, 15], [2626, 15], [2627, 15], [2628, 15], [2629, 15], [2630, 15], [2631, 15], [2632, 15], [2633, 15], [2634, 15], [2635, 15], [2636, 15], [2637, 15], [2640, 15], [2638, 15], [2639, 15], [2641, 15], [2642, 15], [2644, 15], [2643, 15], [2645, 15], [2646, 15], [2647, 15], [2648, 15], [2649, 15], [2651, 15], [2650, 15], [2652, 15], [2653, 15], [2654, 20], [1549, 5], [736, 5], [734, 5], [735, 21], [737, 22], [739, 23], [743, 5], [738, 5], [742, 5], [740, 5], [741, 5], [744, 24], [733, 5], [758, 25], [760, 26], [761, 26], [762, 25], [759, 25], [769, 25], [770, 25], [765, 27], [768, 25], [766, 25], [767, 25], [772, 28], [771, 29], [764, 30], [763, 30], [755, 25], [757, 31], [756, 25], [773, 32], [747, 33], [746, 5], [750, 34], [751, 35], [748, 5], [754, 36], [753, 37], [752, 26], [745, 26], [749, 38], [692, 15], [1511, 39], [1533, 5], [1534, 5], [1443, 40], [1433, 15], [1481, 41], [1513, 42], [1531, 5], [1139, 43], [1504, 41], [1475, 39], [1451, 44], [1505, 45], [1411, 46], [1515, 39], [1502, 43], [1428, 39], [1520, 47], [1427, 41], [1510, 43], [1437, 41], [1457, 48], [1410, 49], [1486, 50], [1430, 39], [1529, 39], [1473, 51], [1438, 40], [1419, 40], [1416, 40], [1509, 52], [1483, 42], [1478, 48], [1458, 53], [1446, 54], [1540, 42], [1506, 39], [1439, 40], [1453, 55], [1454, 42], [1455, 42], [1432, 56], [1417, 41], [1456, 43], [1465, 57], [1539, 15], [1418, 43], [1484, 58], [1459, 48], [1517, 43], [1408, 40], [1440, 40], [1429, 40], [1538, 43], [1522, 48], [1494, 43], [1487, 43], [1541, 43], [1490, 59], [1492, 60], [1493, 43], [1488, 43], [1452, 41], [1495, 42], [1523, 48], [1441, 40], [1435, 39], [1420, 41], [1535, 15], [1436, 39], [1496, 43], [1445, 40], [1527, 39], [1412, 43], [1530, 61], [1460, 62], [1409, 49], [1537, 39], [1536, 39], [1503, 47], [1500, 43], [1426, 41], [1501, 43], [1141, 43], [1140, 43], [1528, 50], [1514, 43], [1526, 48], [1518, 40], [1521, 43], [1434, 41], [1516, 39], [1485, 63], [1512, 64], [1519, 43], [1466, 15], [1468, 65], [1431, 43], [1413, 66], [1415, 67], [1461, 48], [1442, 40], [1425, 68], [1482, 48], [1444, 50], [1463, 69], [1525, 70], [1542, 55], [1543, 71], [1532, 61], [1497, 55], [1499, 43], [1498, 5], [1477, 48], [1470, 5], [1480, 41], [1471, 48], [1476, 15], [1469, 61], [1508, 72], [1414, 73], [1479, 48], [1464, 55], [1507, 5], [1130, 15], [1585, 74], [1489, 61], [1491, 55], [1524, 55], [1132, 48], [1582, 75], [1551, 76], [1583, 77], [1550, 47], [1131, 78], [1137, 62], [1134, 15], [1136, 15], [1547, 79], [1135, 80], [1138, 41], [1544, 48], [1548, 79], [1584, 81], [1545, 48], [1546, 82], [1133, 5], [1119, 83], [1120, 84], [1123, 85], [1121, 86], [1117, 87], [1122, 88], [1116, 89], [1118, 90], [1128, 91], [1124, 92], [1126, 93], [1127, 94], [1129, 95], [154, 96], [153, 97], [116, 98], [112, 99], [119, 100], [114, 101], [115, 5], [117, 98], [113, 101], [110, 5], [118, 101], [111, 5], [132, 102], [137, 15], [130, 102], [131, 103], [138, 104], [129, 105], [122, 105], [120, 106], [136, 107], [133, 106], [135, 105], [134, 106], [128, 106], [127, 105], [121, 105], [123, 108], [125, 105], [126, 105], [124, 105], [489, 5], [472, 109], [490, 110], [471, 5], [519, 111], [520, 112], [518, 113], [521, 114], [522, 115], [523, 116], [524, 117], [525, 118], [526, 119], [527, 120], [528, 121], [529, 122], [530, 123], [625, 124], [626, 124], [627, 125], [585, 126], [628, 127], [629, 128], [630, 129], [580, 5], [583, 130], [581, 5], [582, 5], [631, 131], [632, 132], [633, 133], [634, 134], [635, 135], [636, 136], [637, 136], [639, 5], [638, 137], [640, 138], [641, 139], [642, 140], [624, 141], [584, 5], [643, 142], [644, 143], [645, 144], [677, 145], [646, 146], [647, 147], [648, 148], [649, 149], [650, 150], [651, 151], [652, 152], [653, 153], [654, 154], [655, 155], [656, 155], [657, 156], [658, 5], [659, 157], [661, 158], [660, 159], [662, 160], [663, 161], [664, 162], [665, 163], [666, 164], [667, 165], [668, 166], [669, 167], [670, 168], [671, 169], [672, 170], [673, 171], [674, 172], [675, 173], [676, 174], [810, 15], [1633, 15], [1111, 15], [48, 5], [50, 175], [51, 15], [679, 5], [1105, 176], [1093, 177], [1104, 178], [969, 179], [882, 180], [968, 181], [967, 182], [970, 183], [881, 184], [971, 185], [972, 186], [973, 187], [974, 188], [975, 188], [976, 188], [977, 187], [978, 188], [981, 189], [982, 190], [979, 5], [980, 191], [983, 192], [951, 193], [870, 194], [985, 195], [986, 196], [950, 197], [987, 198], [859, 5], [863, 199], [896, 200], [988, 5], [894, 5], [895, 5], [989, 201], [990, 202], [991, 203], [864, 204], [865, 205], [860, 5], [966, 206], [965, 207], [899, 208], [992, 209], [993, 209], [917, 5], [918, 210], [994, 211], [1007, 5], [1008, 5], [1094, 212], [1009, 213], [1010, 214], [883, 215], [884, 216], [885, 217], [886, 218], [995, 219], [997, 220], [998, 221], [999, 222], [1000, 221], [1006, 223], [996, 222], [1001, 222], [1002, 221], [1003, 222], [1004, 221], [1005, 222], [1011, 202], [1012, 202], [1013, 202], [1015, 224], [1014, 202], [1017, 225], [1018, 202], [1019, 226], [1032, 227], [1020, 225], [1021, 228], [1022, 225], [1023, 202], [1016, 202], [1024, 202], [1025, 229], [1026, 202], [1027, 225], [1028, 202], [1029, 202], [1030, 230], [1031, 202], [1034, 231], [1036, 232], [1037, 233], [1038, 234], [1039, 235], [1042, 236], [1043, 237], [1045, 238], [1046, 239], [1049, 240], [1050, 232], [1052, 241], [1053, 242], [1054, 243], [1041, 244], [1040, 245], [1044, 246], [929, 247], [1056, 248], [928, 249], [1048, 250], [1047, 251], [1057, 243], [1059, 252], [1058, 253], [1062, 254], [1063, 255], [1064, 256], [1065, 5], [1066, 257], [1067, 258], [1068, 259], [1069, 255], [1070, 255], [1071, 255], [1061, 260], [1072, 5], [1060, 261], [1073, 262], [1074, 263], [1075, 264], [904, 265], [905, 266], [962, 267], [924, 268], [906, 269], [907, 270], [908, 271], [909, 272], [910, 273], [911, 274], [912, 272], [914, 275], [913, 272], [915, 273], [916, 265], [921, 276], [920, 277], [922, 278], [923, 265], [933, 213], [891, 279], [872, 280], [871, 281], [873, 282], [867, 283], [926, 284], [877, 5], [887, 285], [1077, 286], [1078, 5], [862, 287], [868, 288], [889, 289], [866, 290], [964, 291], [888, 292], [874, 282], [1055, 282], [890, 293], [861, 294], [875, 295], [869, 296], [878, 297], [879, 297], [880, 297], [1076, 297], [1079, 298], [876, 182], [897, 182], [1080, 299], [1082, 196], [1033, 300], [1081, 301], [1035, 301], [952, 302], [1083, 300], [963, 303], [1051, 304], [925, 305], [1084, 306], [1085, 307], [984, 308], [927, 309], [955, 310], [893, 311], [892, 201], [1095, 5], [1096, 312], [919, 313], [1097, 314], [956, 315], [957, 316], [1098, 317], [937, 318], [958, 319], [959, 320], [1099, 321], [938, 5], [1100, 322], [1101, 5], [945, 323], [960, 324], [947, 5], [944, 325], [961, 326], [939, 5], [946, 327], [1102, 5], [948, 328], [940, 329], [942, 330], [943, 331], [941, 332], [953, 333], [1103, 334], [954, 335], [930, 336], [931, 336], [932, 337], [1086, 214], [1087, 338], [1088, 338], [900, 339], [901, 214], [935, 340], [936, 341], [934, 214], [898, 214], [1089, 214], [902, 5], [903, 342], [1091, 343], [1090, 214], [1092, 5], [949, 5], [1579, 344], [1560, 345], [1558, 346], [1559, 5], [1578, 347], [1557, 348], [1561, 349], [1564, 350], [1562, 351], [1554, 352], [1556, 353], [1563, 354], [1555, 353], [1553, 355], [1552, 5], [1576, 356], [1575, 348], [1565, 348], [1577, 357], [1574, 358], [1580, 359], [1566, 360], [1567, 358], [1573, 358], [1572, 358], [1571, 358], [1568, 358], [1570, 358], [1569, 358], [1581, 361], [140, 5], [586, 5], [1449, 362], [1448, 5], [1450, 363], [1447, 61], [55, 5], [49, 5], [270, 364], [249, 365], [346, 5], [250, 366], [186, 364], [187, 5], [188, 5], [189, 5], [190, 5], [191, 5], [192, 5], [193, 5], [194, 5], [195, 5], [196, 5], [197, 5], [198, 364], [199, 364], [200, 5], [201, 5], [202, 5], [203, 5], [204, 5], [205, 5], [206, 5], [207, 5], [208, 5], [209, 5], [210, 5], [211, 5], [212, 5], [213, 364], [214, 5], [215, 5], [216, 364], [217, 5], [218, 5], [219, 364], [220, 5], [221, 364], [222, 364], [223, 364], [224, 5], [225, 364], [226, 364], [227, 364], [228, 364], [229, 364], [230, 364], [231, 364], [232, 5], [233, 5], [234, 364], [235, 5], [236, 5], [237, 5], [238, 5], [239, 5], [240, 5], [241, 5], [242, 5], [243, 5], [244, 5], [245, 5], [246, 364], [247, 5], [248, 5], [251, 367], [252, 364], [253, 364], [254, 368], [255, 369], [256, 364], [257, 364], [258, 364], [259, 364], [260, 5], [261, 5], [262, 364], [184, 5], [263, 5], [264, 5], [265, 5], [266, 5], [267, 5], [268, 5], [269, 5], [271, 370], [272, 5], [273, 5], [274, 5], [275, 5], [276, 5], [277, 5], [278, 5], [279, 5], [280, 364], [281, 5], [282, 5], [283, 5], [284, 5], [285, 364], [286, 364], [287, 364], [288, 364], [289, 5], [290, 5], [291, 5], [292, 5], [439, 371], [293, 364], [294, 364], [295, 5], [296, 5], [297, 5], [298, 5], [299, 5], [300, 5], [301, 5], [302, 5], [303, 5], [304, 5], [305, 5], [306, 5], [307, 364], [308, 5], [309, 5], [310, 5], [311, 5], [312, 5], [313, 5], [314, 5], [315, 5], [316, 5], [317, 5], [318, 364], [319, 5], [320, 5], [321, 5], [322, 5], [323, 5], [324, 5], [325, 5], [326, 5], [327, 5], [328, 364], [329, 5], [330, 5], [331, 5], [332, 5], [333, 5], [334, 5], [335, 5], [336, 5], [337, 364], [338, 5], [339, 5], [340, 5], [341, 5], [342, 5], [343, 5], [344, 364], [345, 5], [347, 372], [183, 364], [348, 5], [349, 364], [350, 5], [351, 5], [352, 5], [353, 5], [354, 5], [355, 5], [356, 5], [357, 5], [358, 5], [359, 364], [360, 5], [361, 5], [362, 5], [363, 5], [364, 5], [365, 5], [366, 5], [371, 373], [369, 374], [368, 375], [370, 376], [367, 364], [372, 5], [373, 5], [374, 364], [375, 5], [376, 5], [377, 5], [378, 5], [379, 5], [380, 5], [381, 5], [382, 5], [383, 5], [384, 364], [385, 364], [386, 5], [387, 5], [388, 5], [389, 364], [390, 5], [391, 364], [392, 5], [393, 370], [394, 5], [395, 5], [396, 5], [397, 5], [398, 5], [399, 5], [400, 5], [401, 5], [402, 5], [403, 364], [404, 364], [405, 5], [406, 5], [407, 5], [408, 5], [409, 5], [410, 5], [411, 5], [412, 5], [413, 5], [414, 5], [415, 5], [416, 5], [417, 364], [418, 364], [419, 5], [420, 5], [421, 364], [422, 5], [423, 5], [424, 5], [425, 5], [426, 5], [427, 5], [428, 5], [429, 5], [430, 5], [431, 5], [432, 5], [433, 5], [434, 364], [185, 377], [435, 5], [436, 5], [437, 5], [438, 5], [1472, 5], [715, 378], [716, 379], [1462, 5], [99, 380], [98, 381], [95, 5], [96, 382], [97, 383], [150, 5], [69, 384], [68, 385], [64, 386], [67, 387], [65, 388], [66, 388], [90, 389], [88, 384], [89, 384], [87, 384], [86, 390], [91, 391], [63, 392], [61, 393], [59, 394], [60, 395], [62, 394], [85, 396], [74, 397], [78, 398], [84, 397], [80, 397], [73, 397], [83, 397], [72, 398], [79, 398], [71, 5], [76, 397], [81, 397], [75, 397], [77, 397], [82, 397], [554, 5], [105, 15], [1421, 61], [1422, 61], [1424, 399], [1423, 400], [714, 401], [713, 5], [100, 402], [94, 5], [103, 403], [102, 404], [101, 405], [2658, 406], [2710, 406], [562, 406], [560, 406], [2769, 406], [2774, 406], [557, 15], [558, 15], [556, 5], [559, 407], [2657, 406], [2316, 406], [694, 15], [2326, 408], [2325, 5], [2327, 408], [2328, 409], [155, 410], [58, 411], [54, 412], [57, 413], [53, 414], [56, 5], [52, 15], [2660, 415], [2659, 15], [70, 15], [778, 416], [779, 416], [799, 416], [798, 416], [780, 416], [781, 416], [782, 416], [783, 416], [784, 416], [785, 416], [786, 416], [787, 416], [788, 416], [789, 416], [797, 416], [790, 416], [791, 416], [792, 416], [793, 416], [794, 416], [795, 416], [796, 416], [800, 417], [801, 418], [1125, 5], [512, 419], [514, 420], [504, 421], [509, 422], [510, 423], [516, 424], [511, 425], [508, 426], [507, 427], [506, 428], [517, 429], [474, 422], [475, 422], [515, 422], [533, 430], [543, 431], [537, 431], [545, 431], [549, 431], [536, 431], [538, 431], [541, 431], [544, 431], [540, 432], [542, 431], [546, 15], [539, 422], [535, 433], [534, 434], [483, 15], [487, 15], [477, 422], [480, 15], [485, 422], [486, 435], [479, 436], [482, 15], [484, 15], [481, 437], [470, 15], [469, 15], [551, 438], [548, 439], [501, 440], [500, 422], [498, 15], [499, 422], [502, 441], [503, 442], [496, 15], [492, 443], [495, 422], [494, 422], [493, 422], [488, 422], [497, 443], [547, 422], [513, 444], [532, 445], [550, 5], [505, 5], [531, 446], [478, 5], [476, 447], [156, 448], [157, 449], [158, 450], [159, 451], [160, 452], [175, 453], [161, 454], [162, 455], [163, 456], [164, 457], [165, 458], [166, 459], [167, 460], [168, 461], [169, 462], [170, 463], [171, 464], [172, 465], [173, 466], [174, 467], [152, 468], [149, 5], [151, 5], [1467, 5], [683, 469], [573, 5], [571, 470], [574, 470], [575, 471], [577, 472], [572, 473], [579, 474], [684, 475], [566, 476], [576, 476], [678, 477], [680, 478], [567, 15], [682, 479], [564, 480], [565, 481], [563, 471], [570, 482], [568, 5], [569, 5], [578, 470], [681, 471], [2699, 483], [2698, 484], [2671, 5], [2672, 485], [2673, 485], [2679, 5], [2674, 5], [2678, 5], [2675, 5], [2676, 5], [2677, 5], [2691, 5], [2692, 5], [2680, 485], [2681, 5], [2700, 486], [2682, 485], [2695, 5], [2683, 487], [2684, 487], [2685, 487], [2686, 5], [2697, 488], [2687, 487], [2688, 485], [2689, 5], [2690, 485], [2670, 489], [2696, 490], [2693, 491], [2694, 492], [1474, 61], [1174, 61], [1175, 61], [1177, 493], [1176, 61], [1202, 494], [1222, 495], [1219, 495], [1216, 496], [1212, 5], [1214, 496], [1223, 496], [1221, 495], [1217, 496], [1218, 5], [1220, 495], [1215, 61], [1213, 496], [1282, 497], [1281, 61], [1283, 498], [1284, 5], [1404, 61], [1402, 61], [1403, 61], [1401, 61], [1405, 61], [1339, 61], [1340, 61], [1338, 61], [1336, 61], [1337, 61], [1341, 61], [1173, 61], [1169, 61], [1168, 61], [1165, 61], [1170, 61], [1172, 61], [1167, 61], [1171, 61], [1166, 61], [1276, 61], [1274, 61], [1277, 61], [1186, 61], [1273, 499], [1272, 61], [1275, 61], [1278, 61], [1280, 500], [1393, 61], [1396, 61], [1394, 61], [1398, 61], [1397, 61], [1395, 61], [1407, 501], [1331, 61], [1332, 61], [1333, 61], [1334, 502], [1406, 5], [1267, 503], [1400, 61], [1399, 5], [1392, 504], [1387, 505], [1388, 61], [1391, 506], [1386, 61], [1389, 506], [1390, 505], [1371, 61], [1360, 61], [1373, 61], [1357, 61], [1349, 61], [1367, 61], [1350, 61], [1364, 61], [1264, 61], [1359, 61], [1342, 61], [1279, 61], [1366, 61], [1266, 507], [1378, 508], [1351, 509], [1265, 61], [1376, 61], [1369, 61], [1363, 61], [1344, 61], [1384, 61], [1354, 61], [1375, 61], [1358, 61], [1374, 61], [1347, 61], [1345, 510], [1372, 511], [1383, 61], [1379, 61], [1385, 61], [1380, 61], [1365, 61], [1356, 61], [1381, 61], [1346, 61], [1370, 61], [1368, 61], [1343, 61], [1355, 61], [1377, 61], [1382, 61], [1353, 61], [1352, 512], [1362, 61], [1348, 61], [1361, 61], [1207, 61], [1208, 61], [1203, 61], [1209, 5], [1211, 61], [1204, 61], [1206, 61], [1210, 513], [1205, 5], [1143, 61], [1145, 61], [1146, 61], [1151, 61], [1142, 61], [1147, 61], [1144, 61], [1155, 61], [1148, 61], [1149, 5], [1154, 61], [1152, 514], [1153, 510], [1150, 5], [1161, 61], [1163, 61], [1162, 61], [1164, 61], [1178, 61], [1192, 61], [1183, 61], [1187, 515], [1185, 61], [1180, 516], [1189, 61], [1188, 517], [1181, 516], [1182, 61], [1190, 61], [1184, 61], [1191, 516], [1335, 61], [1240, 518], [1245, 519], [1256, 520], [1238, 518], [1228, 518], [1242, 518], [1249, 521], [1247, 518], [1234, 522], [1230, 523], [1231, 518], [1227, 524], [1246, 518], [1235, 518], [1224, 61], [1253, 518], [1254, 518], [1243, 518], [1232, 518], [1251, 518], [1236, 518], [1250, 525], [1237, 518], [1226, 526], [1252, 527], [1239, 518], [1241, 518], [1257, 518], [1156, 61], [1157, 61], [1158, 61], [1159, 61], [1285, 528], [1244, 528], [1286, 529], [1287, 528], [1288, 5], [1289, 528], [1201, 61], [1290, 5], [1291, 61], [1292, 61], [1255, 528], [1293, 528], [1294, 5], [1295, 528], [1229, 5], [1248, 61], [1296, 61], [1233, 5], [1297, 5], [1298, 61], [1299, 5], [1300, 528], [1301, 61], [1302, 5], [1303, 528], [1304, 5], [1305, 5], [1306, 5], [1307, 61], [1308, 5], [1309, 5], [1310, 61], [1311, 5], [1312, 5], [1313, 5], [1314, 528], [1315, 61], [1316, 61], [1317, 61], [1318, 5], [1319, 61], [1320, 5], [1321, 5], [1322, 5], [1323, 61], [1324, 61], [1325, 5], [1326, 528], [1327, 5], [1328, 5], [1329, 61], [1330, 5], [1225, 61], [1160, 5], [1179, 5], [1199, 61], [1200, 61], [1195, 61], [1196, 61], [1193, 61], [1198, 61], [1197, 61], [1194, 61], [1258, 503], [1260, 530], [1261, 61], [1262, 61], [1263, 61], [1268, 531], [1269, 503], [1259, 61], [1271, 532], [1270, 533], [46, 5], [47, 5], [8, 5], [9, 5], [11, 5], [10, 5], [2, 5], [12, 5], [13, 5], [14, 5], [15, 5], [16, 5], [17, 5], [18, 5], [19, 5], [3, 5], [20, 5], [21, 5], [4, 5], [22, 5], [26, 5], [23, 5], [24, 5], [25, 5], [27, 5], [28, 5], [29, 5], [5, 5], [30, 5], [31, 5], [32, 5], [33, 5], [6, 5], [37, 5], [34, 5], [35, 5], [36, 5], [38, 5], [7, 5], [39, 5], [44, 5], [45, 5], [40, 5], [41, 5], [42, 5], [43, 5], [1, 5], [602, 534], [612, 535], [601, 534], [622, 536], [593, 537], [592, 538], [621, 539], [615, 540], [620, 541], [595, 542], [609, 543], [594, 544], [618, 545], [590, 546], [589, 539], [619, 547], [591, 548], [596, 549], [597, 5], [600, 549], [587, 5], [623, 550], [613, 551], [604, 552], [605, 553], [607, 554], [603, 555], [606, 556], [616, 539], [598, 557], [599, 558], [608, 559], [588, 560], [611, 551], [610, 549], [614, 5], [617, 561], [1106, 5], [1109, 5], [1110, 562], [1107, 563], [1108, 564], [473, 565], [491, 566], [817, 567], [813, 568], [812, 5], [814, 569], [815, 5], [816, 570], [836, 571], [825, 572], [827, 573], [834, 574], [829, 5], [830, 5], [828, 575], [831, 576], [823, 5], [824, 5], [835, 577], [826, 578], [832, 5], [833, 579], [1114, 580], [1113, 581], [1115, 581], [1112, 5], [804, 582], [819, 583], [837, 584], [838, 584], [839, 584], [840, 584], [820, 585], [821, 97], [822, 586], [442, 587], [448, 588], [177, 589], [178, 590], [182, 591], [440, 592], [441, 593], [447, 594], [444, 595], [445, 595], [180, 587], [449, 596], [1586, 597], [1587, 598], [841, 599], [842, 600], [461, 103], [843, 601], [844, 600], [845, 600], [846, 103], [142, 602], [555, 603], [847, 97], [848, 600], [687, 604], [849, 601], [709, 605], [850, 97], [1588, 606], [1589, 606], [851, 600], [852, 607], [689, 608], [853, 609], [465, 103], [854, 600], [856, 610], [855, 600], [857, 600], [553, 611], [858, 612], [698, 613], [1590, 601], [1591, 601], [1592, 601], [700, 614], [699, 606], [732, 615], [453, 616], [1596, 617], [144, 103], [1598, 618], [1597, 600], [145, 97], [695, 103], [1600, 619], [1601, 97], [1602, 97], [686, 620], [146, 103], [1603, 621], [1604, 600], [1605, 97], [697, 622], [1606, 600], [1607, 600], [104, 103], [108, 623], [147, 624], [1608, 625], [451, 626], [1609, 627], [1610, 601], [1611, 628], [462, 629], [463, 630], [467, 631], [1612, 601], [459, 632], [1613, 606], [1614, 600], [1615, 633], [457, 634], [1616, 600], [109, 635], [1620, 636], [1621, 600], [143, 637], [107, 103], [452, 638], [1622, 97], [464, 639], [456, 640], [1623, 633], [454, 641], [1624, 606], [1626, 642], [1627, 601], [1628, 600], [1629, 643], [466, 644], [1630, 601], [458, 628], [1631, 600], [1625, 633], [1632, 601], [1634, 645], [1635, 97], [1636, 600], [455, 103], [1637, 600], [2320, 646], [2321, 600], [2322, 97], [2317, 647], [2324, 648], [2329, 649], [2662, 650], [2663, 600], [2661, 651], [2664, 652], [2665, 653], [2666, 654], [2655, 655], [2656, 656], [2667, 606], [2668, 103], [2669, 606], [2701, 657], [1618, 658], [2702, 606], [1617, 659], [106, 660], [2703, 661], [2704, 662], [1619, 661], [2706, 663], [718, 664], [720, 665], [711, 666], [725, 667], [710, 668], [722, 669], [712, 670], [724, 671], [723, 672], [726, 669], [2707, 669], [774, 673], [2708, 674], [685, 675], [2709, 676], [2711, 677], [2712, 600], [2713, 601], [2714, 600], [2715, 601], [2716, 600], [2720, 678], [2721, 601], [2722, 601], [2717, 600], [2723, 600], [2724, 600], [2725, 600], [2718, 679], [2726, 600], [2727, 680], [2729, 681], [2730, 682], [2731, 601], [2732, 601], [2719, 683], [2733, 684], [2734, 685], [2735, 686], [2318, 103], [708, 687], [802, 688], [2736, 689], [703, 606], [2737, 661], [727, 690], [561, 691], [701, 692], [704, 606], [707, 693], [2739, 694], [2742, 695], [2740, 696], [2743, 606], [2741, 697], [2744, 698], [2319, 699], [2745, 700], [2746, 701], [2747, 702], [2748, 703], [2750, 704], [2751, 600], [2749, 696], [2752, 696], [139, 97], [2753, 97], [2738, 705], [1595, 706], [2755, 707], [1599, 103], [2756, 103], [2757, 97], [2705, 708], [809, 709], [702, 710], [691, 711], [805, 97], [806, 97], [807, 97], [808, 97], [811, 712], [2758, 713], [1593, 714], [1594, 714], [148, 715], [2759, 716], [2728, 717], [460, 97], [92, 97], [2323, 97], [176, 97], [181, 97], [688, 97], [446, 97], [443, 97], [179, 97], [141, 714], [717, 97], [2754, 583], [93, 97], [2760, 103], [450, 97], [719, 97], [2761, 601], [803, 97], [690, 718], [2762, 601], [2763, 97], [731, 719], [705, 720], [777, 721], [552, 722], [776, 723], [728, 724], [2764, 97], [468, 725], [2765, 726], [2766, 727], [2767, 728], [721, 729], [775, 730], [2768, 97], [730, 731], [706, 732], [729, 733], [2770, 734], [696, 735], [693, 736], [2771, 663], [2772, 737], [2773, 738], [2775, 739], [818, 740]], "affectedFilesPendingEmit": [[804, 17], [819, 17], [837, 17], [838, 17], [839, 17], [840, 17], [820, 17], [821, 17], [822, 17], [442, 17], [448, 17], [177, 17], [178, 17], [182, 17], [440, 17], [441, 17], [447, 17], [444, 17], [445, 17], [180, 17], [449, 17], [1586, 17], [1587, 17], [841, 17], [842, 17], [461, 17], [843, 17], [844, 17], [845, 17], [846, 17], [142, 17], [555, 17], [847, 17], [848, 17], [687, 17], [849, 17], [709, 17], [850, 17], [1588, 17], [1589, 17], [851, 17], [852, 17], [689, 17], [853, 17], [465, 17], [854, 17], [856, 17], [855, 17], [857, 17], [553, 17], [858, 17], [698, 17], [1590, 17], [1591, 17], [1592, 17], [700, 17], [699, 17], [732, 17], [453, 17], [1596, 17], [144, 17], [1598, 17], [1597, 17], [145, 17], [695, 17], [1600, 17], [1601, 17], [1602, 17], [686, 17], [146, 17], [1603, 17], [1604, 17], [1605, 17], [697, 17], [1606, 17], [1607, 17], [104, 17], [108, 17], [147, 17], [1608, 17], [451, 17], [1609, 17], [1610, 17], [1611, 17], [462, 17], [463, 17], [467, 17], [1612, 17], [459, 17], [1613, 17], [1614, 17], [1615, 17], [457, 17], [1616, 17], [109, 17], [1620, 17], [1621, 17], [143, 17], [107, 17], [452, 17], [1622, 17], [464, 17], [456, 17], [1623, 17], [454, 17], [1624, 17], [1626, 17], [1627, 17], [1628, 17], [1629, 17], [466, 17], [1630, 17], [458, 17], [1631, 17], [1625, 17], [1632, 17], [1634, 17], [1635, 17], [1636, 17], [455, 17], [1637, 17], [2320, 17], [2321, 17], [2322, 17], [2317, 17], [2324, 17], [2329, 17], [2662, 17], [2663, 17], [2661, 17], [2664, 17], [2665, 17], [2666, 17], [2655, 17], [2656, 17], [2667, 17], [2668, 17], [2669, 17], [2701, 17], [1618, 17], [2702, 17], [1617, 17], [106, 17], [2703, 17], [2704, 17], [1619, 17], [2706, 17], [718, 17], [720, 17], [711, 17], [725, 17], [710, 17], [722, 17], [712, 17], [724, 17], [723, 17], [726, 17], [2707, 17], [774, 17], [2708, 17], [685, 17], [2709, 17], [2711, 17], [2712, 17], [2713, 17], [2714, 17], [2715, 17], [2716, 17], [2720, 17], [2721, 17], [2722, 17], [2717, 17], [2723, 17], [2724, 17], [2725, 17], [2718, 17], [2726, 17], [2727, 17], [2729, 17], [2730, 17], [2731, 17], [2732, 17], [2719, 17], [2733, 17], [2734, 17], [2735, 17], [2318, 17], [708, 17], [802, 17], [2736, 17], [703, 17], [2737, 17], [727, 17], [561, 17], [701, 17], [704, 17], [707, 17], [2739, 17], [2742, 17], [2740, 17], [2743, 17], [2741, 17], [2744, 17], [2319, 17], [2745, 17], [2746, 17], [2747, 17], [2748, 17], [2750, 17], [2751, 17], [2749, 17], [2752, 17], [139, 17], [2753, 17], [2738, 17], [1595, 17], [2755, 17], [1599, 17], [2756, 17], [2757, 17], [2705, 17], [809, 17], [702, 17], [691, 17], [811, 17], [2758, 17], [1593, 17], [1594, 17], [148, 17], [2759, 17], [2728, 17], [460, 17], [92, 17], [2323, 17], [176, 17], [181, 17], [688, 17], [446, 17], [443, 17], [179, 17], [141, 17], [717, 17], [2754, 17], [93, 17], [2760, 17], [450, 17], [719, 17], [2761, 17], [803, 17], [690, 17], [2762, 17], [2763, 17], [731, 17], [705, 17], [777, 17], [552, 17], [776, 17], [728, 17], [2764, 17], [468, 17], [2765, 17], [2766, 17], [2767, 17], [721, 17], [775, 17], [2768, 17], [730, 17], [706, 17], [729, 17], [2770, 17], [696, 17], [693, 17], [2771, 17], [2772, 17], [2773, 17], [2775, 17]], "emitSignatures": [92, 93, 104, 106, 107, 108, 109, 139, 141, 142, 143, 144, 145, 146, 147, 148, 176, 177, 178, 179, 180, 181, 182, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 552, 553, 555, 561, 685, 686, 687, 688, 689, 690, 691, 693, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 774, 775, 776, 777, 802, 803, 804, 809, 811, 819, 820, 821, 822, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, 1595, 1596, 1597, 1598, 1599, 1600, 1601, 1602, 1603, 1604, 1605, 1606, 1607, 1608, 1609, 1610, 1611, 1612, 1613, 1614, 1615, 1616, 1617, 1618, 1619, 1620, 1621, 1622, 1623, 1624, 1625, 1626, 1627, 1628, 1629, 1630, 1631, 1632, 1634, 1635, 1636, 1637, 2317, 2318, 2319, 2320, 2321, 2322, 2323, 2324, 2329, 2655, 2656, 2661, 2662, 2663, 2664, 2665, 2666, 2667, 2668, 2669, 2701, 2702, 2703, 2704, 2705, 2706, 2707, 2708, 2709, 2711, 2712, 2713, 2714, 2715, 2716, 2717, 2718, 2719, 2720, 2721, 2722, 2723, 2724, 2725, 2726, 2727, 2728, 2729, 2730, 2731, 2732, 2733, 2734, 2735, 2736, 2737, 2738, 2739, 2740, 2741, 2742, 2743, 2744, 2745, 2746, 2747, 2748, 2749, 2750, 2751, 2752, 2753, 2754, 2755, 2756, 2757, 2758, 2759, 2760, 2761, 2762, 2763, 2764, 2765, 2766, 2767, 2768, 2770, 2771, 2772, 2773, 2775], "version": "5.7.3"}